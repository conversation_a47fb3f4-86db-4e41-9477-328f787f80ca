// include/environment/maps/igridmap.h
#pragma once

#include "core/types.h"
#include "params/parameters.h"
#include <utility>
#include <string>
#include <optional>   // 用于返回可能不存在的值
#include <vector>     // 用于图层名称列表
#include <ctime>      // 用于时间戳
#include <utility>    // 用于 std::pair
#include <nlohmann/json.hpp>

namespace NSDrones {
	namespace NSEnvironment {
		using namespace NSDrones::NSUtils; // 引入工具命名空间，可能包含坐标转换等
		using namespace NSDrones::NSCore;   // 引入核心类型命名空间，如 Vector3D, WGS84Point 等

		/**
		 * @brief 地图元数据结构
		 */
		struct MapMetadata {
			// 基本地图信息
			std::string file_path;           		///< 源文件路径
			std::string file_format;                ///< 文件格式（tif、png 等）
			std::string map_id;                     ///< 地图唯一标识符
			std::string map_name;                   ///< 地图名称

			// 坐标系统信息（从 GeoTIFF 文件中提取）
			///< GeoTIFF 文件的四个角点坐标，顺序为：左上（为局部坐标系坐标原点）-右上-右下-左下
			std::array<WGS84Point, 4> wgs84_corners;
			std::pair<double,double> resolution;    ///< 分辨率（米/像素）(X, Y)
			std::pair<int, int> grid_size;          ///< 网格大小 (width, height)

			// 最高最低点坐标
			WGS84Point min_elevation_point; ///< 最低高程点的 WGS84 坐标
			WGS84Point max_elevation_point; ///< 最高高程点的 WGS84 坐标

			// 边界信息
			WGS84BoundingBox wgs84_bounds;  ///< WGS84 边界框
			BoundingBox world_bounds;       ///< 世界坐标边界框

			// 图层信息（GridMap 中的标准图层）
			std::vector<std::string> available_layers;      			///< 可用的标准图层名称列表
			inline static const std::string elevation_layer = "elevation";      		///< 高程图层的标准名称
			inline static const std::string feature_layer = "feature";          		///< 地物类型图层的标准名称
			inline static const std::string feature_height_layer = "feature_height"; 	///< 地物高度图层的标准名称

			/**
			 * @brief 默认构造函数
			 */
			MapMetadata() : resolution(0.0, 0.0), grid_size(0, 0) {}

			/**
			 * @brief 检查元数据是否有效
			 */
			bool isValid() const {
				return !file_path.empty() && resolution.first > 0.0 && resolution.second > 0.0 &&
					   grid_size.first > 0 && grid_size.second > 0 &&
					   !available_layers.empty(); // 至少要有一个可用图层
			}

			/**
			 * @brief 转换为字符串表示
			 */
			std::string toString() const {
				std::string result = fmt::format(
					"MapMetadata[id='{}', file='{}', format='{}', resolution=({:.3f},{:.3f}), size={}x{}, layers={}",
					map_id, file_path, file_format, resolution.first, resolution.second,
					grid_size.first, grid_size.second, available_layers.size());

				result += ", corners=4";
				result += "]";
				return result;
			}
		};

		/**
		 * @class IGridMap
		 * @brief 网格地图数据源抽象接口
		 *
		 * 该接口定义了地图数据访问的标准方法，支持地形高程、地物类型和地物高度查询。
		 * 所有坐标参数和返回值均使用WGS84坐标系统，距离和高度单位为米。
		 *
		 * 设计原则：
		 * - 统一的WGS84坐标接口，内部坐标转换由实现类处理
		 * - 支持多种地图数据格式（GeoTIFF、PNG等）
		 * - 提供完整的元数据信息和覆盖范围检查
		 * - 线程安全的数据访问接口
		 *
		 * @note 实现类需要确保线程安全性
		 * @note 所有查询方法在数据不可用时返回std::nullopt
		 */
		class IGridMap {
		public:
			/**
			 * @brief 虚析构函数
			 * 确保通过基类指针删除派生类对象时正确调用派生类析构函数
			 */
			virtual ~IGridMap() = default;

			// === 生命周期管理接口 ===

			/**
			 * @brief 初始化地图数据源
			 * @param global_params 全局参数对象，包含地图配置信息
			 * @return 初始化成功返回true，失败返回false
			 * @note 初始化失败时应记录详细错误日志
			 * @note 重复调用应该是安全的
			 */
			virtual bool initialize(std::shared_ptr<NSParams::ParamValues> global_params) = 0;

			// === 地理数据查询接口 ===

			/**
			 * @brief 获取指定位置的地形高程（海拔高度）
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 海拔高度（米），查询失败或超出范围时返回std::nullopt
			 * @note 返回的高程是相对于WGS84椭球面的绝对高度
			 * @note 线程安全，可并发调用
			 */
			virtual std::optional<double> getElevation(double latitude, double longitude) const = 0;

			/**
			 * @brief 获取指定位置的地表覆盖特征类型
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 地物类型枚举值，查询失败或超出范围时返回std::nullopt
			 * @note 地物类型包括：建筑物、植被、水体、道路等
			 * @note 线程安全，可并发调用
			 */
			virtual std::optional<FeatureType> getFeature(double latitude, double longitude) const = 0;

			/**
			 * @brief 获取指定位置的地表特征高度
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 特征高度（米），查询失败或超出范围时返回std::nullopt
			 * @note 特征高度是相对于地面的高度（如建筑物高度、树木高度）
			 * @note 线程安全，可并发调用
			 */
			virtual std::optional<double> getFeatureHeight(double latitude, double longitude) const = 0;

			// === 状态查询接口 ===

			/**
			 * @brief 检查地图数据源是否已成功初始化
			 * @return 已初始化且可提供数据返回true，否则返回false
			 * @note 线程安全，可并发调用
			 */
			virtual bool isInitialized() const = 0;

			/**
			 * @brief 检查指定坐标是否在地图数据覆盖范围内
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 坐标在覆盖范围内且有有效数据返回true，否则返回false
			 * @note 此方法检查实际数据可用性，不仅仅是边界框
			 * @note 线程安全，可并发调用
			 */
			virtual bool isCovered(double latitude, double longitude) const = 0;

			// === 元数据访问接口 ===

			/**
			 * @brief 获取地图元数据信息
			 * @return 地图元数据结构，包含分辨率、边界、图层等信息
			 * @note 对于瓦片地图，返回汇总的元数据信息
			 * @note 线程安全，可并发调用
			 */
			virtual MapMetadata getMetadata() const { return metadata_; }

			/**
			 * @brief 获取所有瓦片的详细元数据列表
			 * @return 瓦片元数据列表，单个地图返回包含自身的单元素向量
			 * @note 对于TiledGridMap，返回每个瓦片的详细信息
			 * @note 线程安全，可并发调用
			 */
			virtual std::vector<MapMetadata> getTileMetadataList() const {
				return {getMetadata()};  // 默认实现：返回自身元数据
			}

		protected:
			// === 成员变量 ===
			MapMetadata metadata_;  ///< 地图元数据信息

			// === 图层检测辅助方法 ===

			/**
			 * @brief 智能检测地图图层名称
			 * @param available_layers 数据源中可用的图层名称列表
			 * @param global_params 全局参数对象，包含图层配置信息
			 * @param elevation_layer [输出] 检测到的高程图层名称
			 * @param feature_layer [输出] 检测到的地物图层名称（可选）
			 * @param feature_height_layer [输出] 检测到的地物高度图层名称（可选）
			 * @return 检测成功返回true（至少找到高程图层），否则返回false
			 *
			 * @details 检测策略：
			 * 1. 优先使用配置文件中指定的图层名称
			 * 2. 配置不存在时，使用预定义的候选名称进行模糊匹配
			 * 3. 高程图层是必需的，地物和地物高度图层是可选的
			 *
			 * @note 静态方法，可在实现类中直接调用
			 */
			static bool detectLayers(
				const std::vector<std::string>& available_layers,
				std::shared_ptr<NSParams::ParamValues> global_params,
				std::string& elevation_layer,
				std::string& feature_layer,
				std::string& feature_height_layer
			);

			/**
			 * @brief 从候选名称列表中匹配可用图层
			 * @param candidates 候选图层名称列表（按优先级排序）
			 * @param available_layers 数据源中实际可用的图层名称列表
			 * @return 匹配到的图层名称，未找到时返回空字符串
			 *
			 * @details 匹配策略：
			 * 1. 精确匹配：候选名称与可用名称完全相同
			 * 2. 包含匹配：可用名称包含候选名称（不区分大小写）
			 * 3. 返回第一个匹配的结果
			 *
			 * @note 静态方法，用于图层名称的模糊匹配
			 */
			static std::string detectLayerFromCandidates(
				const std::vector<std::string>& candidates,
				const std::vector<std::string>& available_layers
			);

		};

	} // namespace NSEnvironment
} // namespace NSDrones
