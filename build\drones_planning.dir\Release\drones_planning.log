﻿  task_allocator.cpp
  energy_evaluator.cpp
  ipath_planner.cpp
  rrtstar_planner.cpp
  trajectory_optimizer.cpp
  config.cpp
  base_object.cpp
  entity_object.cpp
  movement_strategy.cpp
  collision_engine.cpp
  coordinate_manager.cpp
  task_space.cpp
  obstacle.cpp
  zone.cpp
  environment.cpp
  main.cpp
  itask_planner.cpp
  mission_planner.cpp
  followpath_taskplanner.cpp
  loiterpoint_taskplanner.cpp
  正在生成代码...
  正在编译...
  scanarea_taskplanner.cpp
  surveycylinder_taskplanner.cpp
  surveymultipoints_taskplanner.cpp
  surveysphere_taskplanner.cpp
  fixedwing_dynamics.cpp
  multirotor_dynamics.cpp
  vtol_dynamics.cpp
  fixedwing_energies.cpp
  multirotor_energies.cpp
  vtol_energies.cpp
  flight_strategy.cpp
  idynamic_model.cpp
  ienergy_model.cpp
  uav.cpp
  uav_config.cpp
  正在生成代码...
    正在创建库 E:/source/dronesplanning/build/Release/drones_planning.lib 和对象 E:/source/dronesplanning/build/Release/drones_planning.exp
  drones_planning.vcxproj -> E:\source\dronesplanning\build\Release\drones_planning.exe
  拷贝 data 目录
