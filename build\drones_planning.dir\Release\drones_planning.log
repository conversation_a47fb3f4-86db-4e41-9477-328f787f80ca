﻿  single_gridmap.cpp
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(256,38): error C2653: “GeometryManager”: 不是类或命名空间名称
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(256,55): error C3861: “calculateMetersPerDegreeLongitude”: 找不到标识符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(257,37): error C2653: “GeometryManager”: 不是类或命名空间名称
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(257,54): error C3861: “calculateMetersPerDegreeLatitude”: 找不到标识符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(472,27): error C2039: "wgs84ToLocal": 不是 "NSDrones::NSEnvironment::SingleGridMap" 的成员
E:\source\dronesplanning\include\environment/maps/single_gridmap.h(48,9): message : 参见“NSDrones::NSEnvironment::SingleGridMap”的声明
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(472,77): error C2270: “wgs84ToLocal”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(473,37): error C2065: “coordinate_mutex_”: 未声明的标识符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(475,9): error C2065: “local_cartesian_”: 未声明的标识符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(481,4): error C2065: “local_cartesian_”: 未声明的标识符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(486,29): error C2039: "localToWgs84": 不是 "NSDrones::NSEnvironment::SingleGridMap" 的成员
E:\source\dronesplanning\include\environment/maps/single_gridmap.h(48,9): message : 参见“NSDrones::NSEnvironment::SingleGridMap”的声明
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(486,77): error C2270: “localToWgs84”: 非成员函数上不允许修饰符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(487,37): error C2065: “coordinate_mutex_”: 未声明的标识符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(489,9): error C2065: “local_cartesian_”: 未声明的标识符
E:\source\dronesplanning\src\environment\maps\single_gridmap.cpp(495,4): error C2065: “local_cartesian_”: 未声明的标识符
  main.cpp
E:\source\dronesplanning\src\main.cpp(394,82): error C2248: “NSDrones::NSEnvironment::SingleGridMap::loadFromPng”: 无法访问 private 成员(在“NSDrones::NSEnvironment::SingleGridMap”类中声明)
E:\source\dronesplanning\include\environment/maps/single_gridmap.h(259,9): message : 参见“NSDrones::NSEnvironment::SingleGridMap::loadFromPng”的声明
E:\source\dronesplanning\include\environment/maps/single_gridmap.h(48,9): message : 参见“NSDrones::NSEnvironment::SingleGridMap”的声明
E:\source\dronesplanning\src\main.cpp(468,52): error C2248: “NSDrones::NSEnvironment::SingleGridMap::loadFromPng”: 无法访问 private 成员(在“NSDrones::NSEnvironment::SingleGridMap”类中声明)
E:\source\dronesplanning\include\environment/maps/single_gridmap.h(259,9): message : 参见“NSDrones::NSEnvironment::SingleGridMap::loadFromPng”的声明
E:\source\dronesplanning\include\environment/maps/single_gridmap.h(48,9): message : 参见“NSDrones::NSEnvironment::SingleGridMap”的声明
  正在生成代码...
