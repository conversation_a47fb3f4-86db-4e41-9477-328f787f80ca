﻿  task_allocator.cpp
  energy_evaluator.cpp
  ipath_planner.cpp
  config.cpp
E:\source\dronesplanning\include\planning/mission_planner.h(165,12): error C2039: "TrajectoryCost": 不是 "NSDrones::NSCore" 的成员
E:\source\dronesplanning\include\uav/energies/vtol_energies.h(8,12): message : 参见“NSDrones::NSCore”的声明
  environment.cpp
  main.cpp
E:\source\dronesplanning\include\planning/mission_planner.h(165,12): error C2039: "TrajectoryCost": 不是 "NSDrones::NSCore" 的成员
E:\source\dronesplanning\include\uav/energies/vtol_energies.h(8,12): message : 参见“NSDrones::NSCore”的声明
  mission_planner.cpp
E:\source\dronesplanning\include\planning/mission_planner.h(165,12): error C2039: "TrajectoryCost": 不是 "NSDrones::NSCore" 的成员
E:\source\dronesplanning\include\core/movement_strategy.h(15,12): message : 参见“NSDrones::NSCore”的声明
E:\source\dronesplanning\src\planning\mission_planner.cpp(196,45): error C2065: “task_allocator_”: 未声明的标识符
E:\source\dronesplanning\src\planning\mission_planner.cpp(356,9): error C2065: “trajectory_evaluator_”: 未声明的标识符
E:\source\dronesplanning\src\planning\mission_planner.cpp(549,8): error C2065: “trajectory_evaluator_”: 未声明的标识符
E:\source\dronesplanning\src\planning\mission_planner.cpp(582,35): error C2065: “trajectory_evaluator_”: 未声明的标识符
E:\source\dronesplanning\src\planning\mission_planner.cpp(609,14): error C2065: “trajectory_evaluator_”: 未声明的标识符
E:\source\dronesplanning\src\planning\mission_planner.cpp(642,9): error C2065: “trajectory_evaluator_”: 未声明的标识符
E:\source\dronesplanning\src\planning\mission_planner.cpp(678,33): error C2065: “trajectory_evaluator_”: 未声明的标识符
  followpath_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(31,18): error C2661: “NSDrones::NSPlanning::ITaskPlanner::ITaskPlanner”: 没有重载函数接受 2 个参数
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(31,18): message : 尝试匹配参数列表“(NSDrones::NSAlgorithm::IPathPlannerPtr, NSDrones::NSAlgorithm::ITrajectoryOptimizerPtr)”时
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(39,25): error C2065: “path_planner_”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(107,25): error C2065: “path_planner_”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\followpath_taskplanner.cpp(196,25): error C2065: “path_planner_”: 未声明的标识符
  loiterpoint_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(32,18): error C2661: “NSDrones::NSPlanning::ITaskPlanner::ITaskPlanner”: 没有重载函数接受 2 个参数
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(32,18): message : 尝试匹配参数列表“(NSDrones::NSAlgorithm::IPathPlannerPtr, NSDrones::NSAlgorithm::ITrajectoryOptimizerPtr)”时
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(40,25): error C2065: “path_planner_”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(119,25): error C2065: “path_planner_”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\loiterpoint_taskplanner.cpp(205,24): error C2065: “path_planner_”: 未声明的标识符
  scanarea_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(29,18): error C2661: “NSDrones::NSPlanning::ITaskPlanner::ITaskPlanner”: 没有重载函数接受 2 个参数
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(29,18): message : 尝试匹配参数列表“(NSDrones::NSAlgorithm::IPathPlannerPtr, NSDrones::NSAlgorithm::ITrajectoryOptimizerPtr)”时
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(125,25): error C2065: “path_planner_”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(229,24): error C2065: “path_planner_”: 未声明的标识符
  surveycylinder_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(30,18): error C2661: “NSDrones::NSPlanning::ITaskPlanner::ITaskPlanner”: 没有重载函数接受 2 个参数
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(30,18): message : 尝试匹配参数列表“(NSDrones::NSAlgorithm::IPathPlannerPtr, NSDrones::NSAlgorithm::ITrajectoryOptimizerPtr)”时
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(96,25): error C2065: “path_planner_”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(226,24): error C2065: “path_planner_”: 未声明的标识符
  surveymultipoints_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(31,18): error C2661: “NSDrones::NSPlanning::ITaskPlanner::ITaskPlanner”: 没有重载函数接受 2 个参数
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(31,18): message : 尝试匹配参数列表“(NSDrones::NSAlgorithm::IPathPlannerPtr, NSDrones::NSAlgorithm::ITrajectoryOptimizerPtr)”时
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(70,25): error C2065: “path_planner_”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\surveymultipoints_taskplanner.cpp(127,24): error C2065: “path_planner_”: 未声明的标识符
  surveysphere_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(28,18): error C2661: “NSDrones::NSPlanning::ITaskPlanner::ITaskPlanner”: 没有重载函数接受 2 个参数
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(28,18): message : 尝试匹配参数列表“(NSDrones::NSAlgorithm::IPathPlannerPtr, NSDrones::NSAlgorithm::ITrajectoryOptimizerPtr)”时
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(88,25): error C2065: “path_planner_”: 未声明的标识符
E:\source\dronesplanning\src\planning\task_planners\surveysphere_taskplanner.cpp(178,24): error C2065: “path_planner_”: 未声明的标识符
  fixedwing_dynamics.cpp
  multirotor_dynamics.cpp
  vtol_dynamics.cpp
  fixedwing_energies.cpp
  multirotor_energies.cpp
  vtol_energies.cpp
  ienergy_model.cpp
  正在生成代码...
  正在编译...
  uav.cpp
  uav_config.cpp
  正在生成代码...
