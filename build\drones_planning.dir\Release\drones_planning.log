﻿  task_allocator.cpp
  energy_evaluator.cpp
  ipath_planner.cpp
  rrtstar_planner.cpp
  trajectory_optimizer.cpp
  config.cpp
  base_object.cpp
  entity_object.cpp
  movement_strategy.cpp
  collision_engine.cpp
  coordinate_manager.cpp
  task_space.cpp
  obstacle.cpp
  zone.cpp
  environment.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,56): error C2665: “NSDrones::NSEnvironment::GeometryManager::GeometryManager”: 没有重载函数可以转换所有参数类型
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(376,3): message : 可能是“NSDrones::NSEnvironment::GeometryManager::GeometryManager(const NSDrones::NSEnvironment::GeometryManager &)”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,56): message : “NSDrones::NSEnvironment::GeometryManager::GeometryManager(const NSDrones::NSEnvironment::GeometryManager &)”: 无法将参数 1 从“std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>”转换为“const NSDrones::NSEnvironment::GeometryManager &”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,77): message : 原因如下: 无法从“std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>”转换为“const NSDrones::NSEnvironment::GeometryManager”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,77): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,56): message : 尝试匹配参数列表“(std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>)”时
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1983): message : 查看对正在编译的函数 模板 实例化“void std::_Construct_in_place<_Ty,std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>&>(_Ty &,std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager> &) noexcept(false)”的引用
          with
          [
              _Ty=NSDrones::NSEnvironment::GeometryManager
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(2663): message : 查看对正在编译的函数 模板 实例化“std::_Ref_count_obj2<_Ty>::_Ref_count_obj2<std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>&>(std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager> &)”的引用
          with
          [
              _Ty=NSDrones::NSEnvironment::GeometryManager
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(2664,20): message : 查看对正在编译的函数 模板 实例化“std::_Ref_count_obj2<_Ty>::_Ref_count_obj2<std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>&>(std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager> &)”的引用
          with
          [
              _Ty=NSDrones::NSEnvironment::GeometryManager
          ]
E:\source\dronesplanning\src\environment\environment.cpp(241,78): message : 查看对正在编译的函数 模板 实例化“std::shared_ptr<NSDrones::NSEnvironment::GeometryManager> std::make_shared<NSDrones::NSEnvironment::GeometryManager,std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>&>(std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager> &)”的引用
  geometry_manager.cpp
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(316,5): error C2653: “GeographicLib”: 不是类或命名空间名称
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(316,20): error C2061: 语法错误: 标识符“LocalCartesian”
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(325,11): error C2653: “GeographicLib”: 不是类或命名空间名称
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(325,40): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(325,40): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(334,11): error C2653: “GeographicLib”: 不是类或命名空间名称
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(334,40): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(334,40): error C2143: 语法错误: 缺少“,”(在“&”的前面)
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(123,55): error C2660: “NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”: 函数不接受 2 个参数
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(314,27): message : 参见“NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(123,55): message : 尝试匹配参数列表“(const std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>, GeographicLib::LocalCartesian)”时
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(154,55): error C2660: “NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”: 函数不接受 2 个参数
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(314,27): message : 参见“NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(154,55): message : 尝试匹配参数列表“(const std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>, GeographicLib::LocalCartesian)”时
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(156,39): error C2664: “NSDrones::NSCore::WGS84Point NSDrones::NSEnvironment::GeometryManager::convertFromLocalECEF(const NSDrones::NSCore::EcefPoint &,const int) const”: 无法将参数 2 从“GeographicLib::LocalCartesian”转换为“const int”
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(156,75): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(333,15): message : 参见“NSDrones::NSEnvironment::GeometryManager::convertFromLocalECEF”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(156,39): message : 尝试匹配参数列表“(NSDrones::NSCore::EcefPoint, GeographicLib::LocalCartesian)”时
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(179,54): error C2660: “NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”: 函数不接受 2 个参数
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(314,27): message : 参见“NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(179,54): message : 尝试匹配参数列表“(std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>, GeographicLib::LocalCartesian)”时
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(215,54): error C2660: “NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”: 函数不接受 2 个参数
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(314,27): message : 参见“NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(215,54): message : 尝试匹配参数列表“(std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>, GeographicLib::LocalCartesian)”时
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(322,54): error C2660: “NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”: 函数不接受 2 个参数
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(314,27): message : 参见“NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(322,54): message : 尝试匹配参数列表“(std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>, GeographicLib::LocalCartesian)”时
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(395,54): error C2660: “NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”: 函数不接受 2 个参数
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(314,27): message : 参见“NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(395,54): message : 尝试匹配参数列表“(std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>, GeographicLib::LocalCartesian)”时
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(501,55): error C2660: “NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”: 函数不接受 2 个参数
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(314,27): message : 参见“NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(501,55): message : 尝试匹配参数列表“(const std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>>, GeographicLib::LocalCartesian)”时
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(583,49): error C2511: “std::vector<NSDrones::NSCore::EcefPoint,std::allocator<NSDrones::NSCore::EcefPoint>> NSDrones::NSEnvironment::GeometryManager::setupLocalCoordinateSystem(const std::vector<NSDrones::NSCore::WGS84Point,std::allocator<NSDrones::NSCore::WGS84Point>> &,GeographicLib::LocalCartesian &) const”:“NSDrones::NSEnvironment::GeometryManager”中没有找到重载的成员函数
E:\source\dronesplanning\include\environment/environment.h(58,9): message : 参见“NSDrones::NSEnvironment::GeometryManager”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(618,36): error C2511: “NSDrones::NSCore::EcefPoint NSDrones::NSEnvironment::GeometryManager::convertToLocalECEF(const NSDrones::NSCore::WGS84Point &,const GeographicLib::LocalCartesian &) const”:“NSDrones::NSEnvironment::GeometryManager”中没有找到重载的成员函数
E:\source\dronesplanning\include\environment/environment.h(58,9): message : 参见“NSDrones::NSEnvironment::GeometryManager”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(626,37): error C2511: “NSDrones::NSCore::WGS84Point NSDrones::NSEnvironment::GeometryManager::convertFromLocalECEF(const NSDrones::NSCore::EcefPoint &,const GeographicLib::LocalCartesian &) const”:“NSDrones::NSEnvironment::GeometryManager”中没有找到重载的成员函数
E:\source\dronesplanning\include\environment/environment.h(58,9): message : 参见“NSDrones::NSEnvironment::GeometryManager”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(903,30): error C2039: "i0": 不是 "NSDrones::NSCore::TriangleIndices" 的成员
E:\source\dronesplanning\include\core/types.h(556,10): message : 参见“NSDrones::NSCore::TriangleIndices”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(904,30): error C2039: "i1": 不是 "NSDrones::NSCore::TriangleIndices" 的成员
E:\source\dronesplanning\include\core/types.h(556,10): message : 参见“NSDrones::NSCore::TriangleIndices”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(905,30): error C2039: "i2": 不是 "NSDrones::NSCore::TriangleIndices" 的成员
E:\source\dronesplanning\include\core/types.h(556,10): message : 参见“NSDrones::NSCore::TriangleIndices”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(919,30): error C2039: "i0": 不是 "NSDrones::NSCore::TriangleIndices" 的成员
E:\source\dronesplanning\include\core/types.h(556,10): message : 参见“NSDrones::NSCore::TriangleIndices”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(920,30): error C2039: "i1": 不是 "NSDrones::NSCore::TriangleIndices" 的成员
E:\source\dronesplanning\include\core/types.h(556,10): message : 参见“NSDrones::NSCore::TriangleIndices”的声明
E:\source\dronesplanning\src\environment\geometry\geometry_manager.cpp(921,30): error C2039: "i2": 不是 "NSDrones::NSCore::TriangleIndices" 的成员
E:\source\dronesplanning\include\core/types.h(556,10): message : 参见“NSDrones::NSCore::TriangleIndices”的声明
  main.cpp
  itask_planner.cpp
  mission_planner.cpp
  followpath_taskplanner.cpp
  正在生成代码...
  正在编译...
  loiterpoint_taskplanner.cpp
  scanarea_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(136,48): error C2039: "checkPointsCoplanar": 不是 "NSDrones::NSEnvironment::GeometryManager" 的成员
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(37,9): message : 参见“NSDrones::NSEnvironment::GeometryManager”的声明
E:\source\dronesplanning\src\planning\task_planners\scanarea_taskplanner.cpp(156,64): error C2039: "generateScanPath": 不是 "NSDrones::NSEnvironment::GeometryManager" 的成员
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(37,9): message : 参见“NSDrones::NSEnvironment::GeometryManager”的声明
  surveycylinder_taskplanner.cpp
E:\source\dronesplanning\src\planning\task_planners\surveycylinder_taskplanner.cpp(166,73): error C2039: "generateCylinderScanPath": 不是 "NSDrones::NSEnvironment::GeometryManager" 的成员
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(37,9): message : 参见“NSDrones::NSEnvironment::GeometryManager”的声明
  surveymultipoints_taskplanner.cpp
  surveysphere_taskplanner.cpp
  fixedwing_dynamics.cpp
  multirotor_dynamics.cpp
  vtol_dynamics.cpp
  fixedwing_energies.cpp
  multirotor_energies.cpp
  vtol_energies.cpp
  flight_strategy.cpp
  idynamic_model.cpp
  ienergy_model.cpp
  uav.cpp
  uav_config.cpp
  正在生成代码...
