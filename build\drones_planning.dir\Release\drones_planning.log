﻿  environment.cpp
  single_gridmap.cpp
  tiled_gridmap.cpp
  main.cpp
  正在生成代码...
    正在创建库 E:/source/dronesplanning/build/Release/drones_planning.lib 和对象 E:/source/dronesplanning/build/Release/drones_planning.exp
single_gridmap.obj : error LNK2019: 无法解析的外部符号 "private: class Eigen::Matrix<double,3,1,0,3,1> __cdecl NSDrones::NSEnvironment::SingleGridMap::wgs84ToLocal(struct NSDrones::NSCore::WGS84Point const &)const " (?wgs84ToLocal@SingleGridMap@NSEnvironment@NSDrones@@AEBA?AV?$Matrix@N$02$00$0A@$02$00@Eigen@@AEBUWGS84Point@NSCore@3@@Z)，函数 "private: bool __cdecl NSDrones::NSEnvironment::SingleGridMap::wgs84ToMatrixIndex(struct NSDrones::NSCore::WGS84Point const &,int &,int &)const " (?wgs84ToMatrixIndex@SingleGridMap@NSEnvironment@NSDrones@@AEBA_NAEBUWGS84Point@NSCore@3@AEAH1@Z) 中引用了该符号
E:\source\dronesplanning\build\Release\drones_planning.exe : fatal error LNK1120: 1 个无法解析的外部命令
