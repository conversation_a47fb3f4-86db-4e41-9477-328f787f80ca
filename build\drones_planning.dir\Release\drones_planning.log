﻿  environment.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,56): error C2665: “NSDrones::NSEnvironment::GeometryManager::GeometryManager”: 没有重载函数可以转换所有参数类型
E:\source\dronesplanning\include\environment/geometry/geometry_manager.h(426,3): message : 可能是“NSDrones::NSEnvironment::GeometryManager::GeometryManager(const NSDrones::NSEnvironment::GeometryManager &)”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,56): message : “NSDrones::NSEnvironment::GeometryManager::GeometryManager(const NSDrones::NSEnvironment::GeometryManager &)”: 无法将参数 1 从“std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>”转换为“const NSDrones::NSEnvironment::GeometryManager &”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,77): message : 原因如下: 无法从“std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>”转换为“const NSDrones::NSEnvironment::GeometryManager”
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,77): message : 没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\xutility(275,56): message : 尝试匹配参数列表“(std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>)”时
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(1983): message : 查看对正在编译的函数 模板 实例化“void std::_Construct_in_place<_Ty,std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>&>(_Ty &,std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager> &) noexcept(false)”的引用
          with
          [
              _Ty=NSDrones::NSEnvironment::GeometryManager
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(2663): message : 查看对正在编译的函数 模板 实例化“std::_Ref_count_obj2<_Ty>::_Ref_count_obj2<std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>&>(std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager> &)”的引用
          with
          [
              _Ty=NSDrones::NSEnvironment::GeometryManager
          ]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.35.32215\include\memory(2664,20): message : 查看对正在编译的函数 模板 实例化“std::_Ref_count_obj2<_Ty>::_Ref_count_obj2<std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>&>(std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager> &)”的引用
          with
          [
              _Ty=NSDrones::NSEnvironment::GeometryManager
          ]
E:\source\dronesplanning\src\environment\environment.cpp(427,1): message : 查看对正在编译的函数 模板 实例化“std::shared_ptr<NSDrones::NSEnvironment::GeometryManager> std::make_shared<NSDrones::NSEnvironment::GeometryManager,std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager>&>(std::shared_ptr<NSDrones::NSEnvironment::CoordinateManager> &)”的引用
