// src/environment/geometry/geometry_manager.cpp
#include "environment/geometry/geometry_manager.h"
#include "environment/coordinate/coordinate_manager.h"
#include "utils/coordinate_converter.h"
#include "utils/logging.h"
#include <stdexcept>
#include <algorithm>
#include <cmath>
#include <array>

// 确保M_PI定义可用
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// GeographicLib用于精确的地理计算
#include <GeographicLib/Geodesic.hpp>
#include <GeographicLib/LocalCartesian.hpp>

// Earcut用于三角剖分（如果需要）
// #include <earcut.hpp>

namespace NSDrones {
    namespace NSEnvironment {

        // === 构造函数和析构函数 ===

        GeometryManager::GeometryManager(std::shared_ptr<CoordinateManager> coord_manager)
            : coord_manager_(coord_manager) {

            if (!coord_manager_) {
                throw std::invalid_argument("几何管理器: 坐标系统管理器不能为空");
            }

            LOG_INFO("几何管理器: 初始化完成，使用CoordinateManager进行高精度坐标转换");
        }

        // === 基础距离和方位计算 ===

        double GeometryManager::calculateDistance(const WGS84Point& p1, const WGS84Point& p2) const {
            try {
                // 使用GeographicLib进行高精度距离计算
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double distance;
                geod.Inverse(p1.latitude, p1.longitude, p2.latitude, p2.longitude, distance);

                LOG_TRACE("几何管理器: 距离计算完成，{:.3f}米", distance);
                return distance;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 距离计算失败: {}", e.what());
                throw std::runtime_error("距离计算失败: " + std::string(e.what()));
            }
        }

        double GeometryManager::calculateBearing(const WGS84Point& from, const WGS84Point& to) const {
            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double distance, bearing;
                geod.Inverse(from.latitude, from.longitude, to.latitude, to.longitude, distance, bearing);

                // 将方位角转换为 0-360 度范围
                if (bearing < 0) {
                    bearing += 360.0;
                }

                LOG_TRACE("几何管理器: 方位角计算完成，{:.2f}度", bearing);
                return bearing;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 方位角计算失败: {}", e.what());
                throw std::runtime_error("方位角计算失败: " + std::string(e.what()));
            }
        }

        WGS84Point GeometryManager::calculateDestination(const WGS84Point& start, double bearing_deg, double distance_m) const {
            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double dest_lat, dest_lon;
                geod.Direct(start.latitude, start.longitude, bearing_deg, distance_m, dest_lat, dest_lon);

                WGS84Point destination;
                destination.latitude = dest_lat;
                destination.longitude = dest_lon;
                destination.altitude = start.altitude; // 保持相同高度

                LOG_TRACE("几何管理器: 目标点计算完成，({:.6f}, {:.6f})", dest_lat, dest_lon);
                return destination;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 目标点计算失败: {}", e.what());
                throw std::runtime_error("目标点计算失败: " + std::string(e.what()));
            }
        }

        // === 批量计算接口（性能优化） ===

        std::vector<double> GeometryManager::batchCalculateDistance(
            const std::vector<WGS84Point>& points1,
            const std::vector<WGS84Point>& points2) const {

            if (points1.size() != points2.size()) {
                throw std::invalid_argument("几何管理器: 批量距离计算的两个点集大小不匹配");
            }

            std::vector<double> distances;
            distances.reserve(points1.size());

            try {
                // 使用GeographicLib进行批量计算
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();

                for (size_t i = 0; i < points1.size(); ++i) {
                    double distance;
                    geod.Inverse(points1[i].latitude, points1[i].longitude,
                               points2[i].latitude, points2[i].longitude, distance);
                    distances.push_back(distance);
                }

                LOG_DEBUG("几何管理器: 批量距离计算完成，处理了 {} 对点", points1.size());
                return distances;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 批量距离计算失败: {}", e.what());
                throw std::runtime_error("批量距离计算失败: " + std::string(e.what()));
            }
        }

        // === 空间查询 ===

        std::vector<size_t> GeometryManager::findPointsInRadius(
            const WGS84Point& center,
            double radius_m,
            const std::vector<WGS84Point>& candidates) const {

            try {
                std::vector<size_t> result;

                if (candidates.empty()) {
                    return result;
                }

                // 使用GeographicLib进行精确距离计算
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();

                // 查找半径内的点
                for (size_t i = 0; i < candidates.size(); ++i) {
                    double distance;
                    geod.Inverse(center.latitude, center.longitude,
                               candidates[i].latitude, candidates[i].longitude, distance);

                    if (distance <= radius_m) {
                        result.push_back(i);
                    }
                }

                LOG_DEBUG("几何管理器: 半径查询完成，{}个候选点中找到{}个", candidates.size(), result.size());
                return result;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 半径查询失败: {}", e.what());
                throw std::runtime_error("半径查询失败: " + std::string(e.what()));
            }
        }

        // === 面积和几何形状计算 ===

        double GeometryManager::calculatePolygonArea(const std::vector<WGS84Point>& polygon) const {
            if (polygon.size() < 3) {
                throw std::invalid_argument("几何管理器: 多边形顶点数少于3，无法计算面积");
            }

            try {
                // 使用 GeographicLib 进行精确的坐标转换和面积计算

                // 计算多边形的质心作为局部坐标系原点
                double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
                for (const auto& point : polygon) {
                    center_lat += point.latitude;
                    center_lon += point.longitude;
                    center_alt += point.altitude;
                }
                center_lat /= polygon.size();
                center_lon /= polygon.size();
                center_alt /= polygon.size();

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 将WGS84坐标转换到局部笛卡尔坐标系
                std::vector<EcefPoint> local_polygon;
                local_polygon.reserve(polygon.size());

                for (const auto& point : polygon) {
                    double x, y, z;
                    local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
                    local_polygon.emplace_back(x, y, z);
                }

                // 在局部坐标系中计算面积
                double area = calculatePolygonAreaECEF(local_polygon);

                LOG_DEBUG("几何管理器: 多边形面积计算完成，{:.2f}平方米", area);
                return area;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形面积计算失败: {}", e.what());
                throw std::runtime_error("多边形面积计算失败: " + std::string(e.what()));
            }
        }

        //=== 几何关系判断 ===//

        bool GeometryManager::isPointInPolygon(const WGS84Point& point, const std::vector<WGS84Point>& polygon) const {
            if (polygon.size() < 3) {
                return false;
            }

            try {
                // 计算多边形的质心作为局部坐标系原点
                double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
                for (const auto& p : polygon) {
                    center_lat += p.latitude;
                    center_lon += p.longitude;
                    center_alt += p.altitude;
                }
                center_lat /= polygon.size();
                center_lon /= polygon.size();
                center_alt /= polygon.size();

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换点到局部坐标系（使用ECEF坐标进行内部计算）
                double px, py, pz;
                local_cart.Forward(point.latitude, point.longitude, point.altitude, px, py, pz);
                EcefPoint point_ecef(px, py, pz);

                // 转换多边形到局部坐标系
                std::vector<EcefPoint> polygon_ecef;
                polygon_ecef.reserve(polygon.size());
                for (const auto& p : polygon) {
                    double x, y, z;
                    local_cart.Forward(p.latitude, p.longitude, p.altitude, x, y, z);
                    polygon_ecef.emplace_back(x, y, z);
                }

                // 在ECEF坐标系中进行判断（使用静态方法版本，指定容差）
                bool result = GeometryManager::isPointInPolygonECEF(point_ecef, polygon_ecef, 1e-6);

                LOG_TRACE("GeometryManager: 点在多边形内判断完成，结果: {}", result);
                return result;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 点在多边形内判断失败: {}", e.what());
                return false;
            }
        }

        bool GeometryManager::doBoundingBoxesIntersect(const WGS84BoundingBox& box1, const WGS84BoundingBox& box2) const {
            // 简单的边界框相交检测（在WGS84坐标系中）
            return !(box1.maxLatitude < box2.minLatitude || box2.maxLatitude < box1.minLatitude ||
                     box1.maxLongitude < box2.minLongitude || box2.maxLongitude < box1.minLongitude);
        }

        // === 路径和轨迹分析 ===

        double GeometryManager::calculatePathLength(const std::vector<WGS84Point>& waypoints) const {
            if (waypoints.size() < 2) {
                throw std::invalid_argument("几何管理器: 路径点少于2个，无法计算路径长度");
            }

            try {
                // 使用GeographicLib进行精确距离计算
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();

                double total_length = 0.0;
                for (size_t i = 1; i < waypoints.size(); ++i) {
                    double distance;
                    geod.Inverse(waypoints[i-1].latitude, waypoints[i-1].longitude,
                               waypoints[i].latitude, waypoints[i].longitude, distance);
                    total_length += distance;
                }

                LOG_DEBUG("几何管理器: 路径长度计算完成，{:.2f}米", total_length);
                return total_length;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 路径长度计算失败: {}", e.what());
                throw std::runtime_error("路径长度计算失败: " + std::string(e.what()));
            }
        }

        double GeometryManager::calculatePointToLineDistance(
            const WGS84Point& point,
            const WGS84Point& line_start,
            const WGS84Point& line_end) const {

            try {
                // 计算三个点的质心作为局部坐标系原点
                double center_lat = (point.latitude + line_start.latitude + line_end.latitude) / 3.0;
                double center_lon = (point.longitude + line_start.longitude + line_end.longitude) / 3.0;
                double center_alt = (point.altitude + line_start.altitude + line_end.altitude) / 3.0;

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换到局部坐标系（实际上是ECEF坐标系的局部近似）
                double px, py, pz, sx, sy, sz, ex, ey, ez;
                local_cart.Forward(point.latitude, point.longitude, point.altitude, px, py, pz);
                local_cart.Forward(line_start.latitude, line_start.longitude, line_start.altitude, sx, sy, sz);
                local_cart.Forward(line_end.latitude, line_end.longitude, line_end.altitude, ex, ey, ez);

                EcefPoint point_ecef(px, py, pz);
                EcefPoint line_start_ecef(sx, sy, sz);
                EcefPoint line_end_ecef(ex, ey, ez);

                // 在ECEF坐标系中计算距离
                double distance = calculatePointToLineDistanceECEF(point_ecef, line_start_ecef, line_end_ecef);

                LOG_TRACE("GeometryManager: 点到线段距离计算完成，{:.3f}米", distance);
                return distance;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 点到线段距离计算失败: {}", e.what());
                throw std::runtime_error("点到线段距离计算失败: " + std::string(e.what()));
            }
        }

        //=== 内部辅助方法 ===//

        std::string GeometryManager::selectOptimalUsageHint(const std::vector<WGS84Point>& points) const {
            if (points.empty()) {
                return "general";
            }
            
            // 估算数据范围
            double range_km = estimateDataRange(points);
            
            // 根据范围选择最优使用提示
            if (range_km < 0.01) return "collision";     // <10m: 碰撞检测精度
            if (range_km < 0.1) return "terrain";        // <100m: 地形查询精度
            if (range_km < 1.0) return "path";           // <1km: 路径规划精度
            
            return "general";  // 默认精度
        }

        double GeometryManager::estimateDataRange(const std::vector<WGS84Point>& points) const {
            if (points.size() < 2) {
                return 0.0;
            }

            // 计算边界框
            double min_lat = points[0].latitude, max_lat = points[0].latitude;
            double min_lon = points[0].longitude, max_lon = points[0].longitude;

            for (const auto& point : points) {
                min_lat = std::min(min_lat, point.latitude);
                max_lat = std::max(max_lat, point.latitude);
                min_lon = std::min(min_lon, point.longitude);
                max_lon = std::max(max_lon, point.longitude);
            }

            // 使用 GeographicLib 计算边界框对角线的精确距离
            const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
            double diagonal_distance;
            geod.Inverse(min_lat, min_lon, max_lat, max_lon, diagonal_distance);

            // 转换为公里
            return diagonal_distance / 1000.0;
        }

        //=== 内部几何计算方法（基于ECEF坐标系） ===//

        double GeometryManager::calculatePolygonAreaECEF(const std::vector<EcefPoint>& polygon_ecef) const {
            // 使用鞋带公式计算多边形面积（投影到XY平面）
            // 注意：对于ECEF坐标，这是一个近似计算，适用于局部区域
            if (polygon_ecef.size() < 3) {
                return 0.0;
            }

            double area = 0.0;
            size_t n = polygon_ecef.size();

            for (size_t i = 0; i < n; ++i) {
                size_t j = (i + 1) % n;
                area += polygon_ecef[i].x() * polygon_ecef[j].y();
                area -= polygon_ecef[j].x() * polygon_ecef[i].y();
            }

            return std::abs(area) / 2.0;
        }

        bool GeometryManager::isPointInPolygonECEF(const EcefPoint& point_ecef, const std::vector<EcefPoint>& polygon_ecef) const {
            // 使用射线法判断点是否在多边形内（基于XY投影）
            // 注意：对于ECEF坐标，这是一个近似计算，适用于局部区域
            if (polygon_ecef.size() < 3) {
                return false;
            }

            bool inside = false;
            size_t n = polygon_ecef.size();

            for (size_t i = 0, j = n - 1; i < n; j = i++) {
                double xi = polygon_ecef[i].x(), yi = polygon_ecef[i].y();
                double xj = polygon_ecef[j].x(), yj = polygon_ecef[j].y();

                if (((yi > point_ecef.y()) != (yj > point_ecef.y())) &&
                    (point_ecef.x() < (xj - xi) * (point_ecef.y() - yi) / (yj - yi) + xi)) {
                    inside = !inside;
                }
            }

            return inside;
        }

        double GeometryManager::calculatePointToLineDistanceECEF(
            const EcefPoint& point_ecef,
            const EcefPoint& line_start_ecef,
            const EcefPoint& line_end_ecef) const {

            Vector3D line_vec = line_end_ecef.toVector3D() - line_start_ecef.toVector3D();
            Vector3D point_vec = point_ecef.toVector3D() - line_start_ecef.toVector3D();

            double line_length_sq = line_vec.squaredNorm();
            if (line_length_sq < 1e-12) {
                // 线段退化为点
                return (point_ecef.toVector3D() - line_start_ecef.toVector3D()).norm();
            }

            double t = point_vec.dot(line_vec) / line_length_sq;
            t = std::max(0.0, std::min(1.0, t));  // 限制在线段范围内

            Vector3D closest_point = line_start_ecef.toVector3D() + t * line_vec;
            return (point_ecef.toVector3D() - closest_point).norm();
        }



        std::vector<WGS84Point> GeometryManager::generateGreatCirclePath(
            const WGS84Point& start,
            const WGS84Point& end,
            double max_segment_length_m) const {

            try {
                std::vector<WGS84Point> path;

                // 计算总距离
                double total_distance = calculateDistance(start, end);
                if (total_distance <= max_segment_length_m) {
                    // 距离足够短，直接返回起点和终点
                    path.push_back(start);
                    path.push_back(end);
                    return path;
                }

                // 计算需要的分段数
                int num_segments = static_cast<int>(std::ceil(total_distance / max_segment_length_m));
                double segment_distance = total_distance / num_segments;

                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double bearing;
                geod.Inverse(start.latitude, start.longitude, end.latitude, end.longitude, total_distance, bearing);

                // 生成路径点
                path.reserve(num_segments + 1);
                path.push_back(start);

                for (int i = 1; i < num_segments; ++i) {
                    double distance = i * segment_distance;
                    double lat, lon;
                    geod.Direct(start.latitude, start.longitude, bearing, distance, lat, lon);

                    WGS84Point waypoint;
                    waypoint.latitude = lat;
                    waypoint.longitude = lon;
                    // 线性插值高度
                    double t = static_cast<double>(i) / num_segments;
                    waypoint.altitude = start.altitude + t * (end.altitude - start.altitude);

                    path.push_back(waypoint);
                }

                path.push_back(end);

                LOG_DEBUG("几何管理器: 大圆路径生成完成，{}个航点", path.size());
                return path;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 大圆路径生成失败: {}", e.what());
                throw std::runtime_error("大圆路径生成失败: " + std::string(e.what()));
            }
        }

        WGS84Point GeometryManager::calculatePolygonCentroid(const std::vector<WGS84Point>& polygon) const {
            if (polygon.empty()) {
                throw std::invalid_argument("几何管理器: 多边形为空，无法计算质心");
            }

            if (polygon.size() == 1) {
                return polygon[0];
            }

            try {
                // 对于简单情况，直接计算几何中心
                if (polygon.size() == 2) {
                    WGS84Point centroid;
                    centroid.latitude = (polygon[0].latitude + polygon[1].latitude) / 2.0;
                    centroid.longitude = (polygon[0].longitude + polygon[1].longitude) / 2.0;
                    centroid.altitude = (polygon[0].altitude + polygon[1].altitude) / 2.0;
                    return centroid;
                }

                // 计算几何中心作为局部坐标系原点
                double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
                for (const auto& point : polygon) {
                    center_lat += point.latitude;
                    center_lon += point.longitude;
                    center_alt += point.altitude;
                }
                center_lat /= polygon.size();
                center_lon /= polygon.size();
                center_alt /= polygon.size();

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换到局部坐标系
                std::vector<EcefPoint> local_polygon;
                local_polygon.reserve(polygon.size());
                for (const auto& point : polygon) {
                    double x, y, z;
                    local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
                    local_polygon.emplace_back(x, y, z);
                }

                // 计算面积加权质心
                double total_area = 0.0;
                double centroid_x = 0.0, centroid_y = 0.0, centroid_z = 0.0;

                // 使用三角剖分计算质心
                for (size_t i = 1; i < local_polygon.size() - 1; ++i) {
                    const EcefPoint& p0 = local_polygon[0];
                    const EcefPoint& p1 = local_polygon[i];
                    const EcefPoint& p2 = local_polygon[i + 1];

                    // 计算三角形面积（使用叉积）
                    Vector3D v1 = p1.toVector3D() - p0.toVector3D();
                    Vector3D v2 = p2.toVector3D() - p0.toVector3D();
                    double triangle_area = 0.5 * v1.cross(v2).norm();

                    if (triangle_area > 1e-12) {
                        // 三角形质心
                        double tri_cx = (p0.x() + p1.x() + p2.x()) / 3.0;
                        double tri_cy = (p0.y() + p1.y() + p2.y()) / 3.0;
                        double tri_cz = (p0.z() + p1.z() + p2.z()) / 3.0;

                        // 面积加权累加
                        centroid_x += triangle_area * tri_cx;
                        centroid_y += triangle_area * tri_cy;
                        centroid_z += triangle_area * tri_cz;
                        total_area += triangle_area;
                    }
                }

                if (total_area < 1e-12) {
                    // 退化情况，返回几何中心
                    WGS84Point centroid;
                    centroid.latitude = center_lat;
                    centroid.longitude = center_lon;
                    centroid.altitude = center_alt;
                    return centroid;
                }

                // 面积加权质心
                centroid_x /= total_area;
                centroid_y /= total_area;
                centroid_z /= total_area;

                // 转换回 WGS84 坐标
                double centroid_lat, centroid_lon, centroid_alt;
                local_cart.Reverse(centroid_x, centroid_y, centroid_z,
                                  centroid_lat, centroid_lon, centroid_alt);

                WGS84Point centroid;
                centroid.latitude = centroid_lat;
                centroid.longitude = centroid_lon;
                centroid.altitude = centroid_alt;

                LOG_DEBUG("几何管理器: 多边形质心计算完成，({:.6f}, {:.6f}, {:.2f})",
                         centroid_lat, centroid_lon, centroid_alt);
                return centroid;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形质心计算失败: {}", e.what());
                throw std::runtime_error("多边形质心计算失败: " + std::string(e.what()));
            }
        }

        // === 高级几何计算 ===

        std::vector<TriangleIndices> GeometryManager::triangulatePolygon(
            const std::vector<WGS84Point>& polygon,
            const std::vector<std::vector<WGS84Point>>& holes) const {

            if (polygon.size() < 3) {
                throw std::invalid_argument("几何管理器: 多边形顶点数少于3，无法进行三角剖分");
            }

            try {
                // 计算多边形的质心作为局部坐标系原点
                double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
                for (const auto& point : polygon) {
                    center_lat += point.latitude;
                    center_lon += point.longitude;
                    center_alt += point.altitude;
                }
                center_lat /= polygon.size();
                center_lon /= polygon.size();
                center_alt /= polygon.size();

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换到局部坐标系
                std::vector<EcefPoint> local_polygon;
                local_polygon.reserve(polygon.size());
                for (const auto& point : polygon) {
                    double x, y, z;
                    local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
                    local_polygon.emplace_back(x, y, z);
                }

                // 使用静态方法进行三角剖分
                auto triangles = triangulatePolygon2D(local_polygon);

                LOG_DEBUG("几何管理器: 多边形三角剖分完成，生成{}个三角形", triangles.size());
                return triangles;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形三角剖分失败: {}", e.what());
                throw std::runtime_error("多边形三角剖分失败: " + std::string(e.what()));
            }
        }

        std::vector<WGS84Point> GeometryManager::generateGridPoints(const WGS84BoundingBox& bounds,
                                                                   double spacing_m) const {
            if (spacing_m <= 0) {
                throw std::invalid_argument("几何管理器: 网格间距必须大于0");
            }

            try {
                std::vector<WGS84Point> grid_points;

                // 计算边界框中心作为参考点
                double center_lat = (bounds.minLatitude + bounds.maxLatitude) / 2.0;
                double center_lon = (bounds.minLongitude + bounds.maxLongitude) / 2.0;

                // 使用GeographicLib计算度数对应的距离
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();

                // 计算纬度和经度的步长
                double lat_step = spacing_m / 111320.0; // 大约每度111.32km
                double lon_step = spacing_m / (111320.0 * std::cos(center_lat * M_PI / 180.0));

                // 生成网格点
                for (double lat = bounds.minLatitude; lat <= bounds.maxLatitude; lat += lat_step) {
                    for (double lon = bounds.minLongitude; lon <= bounds.maxLongitude; lon += lon_step) {
                        WGS84Point point;
                        point.latitude = lat;
                        point.longitude = lon;
                        point.altitude = (bounds.minAltitude + bounds.maxAltitude) / 2.0;
                        grid_points.push_back(point);
                    }
                }

                LOG_DEBUG("几何管理器: 网格点生成完成，生成{}个点", grid_points.size());
                return grid_points;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 网格点生成失败: {}", e.what());
                throw std::runtime_error("网格点生成失败: " + std::string(e.what()));
            }
        }

        WGS84BoundingBox GeometryManager::calculateBoundingBox(const std::vector<WGS84Point>& points) const {
            if (points.empty()) {
                throw std::invalid_argument("几何管理器: 点集为空，无法计算边界框");
            }

            WGS84BoundingBox bbox;
            bbox.minLatitude = bbox.maxLatitude = points[0].latitude;
            bbox.minLongitude = bbox.maxLongitude = points[0].longitude;
            bbox.minAltitude = bbox.maxAltitude = points[0].altitude;

            for (const auto& point : points) {
                bbox.minLatitude = (std::min)(bbox.minLatitude, point.latitude);
                bbox.maxLatitude = (std::max)(bbox.maxLatitude, point.latitude);
                bbox.minLongitude = (std::min)(bbox.minLongitude, point.longitude);
                bbox.maxLongitude = (std::max)(bbox.maxLongitude, point.longitude);
                bbox.minAltitude = (std::min)(bbox.minAltitude, point.altitude);
                bbox.maxAltitude = (std::max)(bbox.maxAltitude, point.altitude);
            }

            LOG_DEBUG("几何管理器: 边界框计算完成");
            return bbox;
        }

        bool GeometryManager::doPolygonsIntersect(const std::vector<WGS84Point>& poly1,
                                                 const std::vector<WGS84Point>& poly2) const {
            if (poly1.size() < 3 || poly2.size() < 3) {
                return false;
            }

            try {
                // 首先检查边界框是否相交
                WGS84BoundingBox bbox1 = calculateBoundingBox(poly1);
                WGS84BoundingBox bbox2 = calculateBoundingBox(poly2);

                if (!doBoundingBoxesIntersect(bbox1, bbox2)) {
                    return false;
                }

                // 计算两个多边形的质心作为局部坐标系原点
                double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
                for (const auto& point : poly1) {
                    center_lat += point.latitude;
                    center_lon += point.longitude;
                    center_alt += point.altitude;
                }
                for (const auto& point : poly2) {
                    center_lat += point.latitude;
                    center_lon += point.longitude;
                    center_alt += point.altitude;
                }
                center_lat /= (poly1.size() + poly2.size());
                center_lon /= (poly1.size() + poly2.size());
                center_alt /= (poly1.size() + poly2.size());

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换多边形到局部坐标系
                std::vector<EcefPoint> ecef_poly1, ecef_poly2;
                ecef_poly1.reserve(poly1.size());
                ecef_poly2.reserve(poly2.size());

                for (const auto& point : poly1) {
                    double x, y, z;
                    local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
                    ecef_poly1.emplace_back(x, y, z);
                }

                for (const auto& point : poly2) {
                    double x, y, z;
                    local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
                    ecef_poly2.emplace_back(x, y, z);
                }

                // 检查是否有顶点在对方多边形内
                for (const auto& point : ecef_poly1) {
                    if (isPointInPolygonECEF(point, ecef_poly2)) {
                        return true;
                    }
                }

                for (const auto& point : ecef_poly2) {
                    if (isPointInPolygonECEF(point, ecef_poly1)) {
                        return true;
                    }
                }

                // 检查边是否相交
                for (size_t i = 0; i < ecef_poly1.size(); ++i) {
                    size_t next_i = (i + 1) % ecef_poly1.size();
                    for (size_t j = 0; j < ecef_poly2.size(); ++j) {
                        size_t next_j = (j + 1) % ecef_poly2.size();

                        if (isSegmentsIntersect2D(ecef_poly1[i], ecef_poly1[next_i],
                                                 ecef_poly2[j], ecef_poly2[next_j])) {
                            return true;
                        }
                    }
                }

                return false;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形相交检测失败: {}", e.what());
                return false;
            }
        }

        std::vector<WGS84Point> GeometryManager::simplifyPolygon(const std::vector<WGS84Point>& polygon,
                                                                double tolerance_m) const {
            if (polygon.size() <= 2) {
                return polygon;
            }

            try {
                // Douglas-Peucker算法简化多边形
                std::vector<WGS84Point> simplified;
                simplified.reserve(polygon.size());

                // 递归简化函数
                std::function<void(int, int)> simplify_recursive = [&](int start, int end) {
                    if (end - start <= 1) {
                        return;
                    }

                    double max_distance = 0.0;
                    int max_index = start;

                    // 找到距离线段最远的点
                    for (int i = start + 1; i < end; ++i) {
                        double distance = calculatePointToLineDistance(polygon[i], polygon[start], polygon[end]);
                        if (distance > max_distance) {
                            max_distance = distance;
                            max_index = i;
                        }
                    }

                    // 如果最大距离超过容差，递归处理
                    if (max_distance > tolerance_m) {
                        simplify_recursive(start, max_index);
                        simplify_recursive(max_index, end);
                    }
                };

                // 添加起点
                simplified.push_back(polygon[0]);

                // 递归简化
                simplify_recursive(0, polygon.size() - 1);

                // 添加终点（如果不是闭合多边形）
                if (polygon.front().latitude != polygon.back().latitude ||
                    polygon.front().longitude != polygon.back().longitude) {
                    simplified.push_back(polygon.back());
                }

                LOG_DEBUG("几何管理器: 多边形简化完成，从{}个点简化为{}个点", polygon.size(), simplified.size());
                return simplified;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形简化失败: {}", e.what());
                return polygon;
            }
        }

        double GeometryManager::calculateAngleBetweenVectors(const WGS84Point& v1_start, const WGS84Point& v1_end,
                                                           const WGS84Point& v2_start, const WGS84Point& v2_end) const {
            try {
                // 计算四个点的质心作为局部坐标系原点
                double center_lat = (v1_start.latitude + v1_end.latitude + v2_start.latitude + v2_end.latitude) / 4.0;
                double center_lon = (v1_start.longitude + v1_end.longitude + v2_start.longitude + v2_end.longitude) / 4.0;
                double center_alt = (v1_start.altitude + v1_end.altitude + v2_start.altitude + v2_end.altitude) / 4.0;

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换到局部坐标系
                double x1s, y1s, z1s, x1e, y1e, z1e, x2s, y2s, z2s, x2e, y2e, z2e;
                local_cart.Forward(v1_start.latitude, v1_start.longitude, v1_start.altitude, x1s, y1s, z1s);
                local_cart.Forward(v1_end.latitude, v1_end.longitude, v1_end.altitude, x1e, y1e, z1e);
                local_cart.Forward(v2_start.latitude, v2_start.longitude, v2_start.altitude, x2s, y2s, z2s);
                local_cart.Forward(v2_end.latitude, v2_end.longitude, v2_end.altitude, x2e, y2e, z2e);

                // 计算向量
                Vector3D vec1(x1e - x1s, y1e - y1s, z1e - z1s);
                Vector3D vec2(x2e - x2s, y2e - y2s, z2e - z2s);

                // 计算夹角
                double dot_product = vec1.dot(vec2);
                double magnitude1 = vec1.norm();
                double magnitude2 = vec2.norm();

                if (magnitude1 < 1e-12 || magnitude2 < 1e-12) {
                    return 0.0; // 零向量
                }

                double cos_angle = dot_product / (magnitude1 * magnitude2);
                cos_angle = std::max(-1.0, std::min(1.0, cos_angle)); // 限制在[-1, 1]范围内

                double angle = std::acos(cos_angle);

                LOG_TRACE("几何管理器: 向量夹角计算完成，{:.3f}弧度", angle);
                return angle;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 向量夹角计算失败: {}", e.what());
                throw std::runtime_error("向量夹角计算失败: " + std::string(e.what()));
            }
        }



        // === 静态几何工具方法（用于形状和碰撞检测） ===

        bool GeometryManager::isPointInPolygonECEF(const EcefPoint& ecef_point,
                                                  const std::vector<EcefPoint>& ecef_polygon,
                                                  double tolerance) {
            if (ecef_polygon.size() < 3) {
                return false;
            }

            // 使用射线法判断点是否在多边形内（基于XY投影）
            bool inside = false;
            size_t n = ecef_polygon.size();

            for (size_t i = 0, j = n - 1; i < n; j = i++) {
                double xi = ecef_polygon[i].x(), yi = ecef_polygon[i].y();
                double xj = ecef_polygon[j].x(), yj = ecef_polygon[j].y();

                if (((yi > ecef_point.y()) != (yj > ecef_point.y())) &&
                    (ecef_point.x() < (xj - xi) * (ecef_point.y() - yi) / (yj - yi) + xi)) {
                    inside = !inside;
                }
            }

            return inside;
        }

        bool GeometryManager::isSegmentsIntersect2D(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2,
                                                   const EcefPoint& ecef_p3, const EcefPoint& ecef_p4,
                                                   bool include_endpoints, double tolerance) {
            // 使用向量叉积判断线段相交
            auto cross_product = [](double ax, double ay, double bx, double by) -> double {
                return ax * by - ay * bx;
            };

            double x1 = ecef_p1.x(), y1 = ecef_p1.y();
            double x2 = ecef_p2.x(), y2 = ecef_p2.y();
            double x3 = ecef_p3.x(), y3 = ecef_p3.y();
            double x4 = ecef_p4.x(), y4 = ecef_p4.y();

            double d1 = cross_product(x4 - x3, y4 - y3, x1 - x3, y1 - y3);
            double d2 = cross_product(x4 - x3, y4 - y3, x2 - x3, y2 - y3);
            double d3 = cross_product(x2 - x1, y2 - y1, x3 - x1, y3 - y1);
            double d4 = cross_product(x2 - x1, y2 - y1, x4 - x1, y4 - y1);

            if (((d1 > tolerance && d2 < -tolerance) || (d1 < -tolerance && d2 > tolerance)) &&
                ((d3 > tolerance && d4 < -tolerance) || (d3 < -tolerance && d4 > tolerance))) {
                return true;
            }

            // 检查端点是否在线段上（如果需要）
            if (include_endpoints) {
                auto point_on_segment = [tolerance](double px, double py, double x1, double y1, double x2, double y2) -> bool {
                    double cross = (py - y1) * (x2 - x1) - (px - x1) * (y2 - y1);
                    if (std::abs(cross) > tolerance) return false;

                    double dot = (px - x1) * (x2 - x1) + (py - y1) * (y2 - y1);
                    double len_sq = (x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1);
                    return dot >= -tolerance && dot <= len_sq + tolerance;
                };

                return point_on_segment(x1, y1, x3, y3, x4, y4) ||
                       point_on_segment(x2, y2, x3, y3, x4, y4) ||
                       point_on_segment(x3, y3, x1, y1, x2, y2) ||
                       point_on_segment(x4, y4, x1, y1, x2, y2);
            }

            return false;
        }

        // === 形状几何检测（ECEF坐标版本） ===

        bool GeometryManager::isPointInSphere(const EcefPoint& ecef_point, const EcefPoint& sphere_center,
                                             double radius, double tolerance) {
            Vector3D diff = ecef_point.toVector3D() - sphere_center.toVector3D();
            double distance = diff.norm();
            return distance <= (radius + tolerance);
        }

        bool GeometryManager::isPointInBox(const EcefPoint& ecef_point, const EcefPoint& box_center,
                                          const Vector3D& box_dimensions, double tolerance) {
            Vector3D diff = ecef_point.toVector3D() - box_center.toVector3D();
            Vector3D half_size = box_dimensions / 2.0;

            return (std::abs(diff.x()) <= half_size.x() + tolerance) &&
                   (std::abs(diff.y()) <= half_size.y() + tolerance) &&
                   (std::abs(diff.z()) <= half_size.z() + tolerance);
        }

        bool GeometryManager::isLineIntersectBox(const EcefPoint& line_start, const EcefPoint& line_end,
                                                const EcefPoint& box_center, const Vector3D& box_dimensions,
                                                double tolerance) {
            // 首先检查线段端点是否在盒子内
            if (isPointInBox(line_start, box_center, box_dimensions, tolerance) ||
                isPointInBox(line_end, box_center, box_dimensions, tolerance)) {
                return true;
            }

            // 使用AABB线段相交算法
            Vector3D box_min = box_center.toVector3D() - box_dimensions / 2.0;
            Vector3D box_max = box_center.toVector3D() + box_dimensions / 2.0;
            Vector3D start = line_start.toVector3D();
            Vector3D end = line_end.toVector3D();
            Vector3D dir = end - start;

            double t_min = 0.0, t_max = 1.0;

            for (int i = 0; i < 3; ++i) {
                if (std::abs(dir[i]) < tolerance) {
                    // 线段平行于该轴
                    if (start[i] < box_min[i] || start[i] > box_max[i]) {
                        return false;
                    }
                } else {
                    double t1 = (box_min[i] - start[i]) / dir[i];
                    double t2 = (box_max[i] - start[i]) / dir[i];

                    if (t1 > t2) std::swap(t1, t2);

                    t_min = std::max(t_min, t1);
                    t_max = std::min(t_max, t2);

                    if (t_min > t_max) return false;
                }
            }

            return true;
        }

        std::vector<TriangleIndices> GeometryManager::triangulatePolygon2D(const std::vector<EcefPoint>& ecef_polygon) {
            std::vector<TriangleIndices> triangles;

            if (ecef_polygon.size() < 3) {
                return triangles;
            }

            // 简单的扇形三角剖分（适用于凸多边形）
            // 对于复杂多边形，应该使用专门的三角剖分库如earcut
            for (size_t i = 1; i < ecef_polygon.size() - 1; ++i) {
                TriangleIndices triangle;
                triangle.i0 = 0;
                triangle.i1 = i;
                triangle.i2 = i + 1;
                triangles.push_back(triangle);
            }

            return triangles;
        }



        // === 内部几何计算方法（基于ECEF坐标系） ===

        double GeometryManager::calculatePolygonAreaECEF(const std::vector<EcefPoint>& polygon_ecef) const {
            // 使用鞋带公式计算多边形面积（投影到XY平面）
            if (polygon_ecef.size() < 3) {
                return 0.0;
            }

            double area = 0.0;
            size_t n = polygon_ecef.size();

            for (size_t i = 0; i < n; ++i) {
                size_t j = (i + 1) % n;
                area += polygon_ecef[i].x() * polygon_ecef[j].y();
                area -= polygon_ecef[j].x() * polygon_ecef[i].y();
            }

            return std::abs(area) / 2.0;
        }

    } // namespace NSEnvironment
} // namespace NSDrones
