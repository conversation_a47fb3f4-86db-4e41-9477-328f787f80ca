// src/environment/geometry/geometry_manager.cpp
#include "environment/geometry/geometry_manager.h"
#include "environment/coordinate/coordinate_manager.h"
#include "utils/coordinate_converter.h"
#include "utils/logging.h"
#include <stdexcept>
#include <algorithm>
#include <cmath>
#include <functional>

// GeographicLib用于精确的地理计算
#include <GeographicLib/Geodesic.hpp>
#include <GeographicLib/LocalCartesian.hpp>

// Earcut用于三角剖分
#include <mapbox/earcut.hpp>

namespace NSDrones {
    namespace NSEnvironment {

        // === 构造函数和析构函数 ===

        GeometryManager::GeometryManager(std::shared_ptr<CoordinateManager> coord_manager)
            : coord_manager_(coord_manager) {

            if (!coord_manager_) {
                throw std::invalid_argument("几何管理器: 坐标系统管理器不能为空");
            }

            LOG_INFO("几何管理器: 初始化完成，使用GeographicLib进行高精度几何计算");
        }

        // === 1. 基础几何计算 ===

        double GeometryManager::calculateDistance(const WGS84Point& p1, const WGS84Point& p2) const {
            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double distance;
                geod.Inverse(p1.latitude, p1.longitude, p2.latitude, p2.longitude, distance);

                LOG_TRACE("几何管理器: 距离计算完成，{:.3f}米", distance);
                return distance;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 距离计算失败: {}", e.what());
                throw std::runtime_error("距离计算失败: " + std::string(e.what()));
            }
        }

        double GeometryManager::calculateBearing(const WGS84Point& from, const WGS84Point& to) const {
            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double distance, bearing;
                geod.Inverse(from.latitude, from.longitude, to.latitude, to.longitude, distance, bearing);

                // 将方位角转换为 0-360 度范围
                if (bearing < 0) {
                    bearing += 360.0;
                }

                LOG_TRACE("几何管理器: 方位角计算完成，{:.2f}度", bearing);
                return bearing;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 方位角计算失败: {}", e.what());
                throw std::runtime_error("方位角计算失败: " + std::string(e.what()));
            }
        }

        WGS84Point GeometryManager::calculateDestination(const WGS84Point& start, double bearing_deg, double distance_m) const {
            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double dest_lat, dest_lon;
                geod.Direct(start.latitude, start.longitude, bearing_deg, distance_m, dest_lat, dest_lon);

                WGS84Point destination;
                destination.latitude = dest_lat;
                destination.longitude = dest_lon;
                destination.altitude = start.altitude; // 保持相同高度

                LOG_TRACE("几何管理器: 目标点计算完成，({:.6f}, {:.6f})", dest_lat, dest_lon);
                return destination;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 目标点计算失败: {}", e.what());
                throw std::runtime_error("目标点计算失败: " + std::string(e.what()));
            }
        }

        std::vector<double> GeometryManager::batchCalculateDistance(
            const std::vector<WGS84Point>& points1,
            const std::vector<WGS84Point>& points2) const {

            if (points1.size() != points2.size()) {
                throw std::invalid_argument("几何管理器: 批量距离计算的两个点集大小不匹配");
            }

            std::vector<double> distances;
            distances.reserve(points1.size());

            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                
                for (size_t i = 0; i < points1.size(); ++i) {
                    double distance;
                    geod.Inverse(points1[i].latitude, points1[i].longitude, 
                               points2[i].latitude, points2[i].longitude, distance);
                    distances.push_back(distance);
                }

                LOG_DEBUG("几何管理器: 批量距离计算完成，处理了 {} 对点", points1.size());
                return distances;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 批量距离计算失败: {}", e.what());
                throw std::runtime_error("批量距离计算失败: " + std::string(e.what()));
            }
        }

        // === 私有公共方法（坐标转换和局部坐标系建立） ===

        std::vector<EcefPoint> GeometryManager::setupLocalCoordinateSystem(
            const std::vector<WGS84Point>& points,
            GeographicLib::LocalCartesian& local_cart) const {
            
            if (points.empty()) {
                throw std::invalid_argument("几何管理器: 点集为空，无法建立局部坐标系");
            }

            // 计算质心作为局部坐标系原点
            double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
            for (const auto& point : points) {
                center_lat += point.latitude;
                center_lon += point.longitude;
                center_alt += point.altitude;
            }
            center_lat /= points.size();
            center_lon /= points.size();
            center_alt /= points.size();

            // 建立局部坐标系
            local_cart = GeographicLib::LocalCartesian(center_lat, center_lon, center_alt);

            // 转换所有点到局部坐标系
            std::vector<EcefPoint> ecef_points;
            ecef_points.reserve(points.size());

            for (const auto& point : points) {
                double x, y, z;
                local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
                ecef_points.emplace_back(x, y, z);
            }

            return ecef_points;
        }

        EcefPoint GeometryManager::convertToLocalECEF(const WGS84Point& point,
            const GeographicLib::LocalCartesian& local_cart) const {
            
            double x, y, z;
            local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
            return EcefPoint(x, y, z);
        }

        WGS84Point GeometryManager::convertFromLocalECEF(const EcefPoint& ecef_point,
            const GeographicLib::LocalCartesian& local_cart) const {
            
            double lat, lon, alt;
            local_cart.Reverse(ecef_point.x(), ecef_point.y(), ecef_point.z(), lat, lon, alt);
            
            WGS84Point wgs84_point;
            wgs84_point.latitude = lat;
            wgs84_point.longitude = lon;
            wgs84_point.altitude = alt;
            return wgs84_point;
        }

        // === 2. 多边形几何 ===

        double GeometryManager::calculatePolygonArea(const std::vector<WGS84Point>& polygon) const {
            if (polygon.size() < 3) {
                throw std::invalid_argument("几何管理器: 多边形顶点数少于3，无法计算面积");
            }

            try {
                GeographicLib::LocalCartesian local_cart;
                std::vector<EcefPoint> ecef_polygon = setupLocalCoordinateSystem(polygon, local_cart);
                double area = calculatePolygonAreaECEF(ecef_polygon);

                LOG_DEBUG("几何管理器: 多边形面积计算完成，{:.2f}平方米", area);
                return area;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形面积计算失败: {}", e.what());
                throw std::runtime_error("多边形面积计算失败: " + std::string(e.what()));
            }
        }

        WGS84Point GeometryManager::calculatePolygonCentroid(const std::vector<WGS84Point>& polygon) const {
            if (polygon.empty()) {
                throw std::invalid_argument("几何管理器: 多边形为空，无法计算质心");
            }

            if (polygon.size() == 1) {
                return polygon[0];
            }

            if (polygon.size() == 2) {
                WGS84Point centroid;
                centroid.latitude = (polygon[0].latitude + polygon[1].latitude) / 2.0;
                centroid.longitude = (polygon[0].longitude + polygon[1].longitude) / 2.0;
                centroid.altitude = (polygon[0].altitude + polygon[1].altitude) / 2.0;
                return centroid;
            }

            try {
                GeographicLib::LocalCartesian local_cart;
                std::vector<EcefPoint> ecef_polygon = setupLocalCoordinateSystem(polygon, local_cart);
                EcefPoint ecef_centroid = calculatePolygonCentroidECEF(ecef_polygon);
                WGS84Point centroid = convertFromLocalECEF(ecef_centroid, local_cart);

                LOG_DEBUG("几何管理器: 多边形质心计算完成，({:.6f}, {:.6f}, {:.2f})",
                         centroid.latitude, centroid.longitude, centroid.altitude);
                return centroid;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形质心计算失败: {}", e.what());
                throw std::runtime_error("多边形质心计算失败: " + std::string(e.what()));
            }
        }

        bool GeometryManager::isPointInPolygon(const WGS84Point& point, const std::vector<WGS84Point>& polygon) const {
            if (polygon.size() < 3) {
                return false;
            }

            try {
                // 将点和多边形一起建立局部坐标系
                std::vector<WGS84Point> all_points = polygon;
                all_points.push_back(point);

                GeographicLib::LocalCartesian local_cart;
                std::vector<EcefPoint> ecef_points = setupLocalCoordinateSystem(all_points, local_cart);

                // 分离点和多边形
                EcefPoint ecef_point = ecef_points.back();
                ecef_points.pop_back();

                bool result = isPointInPolygonECEF(ecef_point, ecef_points, Constants::GEOMETRY_EPSILON);
                LOG_TRACE("几何管理器: 点在多边形内判断完成，结果: {}", result);
                return result;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 点在多边形内判断失败: {}", e.what());
                return false;
            }
        }

        bool GeometryManager::doPolygonsIntersect(const std::vector<WGS84Point>& poly1,
                                                 const std::vector<WGS84Point>& poly2) const {
            if (poly1.size() < 3 || poly2.size() < 3) {
                return false;
            }

            try {
                // 首先检查边界框是否相交
                WGS84BoundingBox bbox1 = calculateBoundingBox(poly1);
                WGS84BoundingBox bbox2 = calculateBoundingBox(poly2);

                if (!doBoundingBoxesIntersect(bbox1, bbox2)) {
                    return false;
                }

                // 建立局部坐标系
                std::vector<WGS84Point> all_points = poly1;
                all_points.insert(all_points.end(), poly2.begin(), poly2.end());

                GeographicLib::LocalCartesian local_cart;
                std::vector<EcefPoint> ecef_points = setupLocalCoordinateSystem(all_points, local_cart);

                // 分离两个多边形
                std::vector<EcefPoint> ecef_poly1(ecef_points.begin(), ecef_points.begin() + poly1.size());
                std::vector<EcefPoint> ecef_poly2(ecef_points.begin() + poly1.size(), ecef_points.end());

                // 检查是否有顶点在对方多边形内
                for (const auto& point : ecef_poly1) {
                    if (isPointInPolygonECEF(point, ecef_poly2)) {
                        return true;
                    }
                }

                for (const auto& point : ecef_poly2) {
                    if (isPointInPolygonECEF(point, ecef_poly1)) {
                        return true;
                    }
                }

                // 检查边是否相交
                for (size_t i = 0; i < ecef_poly1.size(); ++i) {
                    size_t next_i = (i + 1) % ecef_poly1.size();
                    for (size_t j = 0; j < ecef_poly2.size(); ++j) {
                        size_t next_j = (j + 1) % ecef_poly2.size();

                        if (isSegmentsIntersect2D(ecef_poly1[i], ecef_poly1[next_i],
                                                 ecef_poly2[j], ecef_poly2[next_j])) {
                            return true;
                        }
                    }
                }

                return false;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形相交检测失败: {}", e.what());
                return false;
            }
        }

        std::vector<WGS84Point> GeometryManager::simplifyPolygon(const std::vector<WGS84Point>& polygon,
                                                                double tolerance_m) const {
            if (polygon.size() <= 2) {
                return polygon;
            }

            try {
                std::vector<bool> keep_flags(polygon.size(), false);
                keep_flags[0] = true; // 保留起点
                keep_flags[polygon.size() - 1] = true; // 保留终点

                // 使用Douglas-Peucker算法
                douglasPeuckerRecursive(polygon, 0, polygon.size() - 1, tolerance_m, keep_flags);

                // 构建简化后的多边形
                std::vector<WGS84Point> simplified;
                for (size_t i = 0; i < polygon.size(); ++i) {
                    if (keep_flags[i]) {
                        simplified.push_back(polygon[i]);
                    }
                }

                LOG_DEBUG("几何管理器: 多边形简化完成，从{}个点简化为{}个点", polygon.size(), simplified.size());
                return simplified;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形简化失败: {}", e.what());
                return polygon;
            }
        }

        // === 3. 路径几何 ===

        double GeometryManager::calculatePathLength(const std::vector<WGS84Point>& waypoints) const {
            if (waypoints.size() < 2) {
                throw std::invalid_argument("几何管理器: 路径点少于2个，无法计算路径长度");
            }

            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();

                double total_length = 0.0;
                for (size_t i = 1; i < waypoints.size(); ++i) {
                    double distance;
                    geod.Inverse(waypoints[i-1].latitude, waypoints[i-1].longitude,
                               waypoints[i].latitude, waypoints[i].longitude, distance);
                    total_length += distance;
                }

                LOG_DEBUG("几何管理器: 路径长度计算完成，{:.2f}米", total_length);
                return total_length;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 路径长度计算失败: {}", e.what());
                throw std::runtime_error("路径长度计算失败: " + std::string(e.what()));
            }
        }

        double GeometryManager::calculatePointToLineDistance(
            const WGS84Point& point,
            const WGS84Point& line_start,
            const WGS84Point& line_end) const {

            try {
                // 建立局部坐标系
                std::vector<WGS84Point> all_points = {point, line_start, line_end};
                GeographicLib::LocalCartesian local_cart;
                std::vector<EcefPoint> ecef_points = setupLocalCoordinateSystem(all_points, local_cart);

                // 在ECEF坐标系中计算距离
                double distance = calculatePointToLineDistanceECEF(ecef_points[0], ecef_points[1], ecef_points[2]);

                LOG_TRACE("几何管理器: 点到线段距离计算完成，{:.3f}米", distance);
                return distance;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 点到线段距离计算失败: {}", e.what());
                throw std::runtime_error("点到线段距离计算失败: " + std::string(e.what()));
            }
        }

        std::vector<WGS84Point> GeometryManager::generateGreatCirclePath(const WGS84Point& start,
                                                                        const WGS84Point& end,
                                                                        double max_segment_length_m) const {
            try {
                std::vector<WGS84Point> path;

                // 计算总距离
                double total_distance = calculateDistance(start, end);
                if (total_distance <= max_segment_length_m) {
                    // 距离足够短，直接返回起点和终点
                    path.push_back(start);
                    path.push_back(end);
                    return path;
                }

                // 计算需要的分段数
                int num_segments = static_cast<int>(std::ceil(total_distance / max_segment_length_m));
                double segment_distance = total_distance / num_segments;

                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double bearing;
                geod.Inverse(start.latitude, start.longitude, end.latitude, end.longitude, total_distance, bearing);

                // 生成路径点
                path.reserve(num_segments + 1);
                path.push_back(start);

                for (int i = 1; i < num_segments; ++i) {
                    double distance = i * segment_distance;
                    double lat, lon;
                    geod.Direct(start.latitude, start.longitude, bearing, distance, lat, lon);

                    WGS84Point waypoint;
                    waypoint.latitude = lat;
                    waypoint.longitude = lon;
                    // 线性插值高度
                    double t = static_cast<double>(i) / num_segments;
                    waypoint.altitude = start.altitude + t * (end.altitude - start.altitude);

                    path.push_back(waypoint);
                }

                path.push_back(end);

                LOG_DEBUG("几何管理器: 大圆路径生成完成，{}个航点", path.size());
                return path;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 大圆路径生成失败: {}", e.what());
                throw std::runtime_error("大圆路径生成失败: " + std::string(e.what()));
            }
        }

        double GeometryManager::calculateAngleBetweenVectors(const WGS84Point& v1_start, const WGS84Point& v1_end,
                                                           const WGS84Point& v2_start, const WGS84Point& v2_end) const {
            try {
                // 建立局部坐标系
                std::vector<WGS84Point> all_points = {v1_start, v1_end, v2_start, v2_end};
                GeographicLib::LocalCartesian local_cart;
                std::vector<EcefPoint> ecef_points = setupLocalCoordinateSystem(all_points, local_cart);

                // 计算向量
                Vector3D vec1 = ecef_points[1].toVector3D() - ecef_points[0].toVector3D();
                Vector3D vec2 = ecef_points[3].toVector3D() - ecef_points[2].toVector3D();

                // 计算夹角
                double dot_product = vec1.dot(vec2);
                double magnitude1 = vec1.norm();
                double magnitude2 = vec2.norm();

                if (magnitude1 < 1e-12 || magnitude2 < 1e-12) {
                    return 0.0; // 零向量
                }

                double cos_angle = dot_product / (magnitude1 * magnitude2);
                cos_angle = std::max(-1.0, std::min(1.0, cos_angle)); // 限制在[-1, 1]范围内

                double angle = std::acos(cos_angle);

                LOG_TRACE("几何管理器: 向量夹角计算完成，{:.3f}弧度", angle);
                return angle;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 向量夹角计算失败: {}", e.what());
                throw std::runtime_error("向量夹角计算失败: " + std::string(e.what()));
            }
        }

        // === 4. 空间查询 ===

        std::vector<size_t> GeometryManager::findPointsInRadius(
            const WGS84Point& center,
            double radius_m,
            const std::vector<WGS84Point>& candidates) const {

            try {
                std::vector<size_t> result;

                if (candidates.empty()) {
                    return result;
                }

                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();

                // 查找半径内的点
                for (size_t i = 0; i < candidates.size(); ++i) {
                    double distance;
                    geod.Inverse(center.latitude, center.longitude,
                               candidates[i].latitude, candidates[i].longitude, distance);

                    if (distance <= radius_m) {
                        result.push_back(i);
                    }
                }

                LOG_DEBUG("几何管理器: 半径查询完成，{}个候选点中找到{}个", candidates.size(), result.size());
                return result;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 半径查询失败: {}", e.what());
                throw std::runtime_error("半径查询失败: " + std::string(e.what()));
            }
        }

        WGS84BoundingBox GeometryManager::calculateBoundingBox(const std::vector<WGS84Point>& points) const {
            if (points.empty()) {
                throw std::invalid_argument("几何管理器: 点集为空，无法计算边界框");
            }

            WGS84BoundingBox bbox;
            bbox.minLatitude = bbox.maxLatitude = points[0].latitude;
            bbox.minLongitude = bbox.maxLongitude = points[0].longitude;
            bbox.minAltitude = bbox.maxAltitude = points[0].altitude;

            for (const auto& point : points) {
                bbox.minLatitude = (std::min)(bbox.minLatitude, point.latitude);
                bbox.maxLatitude = (std::max)(bbox.maxLatitude, point.latitude);
                bbox.minLongitude = (std::min)(bbox.minLongitude, point.longitude);
                bbox.maxLongitude = (std::max)(bbox.maxLongitude, point.longitude);
                bbox.minAltitude = (std::min)(bbox.minAltitude, point.altitude);
                bbox.maxAltitude = (std::max)(bbox.maxAltitude, point.altitude);
            }

            LOG_DEBUG("几何管理器: 边界框计算完成");
            return bbox;
        }

        bool GeometryManager::doBoundingBoxesIntersect(const WGS84BoundingBox& box1, const WGS84BoundingBox& box2) const {
            return !(box1.maxLatitude < box2.minLatitude || box2.maxLatitude < box1.minLatitude ||
                     box1.maxLongitude < box2.minLongitude || box2.maxLongitude < box1.minLongitude ||
                     box1.maxAltitude < box2.minAltitude || box2.maxAltitude < box1.minAltitude);
        }

        // === 5. 高级几何计算 ===

        std::vector<TriangleIndices> GeometryManager::triangulatePolygon(
            const std::vector<WGS84Point>& polygon,
            const std::vector<std::vector<WGS84Point>>& holes) const {

            if (polygon.size() < 3) {
                throw std::invalid_argument("几何管理器: 多边形顶点数少于3，无法进行三角剖分");
            }

            try {
                GeographicLib::LocalCartesian local_cart;
                std::vector<EcefPoint> ecef_polygon = setupLocalCoordinateSystem(polygon, local_cart);

                // 使用静态方法进行三角剖分
                auto triangles = triangulatePolygon2D(ecef_polygon);

                LOG_DEBUG("几何管理器: 多边形三角剖分完成，生成{}个三角形", triangles.size());
                return triangles;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 多边形三角剖分失败: {}", e.what());
                throw std::runtime_error("多边形三角剖分失败: " + std::string(e.what()));
            }
        }

        std::vector<WGS84Point> GeometryManager::generateGridPoints(const WGS84BoundingBox& bounds,
                                                                   double spacing_m) const {
            if (spacing_m <= 0) {
                throw std::invalid_argument("几何管理器: 网格间距必须大于0");
            }

            try {
                std::vector<WGS84Point> grid_points;

                // 计算边界框中心作为参考点
                double center_lat = (bounds.minLatitude + bounds.maxLatitude) / 2.0;
                double center_lon = (bounds.minLongitude + bounds.maxLongitude) / 2.0;

                // 计算纬度和经度的步长
                double lat_step = spacing_m / 111320.0; // 大约每度111.32km
                double lon_step = spacing_m / (111320.0 * std::cos(center_lat * Constants::DEG_TO_RAD));

                // 生成网格点
                for (double lat = bounds.minLatitude; lat <= bounds.maxLatitude; lat += lat_step) {
                    for (double lon = bounds.minLongitude; lon <= bounds.maxLongitude; lon += lon_step) {
                        WGS84Point point;
                        point.latitude = lat;
                        point.longitude = lon;
                        point.altitude = (bounds.minAltitude + bounds.maxAltitude) / 2.0;
                        grid_points.push_back(point);
                    }
                }

                LOG_DEBUG("几何管理器: 网格点生成完成，生成{}个点", grid_points.size());
                return grid_points;

            } catch (const std::exception& e) {
                LOG_ERROR("几何管理器: 网格点生成失败: {}", e.what());
                throw std::runtime_error("网格点生成失败: " + std::string(e.what()));
            }
        }

        // === 私有算法实现方法 ===

        void GeometryManager::douglasPeuckerRecursive(const std::vector<WGS84Point>& points,
            int start, int end, double tolerance_m, std::vector<bool>& keep_flags) const {

            if (end - start <= 1) {
                return;
            }

            double max_distance = 0.0;
            int max_index = start;

            // 找到距离线段最远的点
            for (int i = start + 1; i < end; ++i) {
                double distance = calculatePointToLineDistance(points[i], points[start], points[end]);
                if (distance > max_distance) {
                    max_distance = distance;
                    max_index = i;
                }
            }

            // 如果最大距离超过容差，递归处理
            if (max_distance > tolerance_m) {
                keep_flags[max_index] = true;
                douglasPeuckerRecursive(points, start, max_index, tolerance_m, keep_flags);
                douglasPeuckerRecursive(points, max_index, end, tolerance_m, keep_flags);
            }
        }

        // === 私有几何计算方法（ECEF坐标系） ===

        double GeometryManager::calculatePolygonAreaECEF(const std::vector<EcefPoint>& polygon_ecef) const {
            if (polygon_ecef.size() < 3) {
                return 0.0;
            }

            // 使用鞋带公式计算多边形面积（投影到XY平面）
            double area = 0.0;
            size_t n = polygon_ecef.size();

            for (size_t i = 0; i < n; ++i) {
                size_t j = (i + 1) % n;
                area += polygon_ecef[i].x() * polygon_ecef[j].y();
                area -= polygon_ecef[j].x() * polygon_ecef[i].y();
            }

            return std::abs(area) / 2.0;
        }

        EcefPoint GeometryManager::calculatePolygonCentroidECEF(const std::vector<EcefPoint>& polygon_ecef) const {
            if (polygon_ecef.empty()) {
                throw std::invalid_argument("几何管理器: ECEF多边形为空，无法计算质心");
            }

            if (polygon_ecef.size() == 1) {
                return polygon_ecef[0];
            }

            if (polygon_ecef.size() == 2) {
                return EcefPoint(
                    (polygon_ecef[0].x() + polygon_ecef[1].x()) / 2.0,
                    (polygon_ecef[0].y() + polygon_ecef[1].y()) / 2.0,
                    (polygon_ecef[0].z() + polygon_ecef[1].z()) / 2.0
                );
            }

            // 计算面积加权质心
            double total_area = 0.0;
            double centroid_x = 0.0, centroid_y = 0.0, centroid_z = 0.0;

            // 使用三角剖分计算质心
            for (size_t i = 1; i < polygon_ecef.size() - 1; ++i) {
                const EcefPoint& p0 = polygon_ecef[0];
                const EcefPoint& p1 = polygon_ecef[i];
                const EcefPoint& p2 = polygon_ecef[i + 1];

                // 计算三角形面积（使用叉积）
                Vector3D v1 = p1.toVector3D() - p0.toVector3D();
                Vector3D v2 = p2.toVector3D() - p0.toVector3D();
                double triangle_area = 0.5 * v1.cross(v2).norm();

                if (triangle_area > 1e-12) {
                    // 三角形质心
                    double tri_cx = (p0.x() + p1.x() + p2.x()) / 3.0;
                    double tri_cy = (p0.y() + p1.y() + p2.y()) / 3.0;
                    double tri_cz = (p0.z() + p1.z() + p2.z()) / 3.0;

                    // 面积加权累加
                    centroid_x += triangle_area * tri_cx;
                    centroid_y += triangle_area * tri_cy;
                    centroid_z += triangle_area * tri_cz;
                    total_area += triangle_area;
                }
            }

            if (total_area < 1e-12) {
                // 退化情况，返回几何中心
                for (const auto& point : polygon_ecef) {
                    centroid_x += point.x();
                    centroid_y += point.y();
                    centroid_z += point.z();
                }
                centroid_x /= polygon_ecef.size();
                centroid_y /= polygon_ecef.size();
                centroid_z /= polygon_ecef.size();
            } else {
                // 面积加权质心
                centroid_x /= total_area;
                centroid_y /= total_area;
                centroid_z /= total_area;
            }

            return EcefPoint(centroid_x, centroid_y, centroid_z);
        }

        double GeometryManager::calculatePointToLineDistanceECEF(
            const EcefPoint& point_ecef,
            const EcefPoint& line_start_ecef,
            const EcefPoint& line_end_ecef) const {

            Vector3D line_vec = line_end_ecef.toVector3D() - line_start_ecef.toVector3D();
            Vector3D point_vec = point_ecef.toVector3D() - line_start_ecef.toVector3D();

            double line_length_sq = line_vec.squaredNorm();
            if (line_length_sq < 1e-12) {
                // 线段退化为点
                return (point_ecef.toVector3D() - line_start_ecef.toVector3D()).norm();
            }

            double t = point_vec.dot(line_vec) / line_length_sq;
            t = std::max(0.0, std::min(1.0, t));  // 限制在线段范围内

            Vector3D closest_point = line_start_ecef.toVector3D() + t * line_vec;
            return (point_ecef.toVector3D() - closest_point).norm();
        }

        // === 6. 碰撞检测几何（ECEF坐标系静态方法） ===

        bool GeometryManager::isPointInPolygonECEF(const EcefPoint& ecef_point,
                                                  const std::vector<EcefPoint>& ecef_polygon,
                                                  double tolerance) {
            if (ecef_polygon.size() < 3) {
                return false;
            }

            // 使用射线法判断点是否在多边形内（基于XY投影）
            bool inside = false;
            size_t n = ecef_polygon.size();

            for (size_t i = 0, j = n - 1; i < n; j = i++) {
                double xi = ecef_polygon[i].x(), yi = ecef_polygon[i].y();
                double xj = ecef_polygon[j].x(), yj = ecef_polygon[j].y();

                if (((yi > ecef_point.y()) != (yj > ecef_point.y())) &&
                    (ecef_point.x() < (xj - xi) * (ecef_point.y() - yi) / (yj - yi) + xi)) {
                    inside = !inside;
                }
            }

            return inside;
        }

        bool GeometryManager::isSegmentsIntersect2D(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2,
                                                   const EcefPoint& ecef_p3, const EcefPoint& ecef_p4,
                                                   bool include_endpoints, double tolerance) {
            // 使用向量叉积判断线段相交
            auto cross_product = [](double ax, double ay, double bx, double by) -> double {
                return ax * by - ay * bx;
            };

            double x1 = ecef_p1.x(), y1 = ecef_p1.y();
            double x2 = ecef_p2.x(), y2 = ecef_p2.y();
            double x3 = ecef_p3.x(), y3 = ecef_p3.y();
            double x4 = ecef_p4.x(), y4 = ecef_p4.y();

            double d1 = cross_product(x4 - x3, y4 - y3, x1 - x3, y1 - y3);
            double d2 = cross_product(x4 - x3, y4 - y3, x2 - x3, y2 - y3);
            double d3 = cross_product(x2 - x1, y2 - y1, x3 - x1, y3 - y1);
            double d4 = cross_product(x2 - x1, y2 - y1, x4 - x1, y4 - y1);

            if (((d1 > tolerance && d2 < -tolerance) || (d1 < -tolerance && d2 > tolerance)) &&
                ((d3 > tolerance && d4 < -tolerance) || (d3 < -tolerance && d4 > tolerance))) {
                return true;
            }

            // 检查端点是否在线段上（如果需要）
            if (include_endpoints) {
                auto point_on_segment = [tolerance](double px, double py, double x1, double y1, double x2, double y2) -> bool {
                    double cross = (py - y1) * (x2 - x1) - (px - x1) * (y2 - y1);
                    if (std::abs(cross) > tolerance) return false;

                    double dot = (px - x1) * (x2 - x1) + (py - y1) * (y2 - y1);
                    double len_sq = (x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1);
                    return dot >= -tolerance && dot <= len_sq + tolerance;
                };

                return point_on_segment(x1, y1, x3, y3, x4, y4) ||
                       point_on_segment(x2, y2, x3, y3, x4, y4) ||
                       point_on_segment(x3, y3, x1, y1, x2, y2) ||
                       point_on_segment(x4, y4, x1, y1, x2, y2);
            }

            return false;
        }

        bool GeometryManager::isPointInSphere(const EcefPoint& ecef_point, const EcefPoint& sphere_center,
                                             double radius, double tolerance) {
            Vector3D diff = ecef_point.toVector3D() - sphere_center.toVector3D();
            double distance = diff.norm();
            return distance <= (radius + tolerance);
        }

        bool GeometryManager::isPointInBox(const EcefPoint& ecef_point, const EcefPoint& box_center,
                                          const Vector3D& box_dimensions, double tolerance) {
            Vector3D diff = ecef_point.toVector3D() - box_center.toVector3D();
            Vector3D half_size = box_dimensions / 2.0;

            return (std::abs(diff.x()) <= half_size.x() + tolerance) &&
                   (std::abs(diff.y()) <= half_size.y() + tolerance) &&
                   (std::abs(diff.z()) <= half_size.z() + tolerance);
        }

        bool GeometryManager::isLineIntersectBox(const EcefPoint& line_start, const EcefPoint& line_end,
                                                const EcefPoint& box_center, const Vector3D& box_dimensions,
                                                double tolerance) {
            // 首先检查线段端点是否在盒子内
            if (isPointInBox(line_start, box_center, box_dimensions, tolerance) ||
                isPointInBox(line_end, box_center, box_dimensions, tolerance)) {
                return true;
            }

            // 使用AABB线段相交算法
            Vector3D box_min = box_center.toVector3D() - box_dimensions / 2.0;
            Vector3D box_max = box_center.toVector3D() + box_dimensions / 2.0;
            Vector3D start = line_start.toVector3D();
            Vector3D end = line_end.toVector3D();
            Vector3D dir = end - start;

            double t_min = 0.0, t_max = 1.0;

            for (int i = 0; i < 3; ++i) {
                if (std::abs(dir[i]) < tolerance) {
                    // 线段平行于该轴
                    if (start[i] < box_min[i] || start[i] > box_max[i]) {
                        return false;
                    }
                } else {
                    double t1 = (box_min[i] - start[i]) / dir[i];
                    double t2 = (box_max[i] - start[i]) / dir[i];

                    if (t1 > t2) std::swap(t1, t2);

                    t_min = std::max(t_min, t1);
                    t_max = std::min(t_max, t2);

                    if (t_min > t_max) return false;
                }
            }

            return true;
        }

        std::vector<TriangleIndices> GeometryManager::triangulatePolygon2D(const std::vector<EcefPoint>& ecef_polygon) {
            std::vector<TriangleIndices> triangles;

            if (ecef_polygon.size() < 3) {
                return triangles;
            }

            try {
                // 使用earcut进行三角剖分
                // earcut需要的数据格式：vector<vector<array<double, 2>>>
                using Point = std::array<double, 2>;
                using Polygon = std::vector<std::vector<Point>>;

                Polygon polygon;
                std::vector<Point> outer_ring;
                outer_ring.reserve(ecef_polygon.size());

                // 将ECEF点转换为2D点（投影到XY平面）
                for (const auto& ecef_point : ecef_polygon) {
                    outer_ring.push_back({ecef_point.x(), ecef_point.y()});
                }

                polygon.push_back(outer_ring);

                // 执行三角剖分
                std::vector<uint32_t> indices = mapbox::earcut<uint32_t>(polygon);

                // 转换结果为TriangleIndices格式
                triangles.reserve(indices.size() / 3);
                for (size_t i = 0; i < indices.size(); i += 3) {
                    TriangleIndices triangle;
                    triangle.v1 = indices[i];
                    triangle.v2 = indices[i + 1];
                    triangle.v3 = indices[i + 2];
                    triangles.push_back(triangle);
                }

            } catch (const std::exception& e) {
                // 回退到简单的扇形三角剖分
                triangles.clear();
                for (size_t i = 1; i < ecef_polygon.size() - 1; ++i) {
                    TriangleIndices triangle;
                    triangle.v1 = 0;
                    triangle.v2 = i;
                    triangle.v3 = i + 1;
                    triangles.push_back(triangle);
                }
            }

            return triangles;
        }

    } // namespace NSEnvironment
} // namespace NSDrones
