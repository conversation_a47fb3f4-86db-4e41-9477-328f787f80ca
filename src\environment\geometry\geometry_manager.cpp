// src/environment/geometry/geometry_manager_new.cpp
#include "environment/geometry/geometry_manager.h"
#include "environment/coordinate/coordinate_manager.h"
#include "utils/coordinate_converter.h"
#include "utils/logging.h"
#include <stdexcept>
#include <algorithm>
#include <cmath>
#include <array>

// 确保M_PI定义可用
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// --- GeographicLib for precise geographic calculations ---
#include <GeographicLib/Geodesic.hpp>
#include <GeographicLib/LocalCartesian.hpp>

// --- Earcut for triangulation ---
// 注意：earcut.hpp 路径可能需要根据实际安装位置调整
// #include <earcut.hpp>

namespace NSDrones {
    namespace NSEnvironment {

        GeometryManager::GeometryManager(std::shared_ptr<CoordinateManager> coord_manager)
            : coord_manager_(coord_manager) {

            if (!coord_manager_) {
                throw std::invalid_argument("GeometryManager: 坐标系统管理器不能为空");
            }

            LOG_INFO("GeometryManager: 初始化完成，使用CoordinateManager");
        }

        //=== 距离计算 ===//

        double GeometryManager::calculateDistance(const WGS84Point& p1, const WGS84Point& p2) const {
            try {
                // 转换为ECEF坐标进行内部几何计算
                EcefPoint ecef_p1 = NSUtils::CoordinateConverter::wgs84ToECEF(p1);
                EcefPoint ecef_p2 = NSUtils::CoordinateConverter::wgs84ToECEF(p2);

                // 在ECEF坐标系中计算欧几里得距离
                Vector3D diff = ecef_p2 - ecef_p1;
                double distance = diff.norm();

                LOG_TRACE("GeometryManager: ECEF距离计算完成，{:.3f}米", distance);
                return distance;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 距离计算失败: {}", e.what());
                throw std::runtime_error("距离计算失败: " + std::string(e.what()));
            }
        }

        std::vector<double> GeometryManager::batchCalculateDistance(
            const std::vector<WGS84Point>& points1,
            const std::vector<WGS84Point>& points2) const {

            if (points1.size() != points2.size()) {
                throw std::invalid_argument("GeometryManager: 点集大小不匹配");
            }

            try {
                // 批量转换为ECEF坐标进行内部计算
                std::vector<EcefPoint> ecef_points1, ecef_points2;
                ecef_points1.reserve(points1.size());
                ecef_points2.reserve(points2.size());

                for (size_t i = 0; i < points1.size(); ++i) {
                    ecef_points1.push_back(NSUtils::CoordinateConverter::wgs84ToECEF(points1[i]));
                    ecef_points2.push_back(NSUtils::CoordinateConverter::wgs84ToECEF(points2[i]));
                }

                // 在ECEF坐标系中批量计算距离
                std::vector<double> distances;
                distances.reserve(points1.size());

                for (size_t i = 0; i < ecef_points1.size(); ++i) {
                    Vector3D diff = ecef_points2[i] - ecef_points1[i];
                    distances.push_back(diff.norm());
                }

                LOG_DEBUG("GeometryManager: 批量ECEF距离计算完成，{}对点", points1.size());
                return distances;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 批量距离计算失败: {}", e.what());
                throw std::runtime_error("批量距离计算失败: " + std::string(e.what()));
            }
        }

        //=== 半径查询 ===//

        std::vector<size_t> GeometryManager::findPointsInRadius(
            const WGS84Point& center,
            double radius_m,
            const std::vector<WGS84Point>& candidates) const {

            try {
                std::vector<size_t> result;

                if (candidates.empty()) {
                    return result;
                }

                // 转换中心点为ECEF坐标进行内部计算
                EcefPoint ecef_center = NSUtils::CoordinateConverter::wgs84ToECEF(center);

                // 查找半径内的点
                for (size_t i = 0; i < candidates.size(); ++i) {
                    EcefPoint ecef_candidate = NSUtils::CoordinateConverter::wgs84ToECEF(candidates[i]);
                    Vector3D diff = ecef_candidate - ecef_center;
                    double distance = diff.norm();

                    if (distance <= radius_m) {
                        result.push_back(i);
                    }
                }

                LOG_DEBUG("GeometryManager: ECEF半径查询完成，{}个候选点中找到{}个", candidates.size(), result.size());
                return result;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 半径查询失败: {}", e.what());
                throw std::runtime_error("半径查询失败: " + std::string(e.what()));
            }
        }

        //=== 面积计算 ===//

        double GeometryManager::calculatePolygonArea(const std::vector<WGS84Point>& polygon) const {
            if (polygon.size() < 3) {
                LOG_WARN("GeometryManager: 多边形顶点数少于3，无法计算面积");
                return 0.0;
            }

            try {
                // 使用 GeographicLib 进行精确的坐标转换和面积计算

                // 计算多边形的质心作为局部坐标系原点
                double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
                for (const auto& point : polygon) {
                    center_lat += point.latitude;
                    center_lon += point.longitude;
                    center_alt += point.altitude;
                }
                center_lat /= polygon.size();
                center_lon /= polygon.size();
                center_alt /= polygon.size();

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 将WGS84坐标转换到局部笛卡尔坐标系
                std::vector<EcefPoint> local_polygon;
                local_polygon.reserve(polygon.size());

                for (const auto& point : polygon) {
                    double x, y, z;
                    local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
                    local_polygon.emplace_back(x, y, z);
                }

                // 在ECEF坐标系中计算面积（使用局部坐标近似）
                double area = calculatePolygonAreaECEF(local_polygon);

                LOG_DEBUG("GeometryManager: 多边形面积计算完成，{:.2f}平方米", area);
                return area;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 多边形面积计算失败: {}", e.what());
                throw std::runtime_error("多边形面积计算失败: " + std::string(e.what()));
            }
        }

        //=== 几何关系判断 ===//

        bool GeometryManager::isPointInPolygon(const WGS84Point& point, const std::vector<WGS84Point>& polygon) const {
            if (polygon.size() < 3) {
                return false;
            }

            try {
                // 计算多边形的质心作为局部坐标系原点
                double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
                for (const auto& p : polygon) {
                    center_lat += p.latitude;
                    center_lon += p.longitude;
                    center_alt += p.altitude;
                }
                center_lat /= polygon.size();
                center_lon /= polygon.size();
                center_alt /= polygon.size();

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换点到局部坐标系（使用ECEF坐标进行内部计算）
                double px, py, pz;
                local_cart.Forward(point.latitude, point.longitude, point.altitude, px, py, pz);
                EcefPoint point_ecef(px, py, pz);

                // 转换多边形到局部坐标系
                std::vector<EcefPoint> polygon_ecef;
                polygon_ecef.reserve(polygon.size());
                for (const auto& p : polygon) {
                    double x, y, z;
                    local_cart.Forward(p.latitude, p.longitude, p.altitude, x, y, z);
                    polygon_ecef.emplace_back(x, y, z);
                }

                // 在ECEF坐标系中进行判断（使用静态方法版本，指定容差）
                bool result = GeometryManager::isPointInPolygonECEF(point_ecef, polygon_ecef, 1e-6);

                LOG_TRACE("GeometryManager: 点在多边形内判断完成，结果: {}", result);
                return result;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 点在多边形内判断失败: {}", e.what());
                return false;
            }
        }

        bool GeometryManager::doBoundingBoxesIntersect(const WGS84BoundingBox& box1, const WGS84BoundingBox& box2) const {
            // 简单的边界框相交检测（在WGS84坐标系中）
            return !(box1.maxLatitude < box2.minLatitude || box2.maxLatitude < box1.minLatitude ||
                     box1.maxLongitude < box2.minLongitude || box2.maxLongitude < box1.minLongitude);
        }

        //=== 路径和轨迹分析 ===//

        double GeometryManager::calculatePathLength(const std::vector<WGS84Point>& waypoints) const {
            if (waypoints.size() < 2) {
                return 0.0;
            }

            try {
                // 批量转换为ECEF坐标进行内部计算
                std::vector<EcefPoint> ecef_waypoints;
                ecef_waypoints.reserve(waypoints.size());

                for (const auto& wp : waypoints) {
                    ecef_waypoints.push_back(NSUtils::CoordinateConverter::wgs84ToECEF(wp));
                }

                // 在ECEF坐标系中计算相邻点之间的距离
                double total_length = 0.0;
                for (size_t i = 1; i < ecef_waypoints.size(); ++i) {
                    Vector3D diff = ecef_waypoints[i] - ecef_waypoints[i-1];
                    total_length += diff.norm();
                }

                LOG_DEBUG("GeometryManager: ECEF路径长度计算完成，{:.2f}米", total_length);
                return total_length;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 路径长度计算失败: {}", e.what());
                throw std::runtime_error("路径长度计算失败: " + std::string(e.what()));
            }
        }

        double GeometryManager::calculatePointToLineDistance(
            const WGS84Point& point,
            const WGS84Point& line_start,
            const WGS84Point& line_end) const {

            try {
                // 计算三个点的质心作为局部坐标系原点
                double center_lat = (point.latitude + line_start.latitude + line_end.latitude) / 3.0;
                double center_lon = (point.longitude + line_start.longitude + line_end.longitude) / 3.0;
                double center_alt = (point.altitude + line_start.altitude + line_end.altitude) / 3.0;

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换到局部坐标系（实际上是ECEF坐标系的局部近似）
                double px, py, pz, sx, sy, sz, ex, ey, ez;
                local_cart.Forward(point.latitude, point.longitude, point.altitude, px, py, pz);
                local_cart.Forward(line_start.latitude, line_start.longitude, line_start.altitude, sx, sy, sz);
                local_cart.Forward(line_end.latitude, line_end.longitude, line_end.altitude, ex, ey, ez);

                EcefPoint point_ecef(px, py, pz);
                EcefPoint line_start_ecef(sx, sy, sz);
                EcefPoint line_end_ecef(ex, ey, ez);

                // 在ECEF坐标系中计算距离
                double distance = calculatePointToLineDistanceECEF(point_ecef, line_start_ecef, line_end_ecef);

                LOG_TRACE("GeometryManager: 点到线段距离计算完成，{:.3f}米", distance);
                return distance;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 点到线段距离计算失败: {}", e.what());
                throw std::runtime_error("点到线段距离计算失败: " + std::string(e.what()));
            }
        }

        //=== 内部辅助方法 ===//

        std::string GeometryManager::selectOptimalUsageHint(const std::vector<WGS84Point>& points) const {
            if (points.empty()) {
                return "general";
            }
            
            // 估算数据范围
            double range_km = estimateDataRange(points);
            
            // 根据范围选择最优使用提示
            if (range_km < 0.01) return "collision";     // <10m: 碰撞检测精度
            if (range_km < 0.1) return "terrain";        // <100m: 地形查询精度
            if (range_km < 1.0) return "path";           // <1km: 路径规划精度
            
            return "general";  // 默认精度
        }

        double GeometryManager::estimateDataRange(const std::vector<WGS84Point>& points) const {
            if (points.size() < 2) {
                return 0.0;
            }

            // 计算边界框
            double min_lat = points[0].latitude, max_lat = points[0].latitude;
            double min_lon = points[0].longitude, max_lon = points[0].longitude;

            for (const auto& point : points) {
                min_lat = std::min(min_lat, point.latitude);
                max_lat = std::max(max_lat, point.latitude);
                min_lon = std::min(min_lon, point.longitude);
                max_lon = std::max(max_lon, point.longitude);
            }

            // 使用 GeographicLib 计算边界框对角线的精确距离
            const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
            double diagonal_distance;
            geod.Inverse(min_lat, min_lon, max_lat, max_lon, diagonal_distance);

            // 转换为公里
            return diagonal_distance / 1000.0;
        }

        //=== 内部几何计算方法（基于ECEF坐标系） ===//

        double GeometryManager::calculatePolygonAreaECEF(const std::vector<EcefPoint>& polygon_ecef) const {
            // 使用鞋带公式计算多边形面积（投影到XY平面）
            // 注意：对于ECEF坐标，这是一个近似计算，适用于局部区域
            if (polygon_ecef.size() < 3) {
                return 0.0;
            }

            double area = 0.0;
            size_t n = polygon_ecef.size();

            for (size_t i = 0; i < n; ++i) {
                size_t j = (i + 1) % n;
                area += polygon_ecef[i].x() * polygon_ecef[j].y();
                area -= polygon_ecef[j].x() * polygon_ecef[i].y();
            }

            return std::abs(area) / 2.0;
        }

        bool GeometryManager::isPointInPolygonECEF(const EcefPoint& point_ecef, const std::vector<EcefPoint>& polygon_ecef) const {
            // 使用射线法判断点是否在多边形内（基于XY投影）
            // 注意：对于ECEF坐标，这是一个近似计算，适用于局部区域
            if (polygon_ecef.size() < 3) {
                return false;
            }

            bool inside = false;
            size_t n = polygon_ecef.size();

            for (size_t i = 0, j = n - 1; i < n; j = i++) {
                double xi = polygon_ecef[i].x(), yi = polygon_ecef[i].y();
                double xj = polygon_ecef[j].x(), yj = polygon_ecef[j].y();

                if (((yi > point_ecef.y()) != (yj > point_ecef.y())) &&
                    (point_ecef.x() < (xj - xi) * (point_ecef.y() - yi) / (yj - yi) + xi)) {
                    inside = !inside;
                }
            }

            return inside;
        }

        double GeometryManager::calculatePointToLineDistanceECEF(
            const EcefPoint& point_ecef,
            const EcefPoint& line_start_ecef,
            const EcefPoint& line_end_ecef) const {

            Vector3D line_vec = line_end_ecef.toVector3D() - line_start_ecef.toVector3D();
            Vector3D point_vec = point_ecef.toVector3D() - line_start_ecef.toVector3D();

            double line_length_sq = line_vec.squaredNorm();
            if (line_length_sq < 1e-12) {
                // 线段退化为点
                return (point_ecef.toVector3D() - line_start_ecef.toVector3D()).norm();
            }

            double t = point_vec.dot(line_vec) / line_length_sq;
            t = std::max(0.0, std::min(1.0, t));  // 限制在线段范围内

            Vector3D closest_point = line_start_ecef.toVector3D() + t * line_vec;
            return (point_ecef.toVector3D() - closest_point).norm();
        }

        //=== 基于 GeographicLib 的高级几何计算方法 ===//

        double GeometryManager::calculateBearing(const WGS84Point& from, const WGS84Point& to) const {
            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double distance, bearing;
                geod.Inverse(from.latitude, from.longitude, to.latitude, to.longitude, distance, bearing);

                // 将方位角转换为 0-360 度范围
                if (bearing < 0) {
                    bearing += 360.0;
                }

                LOG_TRACE("GeometryManager: 方位角计算完成，{:.2f}度", bearing);
                return bearing;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 方位角计算失败: {}", e.what());
                throw std::runtime_error("方位角计算失败: " + std::string(e.what()));
            }
        }

        WGS84Point GeometryManager::calculateDestination(const WGS84Point& start, double bearing_deg, double distance_m) const {
            try {
                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double dest_lat, dest_lon;
                geod.Direct(start.latitude, start.longitude, bearing_deg, distance_m, dest_lat, dest_lon);

                WGS84Point destination;
                destination.latitude = dest_lat;
                destination.longitude = dest_lon;
                destination.altitude = start.altitude; // 保持相同高度

                LOG_TRACE("GeometryManager: 目标点计算完成，({:.6f}, {:.6f})", dest_lat, dest_lon);
                return destination;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 目标点计算失败: {}", e.what());
                throw std::runtime_error("目标点计算失败: " + std::string(e.what()));
            }
        }

        std::vector<WGS84Point> GeometryManager::generateGreatCirclePath(
            const WGS84Point& start,
            const WGS84Point& end,
            double max_segment_length_m) const {

            try {
                std::vector<WGS84Point> path;

                // 计算总距离
                double total_distance = calculateDistance(start, end);
                if (total_distance <= max_segment_length_m) {
                    // 距离足够短，直接返回起点和终点
                    path.push_back(start);
                    path.push_back(end);
                    return path;
                }

                // 计算需要的分段数
                int num_segments = static_cast<int>(std::ceil(total_distance / max_segment_length_m));
                double segment_distance = total_distance / num_segments;

                const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                double bearing;
                geod.Inverse(start.latitude, start.longitude, end.latitude, end.longitude, total_distance, bearing);

                // 生成路径点
                path.reserve(num_segments + 1);
                path.push_back(start);

                for (int i = 1; i < num_segments; ++i) {
                    double distance = i * segment_distance;
                    double lat, lon;
                    geod.Direct(start.latitude, start.longitude, bearing, distance, lat, lon);

                    WGS84Point waypoint;
                    waypoint.latitude = lat;
                    waypoint.longitude = lon;
                    // 线性插值高度
                    double t = static_cast<double>(i) / num_segments;
                    waypoint.altitude = start.altitude + t * (end.altitude - start.altitude);

                    path.push_back(waypoint);
                }

                path.push_back(end);

                LOG_DEBUG("GeometryManager: 大圆路径生成完成，{}个航点", path.size());
                return path;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 大圆路径生成失败: {}", e.what());
                throw std::runtime_error("大圆路径生成失败: " + std::string(e.what()));
            }
        }

        WGS84Point GeometryManager::calculatePolygonCentroid(const std::vector<WGS84Point>& polygon) const {
            if (polygon.empty()) {
                throw std::invalid_argument("GeometryManager: 多边形为空，无法计算质心");
            }

            if (polygon.size() == 1) {
                return polygon[0];
            }

            try {
                // 计算几何中心作为局部坐标系原点
                double center_lat = 0.0, center_lon = 0.0, center_alt = 0.0;
                for (const auto& point : polygon) {
                    center_lat += point.latitude;
                    center_lon += point.longitude;
                    center_alt += point.altitude;
                }
                center_lat /= polygon.size();
                center_lon /= polygon.size();
                center_alt /= polygon.size();

                // 使用 GeographicLib 的 LocalCartesian 进行精确的坐标转换
                GeographicLib::LocalCartesian local_cart(center_lat, center_lon, center_alt);

                // 转换到局部坐标系（使用ECEF坐标进行内部计算）
                std::vector<EcefPoint> local_polygon;
                local_polygon.reserve(polygon.size());
                for (const auto& point : polygon) {
                    double x, y, z;
                    local_cart.Forward(point.latitude, point.longitude, point.altitude, x, y, z);
                    local_polygon.emplace_back(x, y, z);
                }

                // 计算局部坐标系中的质心（使用ECEF坐标系方法）
                EcefPoint local_centroid = calculatePolygonCentroidECEF(local_polygon);

                // 转换回 WGS84 坐标
                double centroid_lat, centroid_lon, centroid_alt;
                local_cart.Reverse(local_centroid.x(), local_centroid.y(), local_centroid.z(),
                                  centroid_lat, centroid_lon, centroid_alt);

                WGS84Point centroid;
                centroid.latitude = centroid_lat;
                centroid.longitude = centroid_lon;
                centroid.altitude = centroid_alt;

                LOG_DEBUG("GeometryManager: 多边形质心计算完成，({:.6f}, {:.6f}, {:.2f})",
                         centroid_lat, centroid_lon, centroid_alt);
                return centroid;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 多边形质心计算失败: {}", e.what());
                throw std::runtime_error("多边形质心计算失败: " + std::string(e.what()));
            }
        }

        WGS84BoundingBox GeometryManager::calculateBoundingBox(const std::vector<WGS84Point>& points) const {
            if (points.empty()) {
                throw std::invalid_argument("GeometryManager: 点集为空，无法计算边界框");
            }

            WGS84BoundingBox bbox;
            bbox.minLatitude = bbox.maxLatitude = points[0].latitude;
            bbox.minLongitude = bbox.maxLongitude = points[0].longitude;
            bbox.minAltitude = bbox.maxAltitude = points[0].altitude;

            for (const auto& point : points) {
                bbox.minLatitude = (std::min)(bbox.minLatitude, point.latitude);
                bbox.maxLatitude = (std::max)(bbox.maxLatitude, point.latitude);
                bbox.minLongitude = (std::min)(bbox.minLongitude, point.longitude);
                bbox.maxLongitude = (std::max)(bbox.maxLongitude, point.longitude);
            }

            LOG_DEBUG("GeometryManager: 边界框计算完成，纬度: [{:.6f}, {:.6f}], 经度: [{:.6f}, {:.6f}]",
                     bbox.minLatitude, bbox.maxLatitude, bbox.minLongitude, bbox.maxLongitude);
            return bbox;
        }

        //=== 静态几何工具方法（局部坐标） ===//

        bool GeometryManager::isPointInPolygonECEF(const EcefPoint& ecef_point,
                                                  const std::vector<EcefPoint>& ecef_polygon,
                                                  double tolerance) {
            if (ecef_polygon.size() < 3) {
                return false; // 不是有效的多边形
            }

            // 使用射线投射算法（Ray Casting Algorithm）在ECEF坐标系的XY平面上
            // 从点向右发射一条射线，计算与多边形边的交点数
            // 如果交点数为奇数，则点在多边形内部

            int intersections = 0;
            size_t n = ecef_polygon.size();

            for (size_t i = 0; i < n; ++i) {
                size_t j = (i + 1) % n;

                const EcefPoint& vi = ecef_polygon[i];
                const EcefPoint& vj = ecef_polygon[j];

                // 检查射线是否与边相交（在XY平面上投影）
                // 射线方向：从点向右（y坐标相同，x坐标增大）
                if (((vi.y() > ecef_point.y()) != (vj.y() > ecef_point.y())) &&
                    (ecef_point.x() < (vj.x() - vi.x()) * (ecef_point.y() - vi.y()) / (vj.y() - vi.y()) + vi.x())) {
                    intersections++;
                }
            }

            return (intersections % 2) == 1;
        }

        bool GeometryManager::isSegmentsIntersect2D(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2,
                                                   const EcefPoint& ecef_p3, const EcefPoint& ecef_p4,
                                                   bool include_endpoints, double tolerance) {
            // 计算线段的方向向量（在XY平面上投影）
            double dx1 = ecef_p2.x() - ecef_p1.x();
            double dy1 = ecef_p2.y() - ecef_p1.y();
            double dx2 = ecef_p4.x() - ecef_p3.x();
            double dy2 = ecef_p4.y() - ecef_p3.y();

            // 计算行列式
            double det = dx1 * dy2 - dy1 * dx2;

            // 如果行列式接近零，线段平行
            if (std::abs(det) < tolerance) {
                return false; // 简化处理：平行线段不相交
            }

            // 计算参数
            double dx3 = ecef_p1.x() - ecef_p3.x();
            double dy3 = ecef_p1.y() - ecef_p3.y();

            double t = (dx2 * dy3 - dy2 * dx3) / det;
            double u = (dx1 * dy3 - dy1 * dx3) / det;

            // 检查交点是否在两条线段上
            if (include_endpoints) {
                return (t >= -tolerance && t <= 1.0 + tolerance) &&
                       (u >= -tolerance && u <= 1.0 + tolerance);
            } else {
                return (t > tolerance && t < 1.0 - tolerance) &&
                       (u > tolerance && u < 1.0 - tolerance);
            }
        }

        //=== 形状几何检测方法实现 ===//

        bool GeometryManager::isPointInSphere(const EcefPoint& ecef_point, const EcefPoint& sphere_center,
                                             double radius, double tolerance) {
            Vector3D diff(ecef_point.x() - sphere_center.x(),
                                 ecef_point.y() - sphere_center.y(),
                                 ecef_point.z() - sphere_center.z());
            double distance = diff.norm();
            return distance <= (radius + tolerance);
        }

        bool GeometryManager::isPointInBox(const EcefPoint& ecef_point, const EcefPoint& box_center,
                                          const Vector3D& box_dimensions, double tolerance) {
            Vector3D half_dims = box_dimensions * 0.5 + Vector3D::Constant(tolerance);
            Vector3D relative_pos(std::abs(ecef_point.x() - box_center.x()),
                                 std::abs(ecef_point.y() - box_center.y()),
                                 std::abs(ecef_point.z() - box_center.z()));
            return (relative_pos.array() <= half_dims.array()).all();
        }

        bool GeometryManager::isLineIntersectSphere(const EcefPoint& line_start, const EcefPoint& line_end,
                                                   const EcefPoint& sphere_center, double radius,
                                                   double tolerance) {
            // 计算线段方向向量
            Vector3D line_dir(line_end.x() - line_start.x(),
                              line_end.y() - line_start.y(),
                              line_end.z() - line_start.z());
            double line_length = line_dir.norm();

            if (line_length < Constants::GEOMETRY_EPSILON) {
                // 退化为点检测
                return isPointInSphere(line_start, sphere_center, radius, tolerance);
            }

            line_dir /= line_length;

            // 计算线段上最接近球心的点
            Vector3D to_sphere(sphere_center.x() - line_start.x(),
                              sphere_center.y() - line_start.y(),
                              sphere_center.z() - line_start.z());
            double t = std::max(0.0, std::min(line_length, to_sphere.dot(line_dir)));
            EcefPoint closest_point(line_start.x() + t * line_dir.x(),
                                           line_start.y() + t * line_dir.y(),
                                           line_start.z() + t * line_dir.z());

            // 检查最近点是否在球体内
            return isPointInSphere(closest_point, sphere_center, radius, tolerance);
        }

        bool GeometryManager::isLineIntersectBox(const EcefPoint& line_start, const EcefPoint& line_end,
                                                const EcefPoint& box_center, const Vector3D& box_dimensions,
                                                double tolerance) {
            // 简化实现：检查线段端点是否在盒子内
            if (isPointInBox(line_start, box_center, box_dimensions, tolerance) ||
                isPointInBox(line_end, box_center, box_dimensions, tolerance)) {
                return true;
            }

            // TODO: 实现更精确的线段与盒子相交检测算法
            // 这里可以使用射线与AABB相交的算法
            return false;
        }

        std::vector<TriangleIndices> GeometryManager::triangulatePolygon2D(const std::vector<EcefPoint>& ecef_polygon) {
            std::vector<TriangleIndices> triangles;

            if (ecef_polygon.size() < 3) {
                LOG_WARN("GeometryManager: ECEF多边形顶点数少于3，无法进行三角剖分");
                return triangles;
            }

            // 使用简单的扇形三角剖分（Fan Triangulation）
            // 从第一个顶点开始，连接所有非相邻顶点形成三角形
            // 注意：这种方法只适用于凸多边形，对于凹多边形可能产生错误结果
            // 如果需要处理复杂多边形，应该使用 earcut 或其他高级算法

            try {
                for (size_t i = 1; i < ecef_polygon.size() - 1; ++i) {
                    TriangleIndices triangle;
                    triangle.v1 = 0;                        // 第一个顶点
                    triangle.v2 = static_cast<int>(i);      // 当前顶点
                    triangle.v3 = static_cast<int>(i + 1);  // 下一个顶点
                    triangles.push_back(triangle);
                }

                LOG_DEBUG("GeometryManager: 扇形三角剖分成功：{} 个顶点 -> {} 个三角形", ecef_polygon.size(), triangles.size());

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 三角剖分失败: {}", e.what());
                triangles.clear();
            }

            return triangles;
        }

        EcefPoint GeometryManager::calculatePolygonCentroidECEF(const std::vector<EcefPoint>& polygon_ecef) const {
            if (polygon_ecef.empty()) {
                throw std::invalid_argument("GeometryManager: 多边形为空，无法计算质心");
            }

            if (polygon_ecef.size() == 1) {
                return polygon_ecef[0];
            }

            if (polygon_ecef.size() == 2) {
                // 线段的中点
                return EcefPoint(
                    (polygon_ecef[0].x() + polygon_ecef[1].x()) / 2.0,
                    (polygon_ecef[0].y() + polygon_ecef[1].y()) / 2.0,
                    (polygon_ecef[0].z() + polygon_ecef[1].z()) / 2.0
                );
            }

            // 对于多边形，计算面积加权质心
            double total_area = 0.0;
            double centroid_x = 0.0, centroid_y = 0.0, centroid_z = 0.0;

            size_t n = polygon_ecef.size();

            // 使用三角剖分计算质心
            for (size_t i = 1; i < n - 1; ++i) {
                // 形成三角形：顶点0, i, i+1
                const EcefPoint& p0 = polygon_ecef[0];
                const EcefPoint& p1 = polygon_ecef[i];
                const EcefPoint& p2 = polygon_ecef[i + 1];

                // 计算三角形面积（使用叉积）
                Vector3D v1 = p1.toVector3D() - p0.toVector3D();
                Vector3D v2 = p2.toVector3D() - p0.toVector3D();
                double triangle_area = 0.5 * v1.cross(v2).norm();

                if (triangle_area > 1e-12) {  // 避免退化三角形
                    // 三角形质心
                    double tri_cx = (p0.x() + p1.x() + p2.x()) / 3.0;
                    double tri_cy = (p0.y() + p1.y() + p2.y()) / 3.0;
                    double tri_cz = (p0.z() + p1.z() + p2.z()) / 3.0;

                    // 面积加权累加
                    centroid_x += triangle_area * tri_cx;
                    centroid_y += triangle_area * tri_cy;
                    centroid_z += triangle_area * tri_cz;
                    total_area += triangle_area;
                }
            }

            if (total_area < 1e-12) {
                // 退化情况，返回几何中心
                LOG_WARN("GeometryManager: 多边形面积过小，使用几何中心作为质心");
                for (const auto& point : polygon_ecef) {
                    centroid_x += point.x();
                    centroid_y += point.y();
                    centroid_z += point.z();
                }
                centroid_x /= polygon_ecef.size();
                centroid_y /= polygon_ecef.size();
                centroid_z /= polygon_ecef.size();
            } else {
                // 面积加权质心
                centroid_x /= total_area;
                centroid_y /= total_area;
                centroid_z /= total_area;
            }

            return EcefPoint(centroid_x, centroid_y, centroid_z);
        }

        //=== 任务规划专用几何计算方法 ===//

        bool GeometryManager::checkPointsCoplanar(const std::vector<WGS84Point>& points, double tolerance_m) const {
            if (points.size() < 4) {
                // 少于4个点总是共面的
                return true;
            }

            try {
                // 使用ECEF坐标系进行几何运算，避免局部坐标系的投影误差
                std::vector<EcefPoint> ecef_points;
                ecef_points.reserve(points.size());
                for (const auto& point : points) {
                    EcefPoint ecef_point = NSUtils::CoordinateConverter::wgs84ToECEF(point);
                    ecef_points.push_back(ecef_point);
                }

                // 使用前三个非共线的点定义平面
                Vector3D normal;
                bool plane_found = false;

                for (size_t i = 0; i < ecef_points.size() - 2 && !plane_found; ++i) {
                    for (size_t j = i + 1; j < ecef_points.size() - 1 && !plane_found; ++j) {
                        for (size_t k = j + 1; k < ecef_points.size() && !plane_found; ++k) {
                            Vector3D v1 = ecef_points[j] - ecef_points[i];
                            Vector3D v2 = ecef_points[k] - ecef_points[i];
                            normal = v1.cross(v2);

                            if (normal.norm() > 1e-6) {  // 确保不共线
                                normal.normalize();
                                plane_found = true;

                                // 检查所有其他点到平面的距离
                                for (size_t m = 0; m < ecef_points.size(); ++m) {
                                    if (m == i || m == j || m == k) continue;

                                    Vector3D point_vec = ecef_points[m] - ecef_points[i];
                                    double distance = std::abs(point_vec.dot(normal));

                                    if (distance > tolerance_m) {
                                        LOG_DEBUG("GeometryManager: ECEF点集不共面，最大偏差: {:.3f}m", distance);
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                }

                if (!plane_found) {
                    LOG_WARN("GeometryManager: 无法找到有效平面，假设点集共面");
                    return true;
                }

                LOG_DEBUG("GeometryManager: 点集共面检查通过，容差: {:.3f}m", tolerance_m);
                return true;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 共面性检查失败: {}", e.what());
                return false;
            }
        }

        std::vector<WGS84Point> GeometryManager::generateScanPath(
            const std::vector<WGS84Point>& boundary,
            double strip_width,
            double overlap_ratio,
            double scan_angle_deg,
            double height_above_plane) const {

            std::vector<WGS84Point> scan_path;

            if (boundary.size() < 3) {
                LOG_WARN("GeometryManager: 边界点数少于3，无法生成扫描路径");
                return scan_path;
            }

            try {
                LOG_DEBUG("GeometryManager: 开始生成斜面扫描路径，边界点数: {}, 扫描角度: {:.1f}°",
                    boundary.size(), scan_angle_deg);

                // 1. 转换为ECEF坐标系进行几何运算
                std::vector<EcefPoint> ecef_boundary;
                ecef_boundary.reserve(boundary.size());
                for (const auto& point : boundary) {
                    EcefPoint ecef_point = NSUtils::CoordinateConverter::wgs84ToECEF(point);
                    ecef_boundary.push_back(ecef_point);
                }

                // 2. 计算平面方程 (使用前三个点)
                if (ecef_boundary.size() < 3) {
                    LOG_ERROR("GeometryManager: ECEF边界点数不足，无法计算平面");
                    return scan_path;
                }

                EcefPoint p1 = ecef_boundary[0];
                EcefPoint p2 = ecef_boundary[1];
                EcefPoint p3 = ecef_boundary[2];

                // 计算平面法向量: normal = (p2-p1) × (p3-p1)
                Vector3D v1(p2.x() - p1.x(), p2.y() - p1.y(), p2.z() - p1.z());
                Vector3D v2(p3.x() - p1.x(), p3.y() - p1.y(), p3.z() - p1.z());
                Vector3D plane_normal = v1.cross(v2).normalized();

                // 平面方程: normal·(point - p1) = 0
                double plane_d = plane_normal.dot(Vector3D(p1.x(), p1.y(), p1.z()));

                LOG_DEBUG("GeometryManager: 平面法向量: ({:.3f}, {:.3f}, {:.3f}), D: {:.3f}",
                    plane_normal.x(), plane_normal.y(), plane_normal.z(), plane_d);

                // 3. 建立平面局部坐标系
                // 选择平面上的两个正交方向作为扫描坐标系
                Vector3D plane_x_axis, plane_y_axis;

                // 如果法向量接近垂直，使用世界坐标的X轴作为参考
                Vector3D world_x(1, 0, 0);
                if (std::abs(plane_normal.dot(world_x)) > 0.9) {
                    // 法向量接近X轴，使用Y轴作为参考
                    world_x = Vector3D(0, 1, 0);
                }

                // 计算平面内的两个正交轴
                plane_x_axis = world_x - plane_normal * plane_normal.dot(world_x);
                plane_x_axis.normalize();
                plane_y_axis = plane_normal.cross(plane_x_axis).normalized();

                // 应用扫描角度旋转
                double scan_angle_rad = scan_angle_deg * M_PI / 180.0;
                Vector3D rotated_x_axis = plane_x_axis * std::cos(scan_angle_rad) +
                                         plane_y_axis * std::sin(scan_angle_rad);
                Vector3D rotated_y_axis = -plane_x_axis * std::sin(scan_angle_rad) +
                                         plane_y_axis * std::cos(scan_angle_rad);

                LOG_DEBUG("GeometryManager: 平面坐标系 - X轴: ({:.3f}, {:.3f}, {:.3f}), Y轴: ({:.3f}, {:.3f}, {:.3f})",
                    rotated_x_axis.x(), rotated_x_axis.y(), rotated_x_axis.z(),
                    rotated_y_axis.x(), rotated_y_axis.y(), rotated_y_axis.z());

                // 4. 将边界点投影到平面局部坐标系
                std::vector<std::pair<double, double>> projected_boundary;
                double min_u = std::numeric_limits<double>::max();
                double max_u = std::numeric_limits<double>::lowest();
                double min_v = std::numeric_limits<double>::max();
                double max_v = std::numeric_limits<double>::lowest();

                for (const auto& ecef_pt : ecef_boundary) {
                    Vector3D pt_vec(ecef_pt.x(), ecef_pt.y(), ecef_pt.z());
                    Vector3D relative_pos = pt_vec - Vector3D(p1.x(), p1.y(), p1.z());

                    double u = relative_pos.dot(rotated_x_axis);
                    double v = relative_pos.dot(rotated_y_axis);

                    projected_boundary.emplace_back(u, v);
                    min_u = std::min(min_u, u);
                    max_u = std::max(max_u, u);
                    min_v = std::min(min_v, v);
                    max_v = std::max(max_v, v);
                }

                LOG_DEBUG("GeometryManager: 投影边界框 - U: [{:.2f}, {:.2f}], V: [{:.2f}, {:.2f}]",
                    min_u, max_u, min_v, max_v);

                // 5. 生成有界斜面扫描线
                double effective_strip_width = strip_width * (1.0 - overlap_ratio);
                int num_strips = static_cast<int>((max_v - min_v) / effective_strip_width) + 1;

                bool left_to_right = true;
                for (int strip = 0; strip < num_strips; ++strip) {
                    double v = min_v + strip * effective_strip_width;
                    if (v > max_v) v = max_v;

                    // 生成扫描线与边界多边形的交点
                    std::vector<double> intersections;

                    // 检查扫描线与每条边界边的交点
                    for (size_t i = 0; i < projected_boundary.size(); ++i) {
                        size_t next_i = (i + 1) % projected_boundary.size();
                        double u1 = projected_boundary[i].first;
                        double v1 = projected_boundary[i].second;
                        double u2 = projected_boundary[next_i].first;
                        double v2 = projected_boundary[next_i].second;

                        // 检查边是否与扫描线相交
                        if ((v1 <= v && v2 >= v) || (v1 >= v && v2 <= v)) {
                            if (std::abs(v2 - v1) > 1e-9) {
                                // 计算交点的u坐标
                                double t = (v - v1) / (v2 - v1);
                                double u_intersect = u1 + t * (u2 - u1);
                                intersections.push_back(u_intersect);
                            }
                        }
                    }

                    // 排序交点并配对生成扫描线段
                    if (intersections.size() >= 2) {
                        std::sort(intersections.begin(), intersections.end());

                        // 取最外侧的两个交点作为扫描线段
                        double u_start = intersections.front();
                        double u_end = intersections.back();

                        // 在平面上生成扫描线的两个端点
                        Vector3D start_local = Vector3D(p1.x(), p1.y(), p1.z()) +
                                              rotated_x_axis * u_start + rotated_y_axis * v;
                        Vector3D end_local = Vector3D(p1.x(), p1.y(), p1.z()) +
                                            rotated_x_axis * u_end + rotated_y_axis * v;

                        // 沿法向量偏移指定高度
                        start_local += plane_normal * height_above_plane;
                        end_local += plane_normal * height_above_plane;

                        // 转换回WGS84坐标
                        EcefPoint start_ecef(start_local.x(), start_local.y(), start_local.z());
                        EcefPoint end_ecef(end_local.x(), end_local.y(), end_local.z());

                        WGS84Point start_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(start_ecef);
                        WGS84Point end_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(end_ecef);

                        if (left_to_right) {
                            scan_path.push_back(start_wgs84);
                            scan_path.push_back(end_wgs84);
                        } else {
                            scan_path.push_back(end_wgs84);
                            scan_path.push_back(start_wgs84);
                        }
                    } else {
                        LOG_DEBUG("GeometryManager: 扫描线 {} 与边界无有效交点，跳过", strip);
                    }

                    left_to_right = !left_to_right;
                }

                LOG_DEBUG("GeometryManager: 斜面扫描路径生成完成，{}个航点，{}条扫描线",
                    scan_path.size(), num_strips);
                return scan_path;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 斜面扫描路径生成失败: {}", e.what());
                return scan_path;
            }
        }

        std::vector<WGS84Point> GeometryManager::generateCylinderScanPath(
            const WGS84Point& center,
            double radius,
            double height,
            double vertical_spacing,
            int points_per_circle,
            bool clockwise) const {

            std::vector<WGS84Point> scan_path;

            if (radius <= 0.0 || height <= 0.0 || vertical_spacing <= 0.0 || points_per_circle < 3) {
                LOG_WARN("GeometryManager: 圆柱扫描参数无效");
                return scan_path;
            }

            try {
                // 计算需要的层数
                int num_levels = static_cast<int>(std::ceil(height / vertical_spacing)) + 1;

                // 计算角度步长
                double angle_step = 2.0 * M_PI / points_per_circle;
                if (!clockwise) {
                    angle_step = -angle_step;  // 逆时针
                }

                // 为每一层生成圆形路径
                for (int level = 0; level < num_levels; ++level) {
                    double current_height = center.altitude + level * vertical_spacing;

                    // 确保不超过圆柱高度
                    if (level * vertical_spacing > height) {
                        current_height = center.altitude + height;
                    }

                    // 生成当前层的圆形路径
                    for (int point = 0; point < points_per_circle; ++point) {
                        double angle = point * angle_step;

                        // 计算相对于中心的偏移（米）
                        double offset_x = radius * std::cos(angle);  // 东向偏移
                        double offset_y = radius * std::sin(angle);  // 北向偏移

                        // 使用 GeographicLib 计算精确的目标坐标
                        // 先计算东向偏移
                        double intermediate_lat, intermediate_lon;
                        const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();
                        geod.Direct(center.latitude, center.longitude, 90.0, offset_x, intermediate_lat, intermediate_lon);

                        // 再计算北向偏移
                        double final_lat, final_lon;
                        geod.Direct(intermediate_lat, intermediate_lon, 0.0, offset_y, final_lat, final_lon);

                        scan_path.emplace_back(final_lat, final_lon, current_height);
                    }
                }

                LOG_DEBUG("GeometryManager: 圆柱扫描路径生成完成，{}层，{}个航点", num_levels, scan_path.size());
                return scan_path;

            } catch (const std::exception& e) {
                LOG_ERROR("GeometryManager: 圆柱扫描路径生成失败: {}", e.what());
                return scan_path;
            }
        }

    } // namespace NSEnvironment
} // namespace NSDrones
