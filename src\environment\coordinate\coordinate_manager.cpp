// src/environment/coordinate/coordinate_manager.cpp
#include "environment/coordinate/coordinate_manager.h"
#include "params/parameters.h"
#include "utils/logging.h"
#include <stdexcept>
#include <sstream>
#include <iomanip>

namespace NSDrones {
	namespace NSEnvironment {

		//=== CoordinateManager 实现 ===//
		CoordinateManager::CoordinateManager() {
			LOG_DEBUG("坐标系统: 创建坐标系统管理器");
		}

		CoordinateManager::~CoordinateManager() {
			LOG_DEBUG("坐标系统: 销毁坐标系统管理器，当前管理 {} 个任务空间", task_spaces_.size());
		}

		bool CoordinateManager::initialize(std::shared_ptr<NSParams::ParamValues> global_params) {
			LOG_INFO("坐标系统: 初始化坐标系统管理器");

			if (!global_params) {
				LOG_ERROR("坐标系统: 全局参数为空，无法初始化");
				return false;
			}

			LOG_INFO("坐标系统: 坐标系统管理器初始化完成");
			return true;
		}

		bool CoordinateManager::createTaskSpacesFromObjects(const std::vector<nlohmann::json>& taskspace_objects) {
			LOG_INFO("坐标系统: 开始从对象配置创建TaskSpace");

			int created_count = 0;

			for (const auto& object_json : taskspace_objects) {
				try {
					// 解析对象基本信息
					std::string object_id = object_json.value("id", "");
					std::string type_key = object_json.value("type", "");
					bool enabled = object_json.value("enabled", true);

					if (!enabled || object_id.empty() || type_key.empty()) {
						LOG_DEBUG("坐标系统: 跳过TaskSpace对象 '{}' (禁用或配置不完整)", object_id);
						continue;
					}

					// 创建TaskSpace实例
					if (createTaskSpaceFromObjectConfig(object_id, type_key, object_json)) {
						created_count++;
						LOG_DEBUG("坐标系统: 成功创建TaskSpace '{}'", object_id);
					} else {
						LOG_ERROR("坐标系统: 创建TaskSpace '{}' 失败", object_id);
					}

				} catch (const std::exception& e) {
					LOG_ERROR("坐标系统: 处理TaskSpace对象时发生异常: {}", e.what());
				}
			}

			LOG_INFO("坐标系统: 成功从对象配置创建 {} 个TaskSpace", created_count);
			return created_count > 0;
		}

		bool CoordinateManager::createTaskSpaceFromObjectConfig(const std::string& space_id,
			const std::string& type_key, const nlohmann::json& object_json) {
			try {
				LOG_DEBUG("坐标系统: 开始创建TaskSpace '{}' (类型: {})", space_id, type_key);

				// 获取对象名称
				std::string name = object_json.value("name", space_id);

				// 创建基准点策略
				std::unique_ptr<IOriginStrategy> strategy;
				if (type_key == "TaskSpace.StaticTaskSpace") {
					// 静态TaskSpace使用默认基准点，稍后在initialize中更新
					strategy = std::make_unique<StaticOriginStrategy>(WGS84Point(0, 0, 0));
				} else if (type_key == "TaskSpace.DynamicTaskSpace") {
					// 动态TaskSpace使用默认跟随实体，稍后在initialize中更新
					strategy = std::make_unique<DynamicOriginStrategy>(ObjectID(""));
				} else {
					LOG_ERROR("坐标系统: 未知的TaskSpace类型 '{}'", type_key);
					return false;
				}

				// 创建TaskSpace实例（简化版本，由上层负责参数初始化）
				ObjectID task_space_id(space_id);
				auto task_space = std::make_shared<TaskSpace>(task_space_id, type_key, name, std::move(strategy));

				// 存储TaskSpace到管理器
				std::lock_guard<std::mutex> lock(spaces_mutex_);
				task_spaces_[space_id] = task_space;
				total_spaces_created_++;

				LOG_INFO("坐标系统: 成功创建TaskSpace '{}' (类型: {})，等待参数初始化", space_id, type_key);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("坐标系统: 创建TaskSpace '{}' 时发生异常: {}", space_id, e.what());
				return false;
			}
		}

		bool CoordinateManager::addTaskSpace(std::shared_ptr<TaskSpace> task_space) {
			if (!task_space) {
				LOG_ERROR("坐标系统: 尝试添加空的TaskSpace实例");
				return false;
			}

			std::string space_id = task_space->getId();

			std::lock_guard<std::mutex> lock(spaces_mutex_);

			if (task_spaces_.find(space_id) != task_spaces_.end()) {
				LOG_WARN("坐标系统: TaskSpace '{}' 已存在，无法重复添加", space_id);
				return false;
			}

			task_spaces_[space_id] = task_space;
			total_spaces_created_++;

			LOG_INFO("坐标系统: 成功添加TaskSpace '{}' 到管理器", space_id);
			return true;
		}

		bool CoordinateManager::createStaticTaskSpace(const std::string& space_id,
			const WGS84Point& reference_point, const std::string& task_type) {
			try {
				LOG_DEBUG("坐标系统: 开始创建静态TaskSpace '{}' (类型: {})", space_id, task_type);

				// 检查是否已存在
				{
					std::lock_guard<std::mutex> lock(spaces_mutex_);
					if (task_spaces_.find(space_id) != task_spaces_.end()) {
						LOG_WARN("坐标系统: TaskSpace '{}' 已存在，无法重复创建", space_id);
						return false;
					}
				}

				// 创建静态基准点策略
				auto strategy = std::make_unique<StaticOriginStrategy>(reference_point);
				ObjectID task_space_id(space_id);
				auto task_space = std::make_shared<TaskSpace>(task_space_id, task_type, space_id, std::move(strategy));

				// 添加到管理器
				{
					std::lock_guard<std::mutex> lock(spaces_mutex_);
					task_spaces_[space_id] = task_space;
					total_spaces_created_++;
				}

				LOG_INFO("坐标系统: 成功创建静态TaskSpace '{}' (类型: {})", space_id, task_type);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("坐标系统: 创建静态TaskSpace '{}' 时发生异常: {}", space_id, e.what());
				return false;
			}
		}

		bool CoordinateManager::createDynamicTaskSpace(const std::string& space_id,
			ObjectID reference_entity_id, const std::string& task_type) {
			try {
				LOG_DEBUG("坐标系统: 开始创建动态TaskSpace '{}' (类型: {}, 跟随实体: {})", space_id, task_type, reference_entity_id);

				// 检查是否已存在
				{
					std::lock_guard<std::mutex> lock(spaces_mutex_);
					if (task_spaces_.find(space_id) != task_spaces_.end()) {
						LOG_WARN("坐标系统: TaskSpace '{}' 已存在，无法重复创建", space_id);
						return false;
					}
				}

				// 创建动态基准点策略
				auto strategy = std::make_unique<DynamicOriginStrategy>(reference_entity_id);
				ObjectID task_space_id(space_id);
				auto task_space = std::make_shared<TaskSpace>(task_space_id, task_type, space_id, std::move(strategy));

				// 添加到管理器
				{
					std::lock_guard<std::mutex> lock(spaces_mutex_);
					task_spaces_[space_id] = task_space;
					total_spaces_created_++;
				}

				LOG_INFO("坐标系统: 成功创建动态TaskSpace '{}' (类型: {}, 跟随实体: {})", space_id, task_type, reference_entity_id);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("坐标系统: 创建动态TaskSpace '{}' 时发生异常: {}", space_id, e.what());
				return false;
			}
		}

		std::shared_ptr<TaskSpace> CoordinateManager::getTaskSpace(const std::string& space_id) {
			std::lock_guard<std::mutex> lock(spaces_mutex_);
			auto it = task_spaces_.find(space_id);
			return (it != task_spaces_.end()) ? it->second : nullptr;
		}

		bool CoordinateManager::hasTaskSpace(const std::string& space_id) const {
			std::lock_guard<std::mutex> lock(spaces_mutex_);
			return task_spaces_.find(space_id) != task_spaces_.end();
		}

		std::vector<std::string> CoordinateManager::getAllTaskSpaceIds() const {
			std::lock_guard<std::mutex> lock(spaces_mutex_);

			std::vector<std::string> ids;
			ids.reserve(task_spaces_.size());

			for (const auto& pair : task_spaces_) {
				ids.push_back(pair.first);
			}

			return ids;
		}

		bool CoordinateManager::removeTaskSpace(const std::string& space_id) {
			std::lock_guard<std::mutex> lock(spaces_mutex_);

			auto it = task_spaces_.find(space_id);
			if (it == task_spaces_.end()) {
				LOG_WARN("任务空间: 尝试删除不存在的任务空间 '{}'", space_id);
				return false;
			}

			task_spaces_.erase(it);
			LOG_INFO("任务空间: 成功删除任务空间 '{}'", space_id);
			return true;
		}

		void CoordinateManager::clearAllTaskSpaces() {
			std::lock_guard<std::mutex> lock(spaces_mutex_);

			size_t count = task_spaces_.size();
			task_spaces_.clear();

			LOG_INFO("任务空间: 清理所有任务空间，共删除 {} 个", count);
		}

		size_t CoordinateManager::entitySetOrigin(ObjectID entity_id, const WGS84Point& new_position) {
			std::lock_guard<std::mutex> lock(spaces_mutex_);

			size_t updated_count = 0;

			for (auto& pair : task_spaces_) {
				auto& task_space = pair.second;
				if (task_space->canSetOrigin(entity_id)) {
					// 使用新的方法，同时更新基准点并通知注册的对象
					task_space->updateOriginAndNotifyObjects(new_position);
					updated_count++;
					LOG_DEBUG("任务空间: 为实体 {} 更新任务空间 '{}' 的基准点并通知对象", entity_id, pair.first);
				}
			}

			if (updated_count > 0) {
				LOG_DEBUG("任务空间: 实体 {} 位置更新影响了 {} 个任务空间", entity_id, updated_count);
			}

			return updated_count;
		}

		std::vector<std::string> CoordinateManager::findTaskSpacesByEntity(ObjectID entity_id) const {
			std::lock_guard<std::mutex> lock(spaces_mutex_);

			std::vector<std::string> result;

			for (const auto& pair : task_spaces_) {
				if (pair.second->canSetOrigin(entity_id)) {
					result.push_back(pair.first);
				}
			}

			return result;
		}

		size_t CoordinateManager::getActiveTaskSpaceCount() const {
			std::lock_guard<std::mutex> lock(spaces_mutex_);
			return task_spaces_.size();
		}

		std::string CoordinateManager::generateUniqueSpaceId(const std::string& prefix) const {
			// 注意：此方法假设调用者已经持有 spaces_mutex_
			size_t counter = 0;
			std::string candidate;

			do {
				candidate = prefix + "_" + std::to_string(counter++);
			} while (task_spaces_.find(candidate) != task_spaces_.end());

			return candidate;
		}

		bool CoordinateManager::isValidSpaceId(const std::string& space_id) const {
			// 简单的ID验证：不能为空，不能包含特殊字符
			if (space_id.empty()) {
				return false;
			}

			// 检查是否包含不允许的字符
			for (char c : space_id) {
				if (!std::isalnum(c) && c != '_' && c != '-') {
					return false;
				}
			}

			return true;
		}

		//=== 全局坐标空间管理方法 ===//

		std::shared_ptr<TaskSpace> CoordinateManager::getOrCreateTaskSpace(const WGS84Point& origin, std::string& space_id) {
			std::lock_guard<std::mutex> lock(spaces_mutex_);

			// 1. 首先检查是否已存在指定名称的TaskSpace
			auto it = task_spaces_.find(space_id);
			if (it != task_spaces_.end()) {
				// TaskSpace已存在，检查origin是否匹配
				auto& task_space = it->second;
				WGS84Point current_origin = task_space->getOrigin();

				// 计算坐标差异（使用合理的容差）
				double lat_diff = std::abs(current_origin.latitude - origin.latitude);
				double lon_diff = std::abs(current_origin.longitude - origin.longitude);
				double alt_diff = std::abs(current_origin.altitude - origin.altitude);

				// 容差：纬度/经度 1e-6度（约0.1米），高度 1.0米
				if (lat_diff > 1e-6 || lon_diff > 1e-6 || alt_diff > 1.0) {
					LOG_WARN("坐标系统: TaskSpace '{}' 已存在但origin不匹配。当前: ({}), 请求: ({})",
						space_id, current_origin.toString(), origin.toString());

					// 更新TaskSpace的origin
					task_space->updateOriginAndNotifyObjects(origin);
					LOG_INFO("坐标系统: 已更新TaskSpace '{}' 的origin为: ({})", space_id, origin.toString());
				} else {
					LOG_DEBUG("坐标系统: TaskSpace '{}' 已存在且origin匹配: ({})", space_id, origin.toString());
				}

				// space_id保持不变，因为找到了精确匹配的TaskSpace
				return task_space;
			}

			// 2. 指定名称的TaskSpace不存在，尝试根据origin查找匹配的TaskSpace
			for (const auto& [existing_id, existing_task_space] : task_spaces_) {
				WGS84Point existing_origin = existing_task_space->getOrigin();

				// 计算坐标差异
				double lat_diff = std::abs(existing_origin.latitude - origin.latitude);
				double lon_diff = std::abs(existing_origin.longitude - origin.longitude);
				double alt_diff = std::abs(existing_origin.altitude - origin.altitude);

				// 如果找到origin匹配的TaskSpace
				if (lat_diff <= 1e-6 && lon_diff <= 1e-6 && alt_diff <= 1.0) {
					LOG_INFO("坐标系统: 找到origin匹配的TaskSpace '{}' (原请求: '{}'), origin: ({})",
						existing_id, space_id, origin.toString());

					// 更新space_id为实际匹配的TaskSpace ID
					space_id = existing_id;
					return existing_task_space;
				}
			}

			// 3. 没有找到匹配的TaskSpace
			LOG_INFO("坐标系统: 未找到匹配origin的TaskSpace '{}'，origin: ({})", space_id, origin.toString());
			LOG_INFO("坐标系统: 提示：如需创建新TaskSpace，请在配置文件中添加TaskSpace定义和对象实例");

			return nullptr;
		}


	// === 坐标转换方法实现 ===

	EcefPoint CoordinateManager::wgs84ToECEF(const WGS84Point& wgs84_point) const {
		return NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_point);
	}

	WGS84Point CoordinateManager::ecefToWGS84(const EcefPoint& ecef_point) const {
		return NSUtils::CoordinateConverter::ecefToWGS84(ecef_point);
	}

	} // namespace NSEnvironment
} // namespace NSDrones