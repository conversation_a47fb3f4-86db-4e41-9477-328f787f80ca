#include "environment/maps/single_gridmap.h"
#include "utils/coordinate_converter.h"
#include "utils/logging.h"
#include <spdlog/fmt/ranges.h>
#include "core/types.h"
#include <limits>
#include <cmath>
#include <filesystem>

// 使用NSCore::Constants中定义的常量，不需要自定义M_PI
#include <set>
#include <algorithm>
#include <sstream>  // 添加缺失的头文件
#include "Eigen/Core"
#include "Eigen/Dense"
#include <GeographicLib/LocalCartesian.hpp>

// GDAL includes
#include <gdal_priv.h>
#include <ogr_spatialref.h>
#include <cpl_conv.h>

namespace NSDrones {
	namespace NSEnvironment {

		SingleGridMap::SingleGridMap() : initialized_(false) {
			LOG_DEBUG("SingleGridMap: 构造函数调用");
		}

		SingleGridMap::~SingleGridMap() {
			LOG_DEBUG("SingleGridMap: 析构函数调用");
		}

		// ========== IGridMap接口实现 ==========

		bool SingleGridMap::initialize(std::shared_ptr<NSParams::ParamValues> global_params) {
			LOG_INFO("SingleGridMap: 开始初始化");

			if (!global_params) {
				LOG_ERROR("SingleGridMap: 全局参数为空");
				return false;
			}

			try {
				// 获取地图文件路径
				std::string map_file_path = global_params->getValueOrDefault<std::string>("map.file_path", "");
				if (map_file_path.empty()) {
					LOG_ERROR("SingleGridMap: 未配置地图文件路径 (map.file_path)");
					return false;
				}

				// 检查文件扩展名确定加载模式
				std::string extension = std::filesystem::path(map_file_path).extension().string();
				std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

				if (extension == ".tif" || extension == ".geotiff" || extension == ".tiff") {
					return loadFromTif(map_file_path);
				} else if (extension == ".png") {
					return loadFromPng(map_file_path);
				} else {
					LOG_ERROR("SingleGridMap: 不支持的文件格式: {}", extension);
					return false;
				}

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 初始化时发生异常: {}", e.what());
				return false;
			}
		}

		bool SingleGridMap::initializeFromFile(const std::string& file_path, const std::string& load_mode) {
			LOG_INFO("SingleGridMap: 从文件初始化: '{}', 模式: {}", file_path, load_mode);

			try {
				if (load_mode == "geotiff" || load_mode == "tiff") {
					return loadFromTif(file_path);
				} else if (load_mode == "png") {
					return loadFromPng(file_path);
				} else {
					LOG_ERROR("SingleGridMap: 不支持的加载模式: {}", load_mode);
					return false;
				}
			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 从文件初始化时发生异常: {}", e.what());
				return false;
			}
		}

		// === IGridMap接口实现（使用内部查询方法） ===

		std::optional<double> SingleGridMap::getElevation(double latitude, double longitude) const {
			return queryLayerValue<double>(latitude, longitude, elevation_layer_name_, "高程");
		}

		std::optional<FeatureType> SingleGridMap::getFeature(double latitude, double longitude) const {
			auto result = queryLayerValue<float>(latitude, longitude, feature_layer_name_, "地物类型");
			if (result.has_value()) {
				// 将float值转换为FeatureType枚举
				int feature_value = static_cast<int>(result.value());
				return static_cast<FeatureType>(feature_value);
			}
			return std::nullopt;
		}

		std::optional<double> SingleGridMap::getFeatureHeight(double latitude, double longitude) const {
			return queryLayerValue<double>(latitude, longitude, feature_height_layer_name_, "地物高度");
		}

		// === 通用查询方法实现（消除重复代码） ===

		template<typename T>
		std::optional<T> SingleGridMap::queryLayerValue(double latitude, double longitude,
			const std::string& layer_name, const std::string& data_type_name) const {

			try {
				// 参数验证
				if (layer_name.empty()) {
					LOG_DEBUG("单个地图: {}图层名称为空，跳过查询", data_type_name);
					return std::nullopt;
				}

				// 坐标转换
				WGS84Point wgs84_point(longitude, latitude, 0.0);
				int row, col;
				if (!wgs84ToMatrixIndex(wgs84_point, row, col)) {
					return std::nullopt;
				}

				// 获取图层数据
				std::lock_guard<std::mutex> lock(coordinate_mutex_);
				auto layer_it = data_layers_.find(layer_name);
				if (layer_it == data_layers_.end()) {
					LOG_DEBUG("单个地图: {}图层'{}'不存在", data_type_name, layer_name);
					return std::nullopt;
				}

				// 获取数据值
				const auto& matrix = layer_it->second;
				float value = matrix(col, row);

				// 检查有效性
				if (std::isnan(value)) {
					LOG_TRACE("单个地图: 坐标({:.6f}, {:.6f})的{}数据为NaN", latitude, longitude, data_type_name);
					return std::nullopt;
				}

				return static_cast<T>(value);

			} catch (const std::exception& e) {
				LOG_ERROR("单个地图: 查询{}数据时发生异常: {}", data_type_name, e.what());
				return std::nullopt;
			}
		}

		// === 模板方法显式实例化 ===
		template std::optional<double> SingleGridMap::queryLayerValue<double>(
			double latitude, double longitude, const std::string& layer_name, const std::string& data_type_name) const;
		template std::optional<float> SingleGridMap::queryLayerValue<float>(
			double latitude, double longitude, const std::string& layer_name, const std::string& data_type_name) const;

		bool SingleGridMap::loadFromTif(const std::string& file_path) {
			LOG_INFO("SingleGridMap: 开始从GeoTIFF文件加载: '{}'", file_path);

			try {
				// 检查文件是否存在
				if (!std::filesystem::exists(file_path)) {
					LOG_ERROR("SingleGridMap: GeoTIFF文件不存在: '{}'", file_path);
					return false;
				}

				// 注册GDAL驱动
				GDALAllRegister();

				// 打开GeoTIFF文件
				GDALDataset* dataset = (GDALDataset*)GDALOpen(file_path.c_str(), GA_ReadOnly);
				if (!dataset) {
					LOG_ERROR("SingleGridMap: 无法打开GeoTIFF文件: '{}'", file_path);
					return false;
				}

				// 使用RAII管理资源
				auto dataset_guard = std::unique_ptr<GDALDataset, decltype(&GDALClose)>(dataset, GDALClose);

				// 获取地理变换参数
				double geoTransform[6];
				if (dataset->GetGeoTransform(geoTransform) != CE_None) {
					LOG_ERROR("SingleGridMap: 无法获取地理变换参数");
					return false;
				}

				// 获取栅格尺寸
				int nRasterXSize = dataset->GetRasterXSize();
				int nRasterYSize = dataset->GetRasterYSize();

				LOG_INFO("SingleGridMap: GeoTIFF尺寸: {}x{} 像素", nRasterXSize, nRasterYSize);

				// 计算分辨率
				std::pair<double, double> resolution = calculateResolution(geoTransform);
				LOG_INFO("SingleGridMap: 分辨率: X={:.3f}m, Y={:.3f}m", resolution.first, resolution.second);

				// 计算四个角点的WGS84坐标
				std::array<WGS84Point, 4> corners = calculateCorners(dataset, geoTransform);

				// 使用左上角作为地图原点
				map_origin_ = corners[0]; // 左上角
				LOG_INFO("SingleGridMap: 使用左上角作为地图原点: {}", map_origin_.toString());

				// 创建以左上角为原点的局部坐标系统
				local_cartesian_ = std::make_shared<GeographicLib::LocalCartesian>(
					map_origin_.latitude, map_origin_.longitude, map_origin_.altitude);
				
				LOG_INFO("SingleGridMap: 创建局部坐标系统，原点: {}", map_origin_.toString());

				// 设置原始分辨率和网格尺寸
				native_resolution_ = resolution;
				grid_size_ = {nRasterXSize, nRasterYSize};
				
				LOG_INFO("SingleGridMap: 网格尺寸: {}x{} 像素, 分辨率: {:.2f}x{:.2f} m/px", 
					grid_size_.first, grid_size_.second, 
					native_resolution_.first, native_resolution_.second);

				// 从GeoTIFF加载数据到Eigen矩阵
				if (!loadDataFromGeoTiff(dataset, geoTransform)) {
					LOG_ERROR("SingleGridMap: 从GeoTIFF加载数据失败");
					return false;
				}

				// 填充元数据
				populateMetadataFromGeoTiff(file_path, resolution, nRasterXSize, nRasterYSize, corners);

				// 智能检测和设置图层名称
				if (!detectAndSetLayers(nullptr)) {
					LOG_WARN("SingleGridMap: 图层检测失败，但继续加载");
				}

				initialized_ = true;
				LOG_INFO("SingleGridMap: GeoTIFF文件加载完成: '{}'", file_path);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 加载GeoTIFF时发生异常: {}", e.what());
				return false;
			}
		}

		std::pair<double, double> SingleGridMap::calculateResolution(const double* geoTransform) {
			// geoTransform[1] = X方向像素大小（经度方向，单位：度）
			// geoTransform[5] = Y方向像素大小（纬度方向，单位：度，通常为负值）
			double pixel_size_x_deg = std::abs(geoTransform[1]);
			double pixel_size_y_deg = std::abs(geoTransform[5]);

			// 将度数转换为米数
			// 在地球表面，1度经度的距离随纬度变化，1度纬度距离相对固定
			// 使用地图中心点的纬度来计算经度的米数转换
			double center_lat = geoTransform[3] + (grid_size_.second * geoTransform[5]) / 2.0;

			// 纬度方向：1度距离在地球上相对固定
			double native_res_y_meters = pixel_size_y_deg * NSCore::Constants::METERS_PER_DEGREE_LATITUDE;

			// 经度方向：1度的距离 = 赤道处1度距离 * cos(纬度)
			double native_res_x_meters = pixel_size_x_deg * NSCore::Constants::METERS_PER_DEGREE_LONGITUDE_AT_EQUATOR *
				std::cos(center_lat * NSCore::Constants::DEG_TO_RAD);

			LOG_INFO("SingleGridMap: 像素大小: X={:.6f}度, Y={:.6f}度", pixel_size_x_deg, pixel_size_y_deg);
			LOG_INFO("SingleGridMap: 地图中心纬度: {:.6f}度", center_lat);
			LOG_INFO("SingleGridMap: 最终使用分辨率: X={:.3f}m, Y={:.3f}m", native_res_x_meters, native_res_y_meters);
			return {native_res_x_meters, native_res_y_meters};
		}

		std::array<WGS84Point, 4> SingleGridMap::calculateCorners(GDALDataset* dataset, const double* geoTransform) {
			int nRasterXSize = dataset->GetRasterXSize();
			int nRasterYSize = dataset->GetRasterYSize();

			// 计算四个角点的地理坐标
			std::array<std::pair<double, double>, 4> pixel_coords = {{
				{0, 0},                                    // 左上角
				{static_cast<double>(nRasterXSize), 0},   // 右上角
				{static_cast<double>(nRasterXSize), static_cast<double>(nRasterYSize)}, // 右下角
				{0, static_cast<double>(nRasterYSize)}     // 左下角
			}};

			std::array<WGS84Point, 4> corners;
			for (int i = 0; i < 4; ++i) {
				double geo_x = geoTransform[0] + pixel_coords[i].first * geoTransform[1] + pixel_coords[i].second * geoTransform[2];
				double geo_y = geoTransform[3] + pixel_coords[i].first * geoTransform[4] + pixel_coords[i].second * geoTransform[5];

				// 转换为WGS84坐标
				corners[i] = WGS84Point(geo_x, geo_y, 0.0); // 经度, 纬度, 高度

				LOG_DEBUG("SingleGridMap: 角点{}: 像素({:.0f}, {:.0f}) -> 地理({:.6f}, {:.6f}) -> WGS84({:.6f}, {:.6f})",
					i, pixel_coords[i].first, pixel_coords[i].second, geo_x, geo_y, corners[i].longitude, corners[i].latitude);
			}

			return corners;
		}

		bool SingleGridMap::loadDataFromGeoTiff(GDALDataset* dataset, const double* geoTransform) {
			try {
				// 获取第一个波段（高程数据）
				GDALRasterBand* band = dataset->GetRasterBand(1);
				if (!band) {
					LOG_ERROR("SingleGridMap: 无法获取第一个波段");
					return false;
				}

				// 获取NoData值
				int hasNoData;
				double noDataValue = band->GetNoDataValue(&hasNoData);
				if (!hasNoData) {
					noDataValue = std::numeric_limits<double>::quiet_NaN();
				}

				// 获取高程数据的缩放和偏移参数
				double scale = band->GetScale();
				double offset = band->GetOffset();

				// 检查GDAL是否返回了有效的缩放和偏移值
				// GDAL在没有设置时可能返回特殊值，需要检查
				int hasScale = 0, hasOffset = 0;
				double actualScale = band->GetScale(&hasScale);
				double actualOffset = band->GetOffset(&hasOffset);

				if (hasScale) {
					scale = actualScale;
				} else {
					scale = 1.0;  // 默认缩放
				}

				if (hasOffset) {
					offset = actualOffset;
				} else {
					offset = 0.0;  // 默认偏移
				}

				// 如果没有设置缩放和偏移，检查单位类型
				if (!hasScale && !hasOffset) {
					const char* units = band->GetUnitType();
					if (units && strlen(units) > 0) {
						LOG_INFO("SingleGridMap: 高程数据单位: {}", units);
						// 根据单位进行转换（如果需要）
						if (strcmp(units, "cm") == 0) {
							scale = 0.01;  // 厘米转米
						} else if (strcmp(units, "mm") == 0) {
							scale = 0.001; // 毫米转米
						}
					}
				}

				LOG_INFO("SingleGridMap: 高程数据转换参数 - 缩放: {}, 偏移: {}", scale, offset);

				// 创建Eigen矩阵存储高程数据
				Eigen::MatrixXf elevation_matrix(grid_size_.first, grid_size_.second);

				// 批量读取整个栅格数据
				std::vector<float> raster_data(grid_size_.first * grid_size_.second);
				CPLErr err = band->RasterIO(GF_Read, 0, 0, grid_size_.first, grid_size_.second,
										   raster_data.data(), grid_size_.first, grid_size_.second,
										   GDT_Float32, 0, 0);

				if (err != CE_None) {
					LOG_ERROR("SingleGridMap: 批量读取栅格数据失败");
					return false;
				}

				// 将数据填充到Eigen矩阵，并应用缩放和偏移
				int valid_count = 0;
				double min_elevation = std::numeric_limits<double>::max();
				double max_elevation = std::numeric_limits<double>::lowest();

				for (int row = 0; row < grid_size_.second; ++row) {
					for (int col = 0; col < grid_size_.first; ++col) {
						float raw_value = raster_data[row * grid_size_.first + col];

						// 检查NoData值
						if (hasNoData && std::abs(raw_value - noDataValue) < 1e-6) {
							elevation_matrix(col, row) = std::numeric_limits<float>::quiet_NaN();
						} else {
							// 应用缩放和偏移：真实高程 = 原始值 * 缩放 + 偏移
							float real_elevation = static_cast<float>(raw_value * scale + offset);
							elevation_matrix(col, row) = real_elevation;
							valid_count++;

							// 统计高程范围
							min_elevation = std::min(min_elevation, static_cast<double>(real_elevation));
							max_elevation = std::max(max_elevation, static_cast<double>(real_elevation));
						}
					}
				}

				// 存储到数据图层
				data_layers_[elevation_layer_name_] = std::move(elevation_matrix);

				LOG_INFO("SingleGridMap: 高程数据加载完成，有效像素: {}/{}", valid_count, grid_size_.first * grid_size_.second);
				if (valid_count > 0) {
					LOG_INFO("SingleGridMap: 高程范围: {:.2f}m 到 {:.2f}m", min_elevation, max_elevation);
				}

				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 加载数据时发生异常: {}", e.what());
				return false;
			}
		}

		bool SingleGridMap::detectAndSetLayers(std::shared_ptr<NSParams::ParamValues> global_params) {
			LOG_DEBUG("SingleGridMap: 开始智能检测图层名称");

			// 获取可用的图层列表（使用新的data_layers_）
			std::vector<std::string> available_layers;
			for (const auto& layer : data_layers_) {
				available_layers.push_back(layer.first);
			}

			if (available_layers.empty()) {
				LOG_WARN("SingleGridMap: 地图中没有可用的图层");
				return false;
			}

			// 更新元数据中的可用图层列表
			metadata_.available_layers = available_layers;

			LOG_INFO("SingleGridMap: 图层检测完成，可用图层: [{}]", fmt::join(available_layers, ", "));
			return true;
		}

		void SingleGridMap::populateMetadataFromGeoTiff(const std::string& file_path,
			const std::pair<double, double>& resolution,
			int width, int height,
			const std::array<WGS84Point, 4>& corners) {

			// 基本文件信息
			metadata_.file_path = file_path;
			metadata_.file_format = "geotiff";
			metadata_.map_id = std::filesystem::path(file_path).stem().string();
			metadata_.map_name = "GridMap from " + metadata_.map_id;

			// 分辨率和尺寸信息
			metadata_.resolution = resolution;
			metadata_.grid_size = {width, height};

			// WGS84边界框
			double min_lat = std::min({corners[0].latitude, corners[1].latitude, corners[2].latitude, corners[3].latitude});
			double max_lat = std::max({corners[0].latitude, corners[1].latitude, corners[2].latitude, corners[3].latitude});
			double min_lon = std::min({corners[0].longitude, corners[1].longitude, corners[2].longitude, corners[3].longitude});
			double max_lon = std::max({corners[0].longitude, corners[1].longitude, corners[2].longitude, corners[3].longitude});

			metadata_.wgs84_bounds = WGS84BoundingBox(min_lon, max_lon, min_lat, max_lat);

			// 可用图层列表
			metadata_.available_layers.clear();
			for (const auto& layer : data_layers_) {
				metadata_.available_layers.push_back(layer.first);
			}

			// 注意：MapMetadata结构体没有coordinate_system和reference_point成员
			// 这些信息已经通过其他方式存储（如map_origin_和local_cartesian_）

			LOG_INFO("SingleGridMap: 元数据填充完成 - 地图ID: {}, 图层数: {}, 边界: {}",
				metadata_.map_id, metadata_.available_layers.size(), metadata_.wgs84_bounds.toString());
		}

		// ========== 基于局部坐标系和Eigen矩阵的查询方法 ==========

		bool SingleGridMap::hasLayer(const std::string& layer_name) const {
			return data_layers_.find(layer_name) != data_layers_.end();
		}

		Vector3D SingleGridMap::wgs84ToLocal(const WGS84Point& wgs84_point) const {
			std::lock_guard<std::mutex> lock(coordinate_mutex_);

			if (!local_cartesian_) {
				throw std::runtime_error("SingleGridMap: 局部坐标系统未初始化");
			}

			// 使用 GeographicLib 进行坐标转换
			double x, y, z;
			local_cartesian_->Forward(wgs84_point.latitude, wgs84_point.longitude, wgs84_point.altitude, x, y, z);

			return Vector3D(x, y, z);
		}

		WGS84Point SingleGridMap::localToWgs84(const Vector3D& local_point) const {
			std::lock_guard<std::mutex> lock(coordinate_mutex_);

			if (!local_cartesian_) {
				throw std::runtime_error("SingleGridMap: 局部坐标系统未初始化");
			}

			// 使用 GeographicLib 进行反向坐标转换
			double lat, lon, alt;
			local_cartesian_->Reverse(local_point.x(), local_point.y(), local_point.z(), lat, lon, alt);

			// 注意：WGS84Point构造函数参数顺序是 (经度, 纬度, 高度)
			return WGS84Point(lon, lat, alt);
		}

		bool SingleGridMap::wgs84ToMatrixIndex(const WGS84Point& wgs84_point, int& row, int& col) const {
			try {
				if (!initialized_ || !local_cartesian_) {
					LOG_ERROR("SingleGridMap: 地图未初始化或局部坐标系统未初始化");
					return false;
				}

				// 将WGS84坐标转换为局部坐标（相对于左上角）
				Vector3D local_pos = wgs84ToLocal(wgs84_point);

				// 计算像素位置
				// local_pos.x() 对应东向偏移（地图列）
				// local_pos.y() 对应北向偏移（地图行）
				// 注意：由于左上角是原点，正的local_y向北，但矩阵行索引向南增加，所以需要取负值
				double pixel_col = local_pos.x() / native_resolution_.first;
				double pixel_row = -local_pos.y() / native_resolution_.second;  // 注意负号

				// 转换为矩阵索引
				col = static_cast<int>(std::round(pixel_col));
				row = static_cast<int>(std::round(pixel_row));

				// 检查索引是否在有效范围内
				if (row < 0 || row >= grid_size_.second || col < 0 || col >= grid_size_.first) {
					LOG_TRACE("SingleGridMap: 矩阵索引超出范围 - row: {}, col: {}, 有效范围: [0, {}] x [0, {}]",
						row, col, grid_size_.second - 1, grid_size_.first - 1);
					return false;
				}

				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: WGS84坐标转换为矩阵索引时发生异常: {}", e.what());
				return false;
			}
		}

		// === WGS84Point版本的查询方法已移除，统一使用模板方法 ===

		// ========== 元数据访问方法 ==========

		MapMetadata SingleGridMap::getMetadata() const {
			return metadata_;
		}

		bool SingleGridMap::isInitialized() const {
			return initialized_;
		}

		bool SingleGridMap::isCovered(double latitude, double longitude) const {
			if (!initialized_) {
				return false;
			}

			try {
				// 检查是否在WGS84边界内
				if (!metadata_.wgs84_bounds.contains(latitude, longitude)) {
					return false;
				}

				// 转换为矩阵索引检查是否在有效范围内
				WGS84Point point(longitude, latitude, 0.0);
				int row, col;
				if (!wgs84ToMatrixIndex(point, row, col)) {
					return false;
				}

				// 检查是否有有效的高程数据（非NaN）
				if (!elevation_layer_name_.empty()) {
					auto it = data_layers_.find(elevation_layer_name_);
					if (it != data_layers_.end()) {
						float value = it->second(col, row);
						return !std::isnan(value);
					}
				}

				// 如果没有高程图层，检查是否有其他有效图层
				for (const auto& [layer_name, matrix] : data_layers_) {
					float value = matrix(col, row);
					if (!std::isnan(value)) {
						return true;
					}
				}

				return false;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 检查覆盖范围时发生异常: {}", e.what());
				return false;
			}
		}

		// === getAvailableLayers方法已移至TiledGridMap调用接口部分，避免重复 ===

		// ========== 缺失的方法实现 ==========

		bool SingleGridMap::loadFromPng(const std::string& png_file_path) {
			LOG_INFO("SingleGridMap: 开始从PNG文件加载地图数据: '{}'", png_file_path);

			try {
				// 检查文件是否存在
				if (!std::filesystem::exists(png_file_path)) {
					LOG_ERROR("SingleGridMap: PNG文件不存在: '{}'", png_file_path);
					return false;
				}

				// 注册GDAL驱动
				GDALAllRegister();

				// 打开PNG文件
				GDALDataset* dataset = (GDALDataset*)GDALOpen(png_file_path.c_str(), GA_ReadOnly);
				if (!dataset) {
					LOG_ERROR("SingleGridMap: 无法打开PNG文件: '{}'", png_file_path);
					return false;
				}

				// 使用RAII管理资源
				auto dataset_guard = std::unique_ptr<GDALDataset, decltype(&GDALClose)>(dataset, GDALClose);

				// 从PNG文件的元数据中恢复地图信息
				if (!loadMetadataFromPng(dataset)) {
					LOG_ERROR("SingleGridMap: 从PNG文件加载元数据失败");
					return false;
				}

				// 从PNG的像素数据中恢复矩阵数据
				if (!loadMatrixDataFromPng(dataset)) {
					LOG_ERROR("SingleGridMap: 从PNG文件加载矩阵数据失败");
					return false;
				}

				// 重建局部坐标系统
				local_cartesian_ = std::make_shared<GeographicLib::LocalCartesian>(
					map_origin_.latitude, map_origin_.longitude, map_origin_.altitude);

				initialized_ = true;
				LOG_INFO("SingleGridMap: PNG文件加载完成: '{}'", png_file_path);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 从PNG文件加载时发生异常: {}", e.what());
				return false;
			}
		}

		bool SingleGridMap::saveToPng(const std::string& output_path) {
			LOG_INFO("SingleGridMap: 开始保存到PNG文件: '{}'", output_path);

			try {
				if (!initialized_) {
					LOG_ERROR("SingleGridMap: 地图未初始化，无法保存");
					return false;
				}

				// 注册GDAL驱动
				GDALAllRegister();

				// 获取PNG驱动
				GDALDriver* driver = GetGDALDriverManager()->GetDriverByName("PNG");
				if (!driver) {
					LOG_ERROR("SingleGridMap: 无法获取PNG驱动");
					return false;
				}

				// 创建PNG文件（4通道RGBA，用于编码高程和元数据）
				GDALDataset* dataset = driver->Create(
					output_path.c_str(),
					grid_size_.first, grid_size_.second,
					4,  // RGBA 4个通道
					GDT_Byte,  // 8位无符号整数
					nullptr
				);

				if (!dataset) {
					LOG_ERROR("SingleGridMap: 无法创建PNG文件: {}", output_path);
					return false;
				}

				// 保存元数据到PNG的元数据标签
				if (!saveMetadataToPng(dataset)) {
					GDALClose(dataset);
					return false;
				}

				// 将矩阵数据编码到PNG的像素数据中
				if (!saveMatrixDataToPng(dataset)) {
					GDALClose(dataset);
					return false;
				}

				GDALClose(dataset);
				LOG_INFO("SingleGridMap: 成功保存到PNG文件: '{}'", output_path);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 保存到PNG文件时发生异常: {}", e.what());
				return false;
			}
		}

		// === getReferenceOrigin和getLayerMatrix方法已移至TiledGridMap调用接口部分，避免重复 ===

		// ========== PNG格式的元数据和矩阵数据编码/解码方法 ==========

		bool SingleGridMap::saveMetadataToPng(GDALDataset* dataset) {
			try {
				// 将元数据保存为PNG的文本标签
				dataset->SetMetadataItem("MAP_ID", metadata_.map_id.c_str());
				dataset->SetMetadataItem("MAP_NAME", metadata_.map_name.c_str());
				dataset->SetMetadataItem("FILE_FORMAT", metadata_.file_format.c_str());
				dataset->SetMetadataItem("COORDINATE_SYSTEM", "LocalCartesian");

				// 保存分辨率信息
				dataset->SetMetadataItem("RESOLUTION_X", std::to_string(metadata_.resolution.first).c_str());
				dataset->SetMetadataItem("RESOLUTION_Y", std::to_string(metadata_.resolution.second).c_str());

				// 保存网格尺寸
				dataset->SetMetadataItem("GRID_WIDTH", std::to_string(metadata_.grid_size.first).c_str());
				dataset->SetMetadataItem("GRID_HEIGHT", std::to_string(metadata_.grid_size.second).c_str());

				// 保存参考原点
				dataset->SetMetadataItem("ORIGIN_LONGITUDE", std::to_string(map_origin_.longitude).c_str());
				dataset->SetMetadataItem("ORIGIN_LATITUDE", std::to_string(map_origin_.latitude).c_str());
				dataset->SetMetadataItem("ORIGIN_ALTITUDE", std::to_string(map_origin_.altitude).c_str());

				// 保存WGS84边界框
				dataset->SetMetadataItem("BOUNDS_MIN_LON", std::to_string(metadata_.wgs84_bounds.minLongitude).c_str());
				dataset->SetMetadataItem("BOUNDS_MAX_LON", std::to_string(metadata_.wgs84_bounds.maxLongitude).c_str());
				dataset->SetMetadataItem("BOUNDS_MIN_LAT", std::to_string(metadata_.wgs84_bounds.minLatitude).c_str());
				dataset->SetMetadataItem("BOUNDS_MAX_LAT", std::to_string(metadata_.wgs84_bounds.maxLatitude).c_str());

				// 保存可用图层列表
				std::string layers_str;
				for (size_t i = 0; i < metadata_.available_layers.size(); ++i) {
					if (i > 0) layers_str += ",";
					layers_str += metadata_.available_layers[i];
				}
				dataset->SetMetadataItem("AVAILABLE_LAYERS", layers_str.c_str());

				LOG_DEBUG("SingleGridMap: PNG元数据保存完成");
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 保存PNG元数据时发生异常: {}", e.what());
				return false;
			}
		}

		bool SingleGridMap::loadMetadataFromPng(GDALDataset* dataset) {
			try {
				// 从PNG的文本标签中恢复元数据
				const char* map_id = dataset->GetMetadataItem("MAP_ID");
				const char* map_name = dataset->GetMetadataItem("MAP_NAME");
				const char* file_format = dataset->GetMetadataItem("FILE_FORMAT");
				const char* coordinate_system = dataset->GetMetadataItem("COORDINATE_SYSTEM");

				if (!map_id || !map_name || !file_format || !coordinate_system) {
					LOG_ERROR("SingleGridMap: PNG文件缺少必要的元数据");
					return false;
				}

				metadata_.map_id = map_id;
				metadata_.map_name = map_name;
				metadata_.file_format = file_format;
				// coordinate_system信息不存储在metadata_中，而是通过local_cartesian_管理

				// 恢复分辨率信息
				const char* res_x = dataset->GetMetadataItem("RESOLUTION_X");
				const char* res_y = dataset->GetMetadataItem("RESOLUTION_Y");
				if (res_x && res_y) {
					metadata_.resolution.first = std::stod(res_x);
					metadata_.resolution.second = std::stod(res_y);
					native_resolution_ = metadata_.resolution;
				}

				// 恢复网格尺寸
				const char* grid_w = dataset->GetMetadataItem("GRID_WIDTH");
				const char* grid_h = dataset->GetMetadataItem("GRID_HEIGHT");
				if (grid_w && grid_h) {
					metadata_.grid_size.first = std::stoi(grid_w);
					metadata_.grid_size.second = std::stoi(grid_h);
					grid_size_ = metadata_.grid_size;
				}

				// 恢复参考原点
				const char* origin_lon = dataset->GetMetadataItem("ORIGIN_LONGITUDE");
				const char* origin_lat = dataset->GetMetadataItem("ORIGIN_LATITUDE");
				const char* origin_alt = dataset->GetMetadataItem("ORIGIN_ALTITUDE");
				if (origin_lon && origin_lat && origin_alt) {
					map_origin_ = WGS84Point(std::stod(origin_lon), std::stod(origin_lat), std::stod(origin_alt));
					// reference_point信息不存储在metadata_中，而是通过map_origin_管理
				}

				// 恢复WGS84边界框
				const char* min_lon = dataset->GetMetadataItem("BOUNDS_MIN_LON");
				const char* max_lon = dataset->GetMetadataItem("BOUNDS_MAX_LON");
				const char* min_lat = dataset->GetMetadataItem("BOUNDS_MIN_LAT");
				const char* max_lat = dataset->GetMetadataItem("BOUNDS_MAX_LAT");
				if (min_lon && max_lon && min_lat && max_lat) {
					metadata_.wgs84_bounds = WGS84BoundingBox(
						std::stod(min_lon), std::stod(max_lon),
						std::stod(min_lat), std::stod(max_lat));
				}

				// 恢复可用图层列表
				const char* layers_str = dataset->GetMetadataItem("AVAILABLE_LAYERS");
				if (layers_str) {
					metadata_.available_layers.clear();
					std::string layers(layers_str);
					std::stringstream ss(layers);
					std::string layer;
					while (std::getline(ss, layer, ',')) {
						metadata_.available_layers.push_back(layer);
					}
				}

				LOG_DEBUG("SingleGridMap: PNG元数据加载完成");
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 加载PNG元数据时发生异常: {}", e.what());
				return false;
			}
		}

		bool SingleGridMap::saveMatrixDataToPng(GDALDataset* dataset) {
			try {
				// 获取高程图层
				auto it = data_layers_.find(elevation_layer_name_);
				if (it == data_layers_.end()) {
					LOG_ERROR("单个地图: 高程图层'{}'不存在，无法保存到PNG", elevation_layer_name_);
					return false;
				}

				const Eigen::MatrixXf& elevation_matrix = it->second;

				// 计算高程范围用于归一化
				float min_elevation = std::numeric_limits<float>::max();
				float max_elevation = std::numeric_limits<float>::lowest();
				int valid_count = 0;

				for (int row = 0; row < grid_size_.second; ++row) {
					for (int col = 0; col < grid_size_.first; ++col) {
						float value = elevation_matrix(col, row);
						if (!std::isnan(value)) {
							min_elevation = std::min(min_elevation, value);
							max_elevation = std::max(max_elevation, value);
							valid_count++;
						}
					}
				}

				if (valid_count == 0) {
					LOG_ERROR("SingleGridMap: 没有有效的高程数据");
					return false;
				}

				// 保存高程范围到元数据
				dataset->SetMetadataItem("ELEVATION_MIN", std::to_string(min_elevation).c_str());
				dataset->SetMetadataItem("ELEVATION_MAX", std::to_string(max_elevation).c_str());

				LOG_INFO("SingleGridMap: 高程范围: {:.2f}m 到 {:.2f}m", min_elevation, max_elevation);

				// 创建RGBA像素数据
				std::vector<uint8_t> rgba_data(grid_size_.first * grid_size_.second * 4);

				for (int row = 0; row < grid_size_.second; ++row) {
					for (int col = 0; col < grid_size_.first; ++col) {
						float elevation = elevation_matrix(col, row);
						int pixel_index = (row * grid_size_.first + col) * 4;

						uint8_t r, g, b, a;
						if (std::isnan(elevation)) {
							// NoData像素：透明
							r = g = b = 0;
							a = 0;
						} else {
							// 编码高程值到RGBA
							encodeElevationToRGBA(elevation, r, g, b, a);
						}

						rgba_data[pixel_index + 0] = r;
						rgba_data[pixel_index + 1] = g;
						rgba_data[pixel_index + 2] = b;
						rgba_data[pixel_index + 3] = a;
					}
				}

				// 写入RGBA数据到PNG的4个波段
				for (int band = 1; band <= 4; ++band) {
					GDALRasterBand* raster_band = dataset->GetRasterBand(band);
					if (!raster_band) {
						LOG_ERROR("SingleGridMap: 无法获取PNG波段 {}", band);
						return false;
					}

					// 提取对应通道的数据
					std::vector<uint8_t> channel_data(grid_size_.first * grid_size_.second);
					for (int i = 0; i < grid_size_.first * grid_size_.second; ++i) {
						channel_data[i] = rgba_data[i * 4 + (band - 1)];
					}

					CPLErr err = raster_band->RasterIO(GF_Write, 0, 0, grid_size_.first, grid_size_.second,
													  channel_data.data(), grid_size_.first, grid_size_.second,
													  GDT_Byte, 0, 0);

					if (err != CE_None) {
						LOG_ERROR("SingleGridMap: 写入PNG波段 {} 失败", band);
						return false;
					}
				}

				LOG_DEBUG("SingleGridMap: PNG矩阵数据保存完成");
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 保存PNG矩阵数据时发生异常: {}", e.what());
				return false;
			}
		}

		bool SingleGridMap::loadMatrixDataFromPng(GDALDataset* dataset) {
			try {
				// 从元数据中获取高程范围
				const char* min_elev_str = dataset->GetMetadataItem("ELEVATION_MIN");
				const char* max_elev_str = dataset->GetMetadataItem("ELEVATION_MAX");

				if (!min_elev_str || !max_elev_str) {
					LOG_ERROR("SingleGridMap: PNG文件缺少高程范围元数据");
					return false;
				}

				float min_elevation = std::stof(min_elev_str);
				float max_elevation = std::stof(max_elev_str);

				LOG_INFO("SingleGridMap: 从PNG恢复高程范围: {:.2f}m 到 {:.2f}m", min_elevation, max_elevation);

				// 读取RGBA数据
				std::vector<uint8_t> rgba_data(grid_size_.first * grid_size_.second * 4);

				for (int band = 1; band <= 4; ++band) {
					GDALRasterBand* raster_band = dataset->GetRasterBand(band);
					if (!raster_band) {
						LOG_ERROR("SingleGridMap: 无法获取PNG波段 {}", band);
						return false;
					}

					std::vector<uint8_t> channel_data(grid_size_.first * grid_size_.second);
					CPLErr err = raster_band->RasterIO(GF_Read, 0, 0, grid_size_.first, grid_size_.second,
													  channel_data.data(), grid_size_.first, grid_size_.second,
													  GDT_Byte, 0, 0);

					if (err != CE_None) {
						LOG_ERROR("SingleGridMap: 读取PNG波段 {} 失败", band);
						return false;
					}

					// 将通道数据合并到RGBA数组
					for (int i = 0; i < grid_size_.first * grid_size_.second; ++i) {
						rgba_data[i * 4 + (band - 1)] = channel_data[i];
					}
				}

				// 创建高程矩阵并解码数据
				Eigen::MatrixXf elevation_matrix(grid_size_.first, grid_size_.second);
				int valid_count = 0;

				for (int row = 0; row < grid_size_.second; ++row) {
					for (int col = 0; col < grid_size_.first; ++col) {
						int pixel_index = (row * grid_size_.first + col) * 4;

						uint8_t r = rgba_data[pixel_index + 0];
						uint8_t g = rgba_data[pixel_index + 1];
						uint8_t b = rgba_data[pixel_index + 2];
						uint8_t a = rgba_data[pixel_index + 3];

						if (a == 0) {
							// 透明像素表示NoData
							elevation_matrix(col, row) = std::numeric_limits<float>::quiet_NaN();
						} else {
							// 解码高程值
							float elevation = decodeElevationFromRGBA(r, g, b, a);
							elevation_matrix(col, row) = elevation;
							valid_count++;
						}
					}
				}

				// 存储到数据图层
				data_layers_[elevation_layer_name_] = std::move(elevation_matrix);

				LOG_INFO("SingleGridMap: PNG矩阵数据加载完成，有效像素: {}/{}", valid_count, grid_size_.first * grid_size_.second);
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("SingleGridMap: 加载PNG矩阵数据时发生异常: {}", e.what());
				return false;
			}
		}

		void SingleGridMap::encodeElevationToRGBA(float elevation, uint8_t& r, uint8_t& g, uint8_t& b, uint8_t& a) {
			// 使用32位浮点数的IEEE 754表示法进行编码
			// 将float转换为uint32_t，然后分解为4个字节
			union {
				float f;
				uint32_t i;
			} converter;

			converter.f = elevation;
			uint32_t bits = converter.i;

			// 分解为RGBA四个字节
			r = static_cast<uint8_t>((bits >> 24) & 0xFF);  // 最高字节
			g = static_cast<uint8_t>((bits >> 16) & 0xFF);
			b = static_cast<uint8_t>((bits >> 8) & 0xFF);
			a = static_cast<uint8_t>(bits & 0xFF);           // 最低字节

			// 确保alpha不为0（0表示NoData）
			if (a == 0) {
				a = 1;  // 最小非零值
			}
		}

		float SingleGridMap::decodeElevationFromRGBA(uint8_t r, uint8_t g, uint8_t b, uint8_t a) {
			// 从RGBA四个字节重构32位浮点数
			uint32_t bits = (static_cast<uint32_t>(r) << 24) |
							(static_cast<uint32_t>(g) << 16) |
							(static_cast<uint32_t>(b) << 8) |
							static_cast<uint32_t>(a);

			union {
				float f;
				uint32_t i;
			} converter;

			converter.i = bits;
			return converter.f;
		}

	// === TiledGridMap调用接口实现 ===

	WGS84BoundingBox SingleGridMap::getBoundingBox() const {
		std::lock_guard<std::mutex> lock(coordinate_mutex_);
		return metadata_.wgs84_bounds;
	}

	std::pair<double, double> SingleGridMap::getResolution() const {
		std::lock_guard<std::mutex> lock(coordinate_mutex_);
		return native_resolution_;
	}

	std::pair<int, int> SingleGridMap::getGridSize() const {
		std::lock_guard<std::mutex> lock(coordinate_mutex_);
		return grid_size_;
	}

	bool SingleGridMap::containsLayer(const std::string& layer_name) const {
		std::lock_guard<std::mutex> lock(coordinate_mutex_);
		return data_layers_.find(layer_name) != data_layers_.end();
	}

	std::string SingleGridMap::getTileId() const {
		std::lock_guard<std::mutex> lock(coordinate_mutex_);
		// 从文件路径生成瓦片ID
		if (!metadata_.file_path.empty()) {
			std::filesystem::path file_path(metadata_.file_path);
			return file_path.stem().string();
		}
		return metadata_.map_id;
	}

	std::vector<std::string> SingleGridMap::getAvailableLayers() const {
		std::lock_guard<std::mutex> lock(coordinate_mutex_);
		std::vector<std::string> layers;
		layers.reserve(data_layers_.size());

		for (const auto& [layer_name, matrix] : data_layers_) {
			layers.push_back(layer_name);
		}

		return layers;
	}

	std::optional<Eigen::MatrixXf> SingleGridMap::getLayerMatrix(const std::string& layer_name) const {
		std::lock_guard<std::mutex> lock(coordinate_mutex_);

		auto it = data_layers_.find(layer_name);
		if (it != data_layers_.end()) {
			return it->second;  // 返回矩阵的拷贝
		}

		return std::nullopt;
	}

	WGS84Point SingleGridMap::getReferenceOrigin() const {
		std::lock_guard<std::mutex> lock(coordinate_mutex_);
		return map_origin_;
	}

	} // namespace NSEnvironment
} // namespace NSDrones
