// src/planning/mission_planner.cpp
#include "core/types.h"
#include "planning/mission_planner.h"
#include "environment/environment.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "uav/uav.h"
#include "algorithm/allocator/itask_allocator.h"
#include "algorithm/allocator/task_allocator.h"
#include "planning/itask_planner.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "utils/geometry_manager.h"
#include "utils/logging.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <thread>
#include <future>
#include <vector>
#include <map>
#include <set>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <stdexcept>
#include <typeinfo>

namespace NSDrones {
	namespace NSPlanning {

		// === 构造函数实现 ===

		MissionPlanner::MissionPlanner() {
			LOG_INFO("任务规划器: MissionPlanner初始化完成");
		}

		// === 环境和算法组件访问实现 ===

		std::shared_ptr<NSEnvironment::Environment> MissionPlanner::getEnvironment() const {
			return NSEnvironment::Environment::getInstance();
		}

		IPathPlannerPtr MissionPlanner::getPathPlanner() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getPathPlanner();
		}

		ITrajectoryOptimizerPtr MissionPlanner::getTrajectoryOptimizer() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryOptimizer();
		}

		ITrajectoryEvaluatorPtr MissionPlanner::getTrajectoryEvaluator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryEvaluator();
		}

		ITaskAllocatorPtr MissionPlanner::getTaskAllocator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTaskAllocator();
		}

		// === 规划器注册管理实现 ===

		void MissionPlanner::registerTaskPlanner(NSMission::TaskType task_type, TaskPlannerPtr planner) {
			if (planner) {
				std::string type_str = NSUtils::enumToString(task_type);
				if (task_planners_.count(task_type)) {
					LOG_WARN("任务规划器: 覆盖任务类型[{}]的规划器", type_str);
				}
				task_planners_[task_type] = planner;
				LOG_INFO("任务规划器: 已注册任务类型[{}]的规划器", type_str);
			} else {
				LOG_WARN("任务规划器: 尝试注册空的规划器，任务类型[{}]，已忽略",
					NSUtils::enumToString(task_type));
			}
		}

		// === 无人机资源管理实现 ===

		std::vector<NSUav::UavPtr> MissionPlanner::getAvailableUAVs() const {
			std::lock_guard<std::mutex> lock(uav_list_mutex_);
			return available_uavs_; // 返回列表副本
		}

		void MissionPlanner::addAvailableUAV(NSUav::UavPtr uav) {
			if (!uav) {
				LOG_WARN("任务规划器: 尝试添加空的UAV指针，已忽略");
				return;
			}

			const NSUtils::ObjectID& uav_id = uav->getId();
			std::lock_guard<std::mutex> lock(uav_list_mutex_);

			// 检查是否已存在
			bool found = std::any_of(available_uavs_.begin(), available_uavs_.end(),
				[&uav_id](const NSUav::UavPtr& existing_uav) {
					return existing_uav && existing_uav->getId() == uav_id;
				});

			if (!found) {
				available_uavs_.push_back(uav);
				LOG_INFO("任务规划器: 已添加可用无人机{}，当前总数{}", uav_id, available_uavs_.size());
			} else {
				LOG_DEBUG("任务规划器: 无人机{}已存在，忽略添加请求", uav_id);
			}
		}

		bool MissionPlanner::removeAvailableUAV(const NSUtils::ObjectID& uav_id) {
			if (!NSUtils::isValidObjectID(uav_id)) {
				LOG_WARN("任务规划器: 尝试移除无效的UAV ID '{}'，已忽略", uav_id);
				return false;
			}

			std::lock_guard<std::mutex> lock(uav_list_mutex_);

			// 使用remove_if + erase删除匹配的无人机
			auto new_end = std::remove_if(available_uavs_.begin(), available_uavs_.end(),
				[&uav_id](const NSUav::UavPtr& uav) {
					return uav && uav->getId() == uav_id;
				});

			if (new_end != available_uavs_.end()) {
				available_uavs_.erase(new_end, available_uavs_.end());
				LOG_INFO("任务规划器: 已移除可用无人机{}，当前总数{}", uav_id, available_uavs_.size());
				return true;
			} else {
				LOG_WARN("任务规划器: 未找到要移除的无人机ID: {}", uav_id);
				return false;
			}
		}

		// === 规划器查找实现 ===

		TaskPlannerPtr MissionPlanner::findPlannerForTask(const NSMission::Task& task) const {
			auto it = task_planners_.find(task.getType());
			if (it != task_planners_.end()) {
				return it->second;
			} else {
				LOG_WARN("任务规划器: 未找到任务类型[{}]的注册规划器",
					NSUtils::enumToString(task.getType()));
				return nullptr;
			}
		}

		// === 核心规划接口实现 ===

		PlanningResult MissionPlanner::planMission(const NSMission::Mission& mission) {
			LOG_INFO("任务规划器: 开始规划任务计划，ID: {}", mission.getId());

			PlanningResult overall_result;
			overall_result.setStatus(true);

			// 获取可用无人机
			std::vector<NSUav::UavPtr> current_available_uavs = getAvailableUAVs();
			LOG_INFO("任务规划器: 当前可用无人机数量: {}", current_available_uavs.size());

			if (current_available_uavs.empty() && !mission.getTasks().empty()) {
				overall_result.setStatus(false, "无可用无人机执行任务");
				LOG_ERROR("任务规划器: {}", overall_result.getMessage());
				return overall_result;
			}

			// 初始化状态映射
			std::map<NSUtils::ObjectID, NSUav::UavState> next_start_states;
			std::map<NSUtils::ObjectID, NSUav::UavPtr> uav_map;
			std::map<NSUtils::ObjectID, NSUav::UavState> mission_initial_states;

			for (const auto& uav : current_available_uavs) {
				if (uav) {
					NSUtils::ObjectID uav_id = uav->getId();
					NSUav::UavState initial_state = uav->getUavState();
					next_start_states[uav_id] = initial_state;
					mission_initial_states[uav_id] = initial_state;
					uav_map[uav_id] = uav;
				}
			}

			LOG_INFO("步骤 1: 调用任务分配器进行分配...");
			auto task_allocator = getTaskAllocator();
			if (!task_allocator) {
				LOG_ERROR("任务规划器: 任务分配器未初始化");
				result.setStatus(false, "任务分配器未初始化");
				return result;
			}
			TaskAllocationResult allocation_result = task_allocator->allocate(mission, current_available_uavs, next_start_states);

			if (!allocation_result.success) {
				std::string err_msg = "任务分配失败: " + allocation_result.message;
				LOG_ERROR(err_msg);
				overall_result.setStatus(false, err_msg);
				for (const auto& unalloc_task_id : allocation_result.unallocated_tasks) {
					overall_result.addWarning(WarningEvent(WarningType::RESOURCE_UNAVAILABLE, "任务 [" + unalloc_task_id + "] 未能分配到无人机。", 0, {}, "", unalloc_task_id));
				}
				return overall_result;
			}
			LOG_INFO("任务分配完成，共 {} 个任务被成功分配。", allocation_result.allocation.size());

			std::map<ObjectID, PlannedRoute> mission_routes;
			int task_counter = 0;
			for (const auto& task_ptr : mission.getTasks()) {
				task_counter++;
				if (!task_ptr) {
					LOG_WARN("任务列表中发现空任务指针，已跳过任务 #{}。", task_counter);
					continue;
				}
				const NSMission::Task& task = *task_ptr;
				const ObjectID& task_id = task.getId();
				LOG_INFO("===> 开始规划任务 #{} (ID: {}, 类型: {})", task_counter, task_id, NSUtils::enumToString(task.getType()));

				auto alloc_it = allocation_result.allocation.find(task_id);
				if (alloc_it == allocation_result.allocation.end() || alloc_it->second.empty()) {
					LOG_WARN("任务 [{}] 在分配结果中没有找到无人机或列表为空，跳过此任务规划。", task_id);
					continue;
				}
				const std::vector<ObjectID>& assigned_uav_ids = alloc_it->second;
				std::vector<NSUav::UavPtr> task_uavs;
				task_uavs.reserve(assigned_uav_ids.size());
				LOG_INFO("  任务 [{}] 分配给 {} 架无人机:", task_id, assigned_uav_ids.size());
				bool missing_uav_ptr = false;
				for (const auto& uav_id : assigned_uav_ids) {
					auto uav_ptr_it = uav_map.find(uav_id);
					if (uav_ptr_it != uav_map.end()) {
						task_uavs.push_back(uav_ptr_it->second);
						LOG_INFO("    - {}", uav_id);
					}
					else {
						LOG_ERROR("内部错误：分配给任务 [{}] 的无人机 [{}] 在 UAV Map 中找不到！", task_id, uav_id);
						missing_uav_ptr = true;
						break;
					}
				}
				if (missing_uav_ptr) {
					overall_result.setStatus(false, "内部错误：分配的无人机对象丢失。");
					break; // 使用break代替goto
				}

				auto suitable_planner = findPlannerForTask(task);
				if (!suitable_planner) {
					std::string err_msg = "未找到任务类型 " + NSUtils::enumToString(task.getType()) + " 的规划器，任务规划中止。";
					LOG_ERROR(err_msg);
					overall_result.addWarning(WarningEvent(WarningType::PLANNER_NOT_FOUND, err_msg, 0.0, {}, "", task.getId()));
					overall_result.setStatus(false, err_msg);
					break; // 使用break代替goto
				}
				LOG_DEBUG("  使用规划器: {}", typeid(*suitable_planner).name());

				LOG_DEBUG("  准备任务起始状态...");
				std::map<ObjectID, NSUav::UavState> task_start_states;
				for (const auto& uav : task_uavs) {
					if (!uav) continue;
					ObjectID uav_id = uav->getId();
					if (next_start_states.count(uav_id)) {
						task_start_states[uav_id] = next_start_states[uav_id];
						LOG_TRACE("    无人机 [{}] 起始状态: t={:.2f}, pos=({}), E={:.1f}", uav_id,
							task_start_states[uav_id].time_stamp,
							task_start_states[uav_id].position.toString(),
							task_start_states[uav_id].current_energy);
					}
					else {
						std::string err_msg = "内部错误：未找到选定无人机 [" + uav_id + "] 的预期起始状态，任务规划中止。";
						LOG_ERROR(err_msg);
						overall_result.addWarning(WarningEvent(WarningType::INVALID_STATE, err_msg, 0.0, {}, uav_id, task.getId()));
						overall_result.setStatus(false, err_msg);
						break; // 使用break代替goto
					}
				}

				LOG_INFO("  调用任务规划器执行规划...");
				try {
					PlanningResult task_result = suitable_planner->planTask(task, task_uavs, task_start_states);

					LOG_INFO("  任务 [{}] 规划完成，结果: {}", task.getId(), task_result.wasSuccessful() ? "成功" : "失败");
					if (task_result.wasSuccessful()) {
						LOG_DEBUG("  开始合并任务 [{}] 的成功结果...", task.getId());
						for (const auto& pair : task_result.getAllRoutes()) {
							const ObjectID& uav_id = pair.first;
							const PlannedRoute& new_route_segment = pair.second;
							if (!new_route_segment.isEmpty()) {
								LOG_DEBUG("    合并无人机 [{}] 的航线段 ({} 个点)", uav_id, new_route_segment.getWaypoints().size());
								if (!mission_routes.count(uav_id)) {
									LOG_TRACE("      首次为无人机 [{}] 创建航线。", uav_id);
									mission_routes[uav_id] = PlannedRoute(uav_id);
								}
								PlannedRoute& total_route = mission_routes[uav_id];
								total_route.addWaypoints(new_route_segment.getWaypoints());
								if (!new_route_segment.isEmpty()) {
									LOG_TRACE("      更新无人机 [{}] 的下一个起始状态。", uav_id);
									NSUav::UavState last_task_state = next_start_states[uav_id];
									NSUav::UavState next_start = NSUav::stateFromRoutePt(new_route_segment.getEndPoint());
									next_start.current_energy = last_task_state.current_energy;
									next_start_states[uav_id] = next_start;
									LOG_TRACE("        新起始: t={:.2f}, pos=({}), E={:.1f}",
										next_start_states[uav_id].time_stamp,
										next_start_states[uav_id].position.toString(),
										next_start_states[uav_id].current_energy);
								}
								else {
									LOG_WARN("      无人机 [{}] 规划的任务航段为空，无法更新下一个起始状态。", uav_id);
								}
							}
							else {
								LOG_WARN("    任务结果中包含无人机 [{}] 的空航线，已忽略。", uav_id);
							}
						}
						if (!task_result.getWarnings().empty()) {
							LOG_DEBUG("    合并 {} 条任务告警...", task_result.getWarnings().size());
							for (const auto& warning : task_result.getWarnings()) {
								overall_result.addWarning(warning);
							}
						}
					}
					else {
						std::string err_msg = "任务规划失败，ID [" + task.getId() + "]: " + task_result.getMessage();
						LOG_ERROR(err_msg);
						if (!task_result.getWarnings().empty()) {
							LOG_DEBUG("    合并 {} 条任务失败相关的告警...", task_result.getWarnings().size());
							for (const auto& warning : task_result.getWarnings()) {
								overall_result.addWarning(warning);
							}
						}
						overall_result.addWarning(WarningEvent(WarningType::PLANNING_FAILURE, err_msg, 0.0, {}, "", task.getId()));
						overall_result.setStatus(false, err_msg);
						break; // 使用break代替goto
					}
				}
				catch (const std::exception& e) {
					std::string err_msg = "规划任务 ID [" + task.getId() + "] 时发生异常: " + e.what();
					LOG_ERROR(err_msg);
					overall_result.addWarning(WarningEvent(WarningType::UNKNOWN, err_msg, 0.0, {}, "", task.getId()));
					overall_result.setStatus(false, err_msg);
					break; // 使用break代替goto
				}
				catch (...) {
					std::string err_msg = "规划任务 ID [" + task.getId() + "] 时发生未知异常。";
					LOG_ERROR(err_msg);
					overall_result.addWarning(WarningEvent(WarningType::INTERNAL_ERROR, err_msg, 0.0, {}, "", task.getId()));
					overall_result.setStatus(false, err_msg);
					break; // 使用break代替goto
				}
			} // 结束任务循环

			// 规划完成，记录结果统计
			if (overall_result.wasSuccessful() && !mission_routes.empty()) {
				LOG_INFO("任务规划器: 所有任务规划完成，生成{}条航线", mission_routes.size());
				for (const auto& [uav_id, route] : mission_routes) {
					if (!route.isEmpty()) {
						LOG_DEBUG("任务规划器: 无人机[{}]航线包含{}个航点，总时长{:.2f}s",
							uav_id, route.getWaypoints().size(), route.getTotalTime());
					}
				}
			}


			// --- 最终结果处理 ---
			if (overall_result.wasSuccessful()) {
				if (mission_routes.empty() && !mission.getTasks().empty()) {
					overall_result.setStatus(false, "成功规划了所有任务，但未生成任何有效航线。");
					LOG_WARN("{}", overall_result.getMessage());
				}
				else if (!mission_routes.empty()) {
					LOG_DEBUG("将 {} 条合并后的无人机航线添加到最终结果...", mission_routes.size());
					for (auto& pair : mission_routes) {
						overall_result.addRoute(std::move(pair.second));
					}
				}
				else {
					LOG_INFO("任务计划为空或未生成任何航线。");
				}
			}
			else {
				LOG_ERROR("任务规划过程失败，不添加航线到最终结果。失败原因: {}", overall_result.getMessage());
			}

			LOG_INFO("任务规划完成，任务计划 ID: {}. 最终成功: {}. 生成 {} 条无人机航线, {} 条总体告警。",
				mission.getId(), overall_result.wasSuccessful(), overall_result.getAllRoutes().size(), overall_result.getWarnings().size());
			return overall_result;
		}


		/**
		 * @brief 规划单个任务。
		 */
		PlanningResult MissionPlanner::planSingleTask(const NSMission::Task& task) {
			LOG_INFO("开始规划单个任务，任务 ID: {}, 类型: {}", task.getId(), NSUtils::enumToString(task.getType()));
			PlanningResult result;
			result.setStatus(true); // 先假设成功

			std::vector<NSUav::UavPtr> current_available_uavs = getAvailableUAVs();
			if (current_available_uavs.empty()) {
				result.setStatus(false, "无可用无人机执行任务。");
				LOG_ERROR("{}", result.getMessage());
				result.addWarning(WarningEvent(WarningType::RESOURCE_UNAVAILABLE, result.getMessage(), 0, {}, "", task.getId()));
				return result;
			}
			std::map<ObjectID, NSUav::UavState> current_states;
			std::map<ObjectID, NSUav::UavPtr> uav_map;
			for (const auto& uav : current_available_uavs) {
				if (uav) {
					current_states[uav->getId()] = uav->getUavState();
					uav_map[uav->getId()] = uav;
				}
			}

			LOG_INFO("步骤 1: 调用任务分配器, 为单个任务分配无人机...");

			TaskAllocationResult allocation_result;
			allocation_result.success = false; // 默认失败

			if (!current_available_uavs.empty()) {
				// 实现更智能的单任务分配逻辑
				NSUav::UavPtr best_uav = nullptr;
				double best_score = -1.0;

				// 为每个可用无人机计算适合度分数
				for (const auto& uav : current_available_uavs) {
					if (!uav) continue;

					double score = calculateUavTaskFitness(uav, task, current_states[uav->getId()]);
					LOG_TRACE("无人机 {} 对任务 {} 的适合度分数: {:.3f}", uav->getId(), task.getId(), score);

					if (score > best_score) {
						best_score = score;
						best_uav = uav;
					}
				}

				if (best_uav && best_score > 0.0) {
					allocation_result.success = true;
					allocation_result.allocation[task.getId()] = { best_uav->getId() };
					LOG_INFO("planSingleTask: 将任务 {} 分配给最适合的无人机 {} (适合度: {:.3f})",
						task.getId(), best_uav->getId(), best_score);
				}
				else {
					allocation_result.message = "没有无人机适合执行此任务";
					LOG_WARN("planSingleTask: 没有找到适合执行任务 {} 的无人机", task.getId());
				}
			}
			else {
				allocation_result.message = "无可用无人机";
			}

			if (!allocation_result.success || allocation_result.allocation.find(task.getId()) == allocation_result.allocation.end()) {
				std::string err_msg = "任务分配失败: " + allocation_result.message;
				if (allocation_result.allocation.find(task.getId()) == allocation_result.allocation.end()) {
					err_msg = "任务 [" + task.getId() + "] 未能分配到无人机。";
				}
				LOG_ERROR(err_msg);
				result.setStatus(false, err_msg);
				result.addWarning(WarningEvent(WarningType::RESOURCE_UNAVAILABLE, err_msg, 0, {}, "", task.getId()));
				return result;
			}

			const std::vector<ObjectID>& assigned_uav_ids = allocation_result.allocation.at(task.getId());
			if (assigned_uav_ids.empty()) {
				std::string err_msg = "任务 [" + task.getId() + "] 分配结果为空。";
				LOG_ERROR(err_msg);
				result.setStatus(false, err_msg);
				result.addWarning(WarningEvent(WarningType::ALLOCATION_ERROR, err_msg, 0, {}, "", task.getId()));
				return result;
			}
			std::vector<NSUav::UavPtr> task_uavs;
			std::map<ObjectID, NSUav::UavState> task_start_states;
			LOG_INFO("  任务 [{}] 分配给 {} 架无人机:", task.getId(), assigned_uav_ids.size());
			bool missing_uav_ptr = false;
			for (const auto& uav_id : assigned_uav_ids) {
				auto uav_ptr_it = uav_map.find(uav_id);
				if (uav_ptr_it != uav_map.end()) {
					task_uavs.push_back(uav_ptr_it->second);
					task_start_states[uav_id] = current_states[uav_id];
					LOG_INFO("    - {}", uav_id);
				}
				else {
					LOG_ERROR("内部错误：分配给任务 [{}] 的无人机 [{}] 在 UAV Map 中找不到！", task.getId(), uav_id);
					missing_uav_ptr = true;
					break;
				}
			}
			if (missing_uav_ptr) {
				result.setStatus(false, "内部错误：分配的无人机对象丢失。");
				result.addWarning(WarningEvent(WarningType::INTERNAL_ERROR, result.getMessage(), 0, {}, "", task.getId()));
				return result;
			}


			auto suitable_planner = findPlannerForTask(task);
			if (!suitable_planner) {
				std::string err_msg = "未找到任务类型 " + NSUtils::enumToString(task.getType()) + " 的规划器。";
				LOG_ERROR(err_msg);
				result.addWarning(WarningEvent(WarningType::PLANNER_NOT_FOUND, err_msg, 0.0, {}, "", task.getId()));
				result.setStatus(false, err_msg);
				return result;
			}
			LOG_DEBUG("  使用规划器: {}", typeid(*suitable_planner).name());


			// 调用具体 TaskPlanner 进行规划
			PlanningResult task_result = suitable_planner->planTask(task, task_uavs, task_start_states);

			if (!task_result.wasSuccessful()) {
				LOG_WARN("任务 [{}] 规划失败: {}", task.getId(), task_result.getMessage());
				// 合并告警，设置最终状态
				for (const auto& warn : task_result.getWarnings()) {
					result.addWarning(warn);
				}
				// 如果任务规划失败，最终结果也失败
				result.setStatus(false, "任务 [" + task.getId() + "] 规划失败: " + task_result.getMessage());
				return result; // 直接返回，不进行后续评估
			}

			// 合并航线和告警
			for (const auto& [uav_id, route] : task_result.getAllRoutes()) {
				result.addRoute(route); // 移动语义
			}
			for (const auto& warn : task_result.getWarnings()) {
				result.addWarning(warn); // 移动语义
			}

			// --- 全局航线评估（MissionPlanner层面） ---
			auto trajectory_evaluator = getTrajectoryEvaluator();
			if (trajectory_evaluator && result.wasSuccessful()) {
				LOG_INFO("任务规划器: 对任务[{}]进行全局航线评估", task.getId());
				bool all_feasible = true;

				for (auto& [uav_id, route] : result.getAllRoutes()) {
					if (route.isEmpty()) {
						LOG_DEBUG("任务规划器: 无人机[{}]航线为空，跳过评估", uav_id);
						continue;
					}
					auto uav_it = uav_map.find(uav_id);
					if (uav_it == uav_map.end()) {
						LOG_WARN("任务规划器: 找不到无人机[{}]对象，无法进行全局评估", uav_id);
						continue;
					}

					LOG_DEBUG("任务规划器: 对无人机[{}]进行全局航线评估", uav_id);

					// 将PlannedRoute转换为Trajectory
					Trajectory trajectory_to_evaluate;
					TrajectorySegment segment_to_evaluate;
					const auto& waypoints = route.getWaypoints();
					segment_to_evaluate.states.reserve(waypoints.size());

					if (!waypoints.empty()) {
						segment_to_evaluate.duration = route.getTotalTime();
						for (const auto& rp : waypoints) {
							segment_to_evaluate.states.push_back(NSUav::stateFromRoutePt(rp));
						}
						trajectory_to_evaluate.push_back(segment_to_evaluate);
					}

					if (trajectory_to_evaluate.empty()) {
						LOG_WARN("任务规划器: 无人机[{}]转换后的轨迹为空，跳过全局评估", uav_id);
						continue;
					}

					// 进行全局评估（考虑任务上下文）
					TrajectoryCost eval_result = trajectory_evaluator->evaluate(
						uav_it->second, trajectory_to_evaluate, nullptr);

					if (!eval_result.is_feasible) {
						all_feasible = false;
						std::string warn_msg = "全局航线评估失败(无人机[" + uav_id + "]): " + eval_result.message;
						LOG_WARN("任务规划器: {}", warn_msg);

						NSCore::WGS84Point warn_location = route.isEmpty() ?
							NSCore::WGS84Point(NAN, NAN, NAN) : route.getStartPoint().position;
						result.addWarning(WarningEvent(WarningType::TRAJECTORY_INFEASIBLE,
							warn_msg, 0.0, warn_location, uav_id, {}, task.getId()));
					} else {
						LOG_INFO("任务规划器: 无人机[{}]全局航线评估通过", uav_id);
						// 可以在这里存储评估成本等信息
					}
				}

				// 全局评估结果总结
				if (!all_feasible) {
					LOG_ERROR("任务规划器: 任务[{}]的全局航线评估未通过", task.getId());
					result.setStatus(false, "任务[" + task.getId() + "]全局航线评估失败，存在不可行路径");
				} else {
					LOG_INFO("任务规划器: 任务[{}]的所有航线全局评估通过", task.getId());
				}
			} else if (!trajectory_evaluator && result.wasSuccessful()) {
				LOG_DEBUG("任务规划器: 未配置轨迹评估器，跳过任务[{}]的全局评估", task.getId());
			}

			LOG_INFO("单个任务 [{}] 规划完成。最终状态: {}", task.getId(), result.wasSuccessful() ? "成功" : "失败");
			return result;
		}

		// --- 私有辅助函数 ---

		/** @brief 加载 MissionPlanner 自身的参数。*/
		void MissionPlanner::loadParams() {
			// 暂时没有 MissionPlanner 特有的参数需要加载
			LOG_DEBUG("MissionPlanner::loadParams - 当前无特定参数加载。");
		}

		/**
		 * @brief 计算单个任务的成本。
		 * @param task 任务对象。
		 * @param uav 执行任务的无人机。
		 * @param route 任务的规划航线。
		 * @param result 用于添加告警。
		 * @return 任务成本 (TrajectoryCost)。如果无效则 is_feasible 为 false。
		 */
		TrajectoryCost MissionPlanner::evaluateTaskCost(const NSMission::Task& task,
			const NSUav::UavPtr& uav, // 使用 UAVPtr
			const PlannedRoute& route,
			PlanningResult& result) {
			LOG_TRACE("开始评估任务 {} (UAV: {}) 的成本...", task.getId(), uav->getId());

			TrajectoryCost cost;
			cost.is_feasible = false; // 默认不可行

			auto trajectory_evaluator = getTrajectoryEvaluator();
			if (!trajectory_evaluator) {
				LOG_ERROR("任务规划器: 评估任务{}成本失败，轨迹评估器无效", task.getId());
				result.addWarning(WarningEvent(WarningType::INTERNAL_ERROR, "轨迹评估器无效", 0.0, {}, uav->getId(), {}, task.getId()));
				return cost;
			}
			if (route.isEmpty()) {
				LOG_WARN("评估任务 {} 成本：规划路径为空，视为不可行。", task.getId());
				cost.message = "路径为空";
				return cost;
			}

			// **评估逻辑**
			// 1. 将 PlannedRoute 转换为 Trajectory
			Trajectory trajectory_to_evaluate;
			// 假设 PlannedRoute 整体作为一个 TrajectorySegment 处理
			// (如果 PlannedRoute 内部结构更复杂，可能需要更精细的转换)
			TrajectorySegment segment_to_evaluate;
			const auto& waypoints = route.getWaypoints();
			segment_to_evaluate.states.reserve(waypoints.size());
			if (!waypoints.empty()) {
				segment_to_evaluate.duration = route.getTotalTime();
				for (const auto& rp : waypoints) {
					// 需要一个辅助函数将 RoutePoint 转换为 UAVState
					// 假设存在 NSUav::stateFromRoutePt(const RoutePoint&)
					segment_to_evaluate.states.push_back(NSUav::stateFromRoutePt(rp));
				}
				trajectory_to_evaluate.push_back(segment_to_evaluate);
			}

			if (trajectory_to_evaluate.empty()) {
				LOG_WARN("评估任务 {} 成本：转换后的轨迹为空。", task.getId());
				cost.message = "转换后的轨迹为空";
				return cost;
			}

			// 2. 调用 evaluateTrajectory 并获取 TrajectoryCost
			TrajectoryCost eval_result = trajectory_evaluator->evaluate(
				uav, // 使用 UAVPtr
				trajectory_to_evaluate,
				nullptr // 暂不传递 Mission 指针
			);

			if (eval_result.is_feasible) {
				LOG_DEBUG("任务 {} (UAV: {}) 成本评估完成：可行，能量消耗={:.2f} Wh, 剩余续航={:.1f}s",
					task.getId(), uav->getId(),
					eval_result.estimated_energy_consumption.value_or(-1.0),
					eval_result.estimated_endurance_remaining.value_or(-1.0));
				cost = eval_result; // 使用评估结果
			}
			else {
				LOG_WARN("任务 {} (UAV: {}) 成本评估完成：不可行。原因: {}", task.getId(), uav->getId(), eval_result.message);
				cost.message = "评估结果不可行: " + eval_result.message;
				// 添加告警
				result.addWarning(WarningEvent(WarningType::TRAJECTORY_INFEASIBLE,
					"轨迹评估不可行: " + eval_result.message, route.getStartPoint().time_stamp, route.getStartPoint().position, uav->getId(), {}, task.getId()));
			}

			return cost;
		}

		// --- 私有辅助方法实现 ---

		// 辅助函数：从TaskTargetVariant中提取位置信息
		std::optional<WGS84Point> MissionPlanner::getTaskPosition(const NSMission::Task& task) const {
			const auto& target = task.getTarget();

			return std::visit([this](const auto& target_ptr) -> std::optional<WGS84Point> {
				using T = std::decay_t<decltype(*target_ptr)>;

				if constexpr (std::is_same_v<T, NSMission::PointTarget>) {
					return target_ptr->getPosition();
				}
				else if constexpr (std::is_same_v<T, NSMission::LineTarget>) {
					const auto& points = target_ptr->getPoints();
					if (!points.empty()) {
						return points[0]; // 返回第一个点
					}
				}
				else if constexpr (std::is_same_v<T, NSMission::AreaTarget>) {
					const auto& boundary = target_ptr->getBoundary();
					if (!boundary.empty()) {
						return boundary[0]; // 返回第一个顶点
					}
				}
				else if constexpr (std::is_same_v<T, NSMission::VolumeTarget>) {
					// VolumeTarget 可能需要转换，暂时跳过
					// return target_ptr->getCenter();
				}
				else if constexpr (std::is_same_v<T, NSMission::ObjectTarget>) {
					// 对于对象目标，需要从环境中查找对象位置
					auto environment = getEnvironment();
					if (environment) {
						auto obj = environment->getObjectById(target_ptr->getTargetId());
						if (obj) {
							// 需要将局部坐标转换为WGS84坐标
							auto task_space = environment->getTaskSpace();
							if (task_space) {
								auto ned_pos_opt = obj->getNedPosition();
								if (ned_pos_opt.has_value()) {
									return task_space->nedToWGS84(ned_pos_opt.value());
								}
							}
						}
					}
				}
				return std::nullopt;
				}, target);
		}

		// 辅助函数：检查任务是否有载荷要求
		std::optional<double> MissionPlanner::getTaskPayloadRequirement(const NSMission::Task& task) const {
			const auto& requirements = task.getRequirements();
			// 这里需要根据CapabilityRequirement的实际结构来实现
			// 暂时返回空，表示没有载荷要求
			return std::nullopt;
		}

		double MissionPlanner::calculateUavTaskFitness(const NSUav::UavPtr& uav,
			const NSMission::Task& task, const NSUav::UavState& uav_state) const {
			if (!uav) {
				return 0.0;
			}

			double fitness_score = 0.0;

			// 1. 基础适合度分数
			fitness_score += 1.0;

			// 2. 能量适合度 (权重: 30%)
			double max_energy = 0.0;
			if (auto energy_model = uav->getEnergyModel()) {
				max_energy = energy_model->getMaxEnergyCapacity();
			}

			if (max_energy > 0.0) {
				double energy_ratio = uav_state.current_energy / max_energy;
				if (energy_ratio > 0.8) {
					fitness_score += 0.3; // 高能量
				}
				else if (energy_ratio > 0.5) {
					fitness_score += 0.2; // 中等能量
				}
				else if (energy_ratio > 0.2) {
					fitness_score += 0.1; // 低能量但可用
				}
				else {
					return 0.0; // 能量过低，不适合
				}
			}
			else {
				// 无法获取能量信息，给予中等分数
				fitness_score += 0.15;
			}

			// 3. 距离适合度 (权重: 40%)
			auto task_position_wgs84 = getTaskPosition(task);
			if (task_position_wgs84.has_value()) {
				// 转换为ECEF坐标进行内部几何计算
				EcefPoint task_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(task_position_wgs84.value());
				EcefPoint uav_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(uav_state.position);
				Vector3D diff = task_ecef - uav_ecef;
				double distance = diff.norm();

				// 距离越近，适合度越高
				double max_reasonable_distance = 10000.0; // 10km
				double distance_score = std::max(0.0, 1.0 - (distance / max_reasonable_distance));
				fitness_score += 0.4 * distance_score;
			}
			else {
				// 无法获取坐标转换，给予中等分数
				fitness_score += 0.2;
			}

			// 4. 载荷适合度 (权重: 20%)
			// 检查无人机是否具备执行任务所需的载荷能力
			auto payload_requirement = getTaskPayloadRequirement(task);
			if (payload_requirement.has_value()) {
				// TODO: 实现载荷匹配逻辑
				// 暂时给予中等分数
				fitness_score += 0.1;
			}
			else {
				// 无载荷要求，满分
				fitness_score += 0.2;
			}

			// 5. 任务类型适合度 (权重: 10%)
			// 某些无人机可能更适合特定类型的任务
			switch (task.getType()) {
			case TaskType::FOLLOW_PATH:
			case TaskType::LOITER_POINT:
				fitness_score += 0.1; // 基础任务，所有无人机都适合
				break;
			default:
				fitness_score += 0.05; // 其他任务类型给予较低分数
				break;
			}

			LOG_TRACE("无人机 {} 执行任务 {} 的适合度计算: 适合度分数={:.3f}",
				uav->getId(), task.getId(), fitness_score);

			return fitness_score;
		}

	} // namespace NSPlanning
} // namespace NSDrones