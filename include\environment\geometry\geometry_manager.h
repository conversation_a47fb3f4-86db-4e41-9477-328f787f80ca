// include/environment/geometry/geometry_manager.h
#pragma once

#include "core/types.h"
#include <memory>
#include <vector>
#include <optional>
#include <functional>
#include <fcl/fcl.h>

// GeographicLib前向声明
namespace GeographicLib {
	class LocalCartesian;
}

namespace NSDrones {
	namespace NSEnvironment {
		class CoordinateManager;
	}
}

namespace NSDrones {
	namespace NSEnvironment {
		using namespace NSDrones::NSCore;

		/**
		 * @class GeometryManager
		 * @brief 统一几何运算管理器 - 环境模块中所有几何计算的核心组件
		 *
		 * 功能分类：
		 * 1. **基础几何计算**：距离、方位角、目标点计算
		 * 2. **多边形几何**：面积、质心、包含关系、相交检测
		 * 3. **路径几何**：路径长度、点到线距离、大圆路径
		 * 4. **空间查询**：半径查询、边界框计算
		 * 5. **高级几何**：三角剖分、网格生成、多边形简化
		 * 6. **碰撞检测几何**：ECEF坐标系下的几何检测（静态方法）
		 */
		class GeometryManager {
		public:
			// === 静态工具类 - 禁止实例化 ===
			GeometryManager() = delete;
			~GeometryManager() = delete;
			GeometryManager(const GeometryManager&) = delete;
			GeometryManager& operator=(const GeometryManager&) = delete;

			// === 1. 基础几何计算 ===

			/**
			 * @brief 计算两个WGS84点之间的精确距离
			 * @param p1 第一个WGS84点
			 * @param p2 第二个WGS84点
			 * @return 距离(米)，使用GeographicLib保证高精度
			 * @throws std::runtime_error 计算失败时抛出异常
			 */
			static double calculateDistance(const WGS84Point& p1, const WGS84Point& p2);

			/**
			 * @brief 计算从起点到终点的方位角
			 * @param from 起始点（WGS84）
			 * @param to 目标点（WGS84）
			 * @return 方位角（度，0-360度范围）
			 * @throws std::runtime_error 计算失败时抛出异常
			 */
			static double calculateBearing(const WGS84Point& from, const WGS84Point& to);

			/**
			 * @brief 根据起始点、方位角和距离计算目标点
			 * @param start 起始点（WGS84）
			 * @param bearing_deg 方位角（度）
			 * @param distance_m 距离（米）
			 * @return 目标点（WGS84）
			 * @throws std::runtime_error 计算失败时抛出异常
			 */
			static WGS84Point calculateDestination(const WGS84Point& start, double bearing_deg, double distance_m);

			/**
			 * @brief 批量计算距离（性能优化版本）
			 * @param points1 第一组点（WGS84）
			 * @param points2 第二组点（WGS84）
			 * @return 对应的距离列表
			 * @throws std::invalid_argument 如果两个点集大小不匹配
			 */
			static std::vector<double> batchCalculateDistance(
				const std::vector<WGS84Point>& points1,
				const std::vector<WGS84Point>& points2);

			// === 2. 多边形几何 ===

			/**
			 * @brief 计算多边形面积
			 * @param polygon WGS84坐标的多边形顶点
			 * @return 面积(平方米)，使用GeographicLib保证高精度
			 * @throws std::invalid_argument 如果多边形顶点少于3个
			 */
			static double calculatePolygonArea(const std::vector<WGS84Point>& polygon);

			/**
			 * @brief 计算多边形质心
			 * @param polygon WGS84坐标的多边形顶点
			 * @return 质心位置（WGS84）
			 * @throws std::invalid_argument 如果多边形为空
			 */
			static WGS84Point calculatePolygonCentroid(const std::vector<WGS84Point>& polygon);

			/**
			 * @brief 判断点是否在多边形内
			 * @param point 待判断的点（WGS84）
			 * @param polygon 多边形顶点（WGS84）
			 * @return 如果点在多边形内返回true
			 */
			static bool isPointInPolygon(const WGS84Point& point, const std::vector<WGS84Point>& polygon);

			/**
			 * @brief 检查两个多边形是否相交
			 * @param poly1 第一个多边形（WGS84）
			 * @param poly2 第二个多边形（WGS84）
			 * @return 如果相交返回true
			 */
			static bool doPolygonsIntersect(const std::vector<WGS84Point>& poly1,
				const std::vector<WGS84Point>& poly2);

			/**
			 * @brief 简化多边形（Douglas-Peucker算法）
			 * @param polygon 原始多边形（WGS84）
			 * @param tolerance_m 简化容差（米）
			 * @return 简化后的多边形（WGS84）
			 */
			static std::vector<WGS84Point> simplifyPolygon(const std::vector<WGS84Point>& polygon,
				double tolerance_m);

			// === 3. 路径几何 ===

			/**
			 * @brief 计算路径总长度
			 * @param waypoints 路径点列表（WGS84）
			 * @return 路径总长度(米)，使用GeographicLib保证高精度
			 * @throws std::invalid_argument 如果路径点少于2个
			 */
			static double calculatePathLength(const std::vector<WGS84Point>& waypoints);

			/**
			 * @brief 计算点到线段的最短距离
			 * @param point 点（WGS84）
			 * @param line_start 线段起点（WGS84）
			 * @param line_end 线段终点（WGS84）
			 * @return 最短距离(米)
			 */
			static double calculatePointToLineDistance(
				const WGS84Point& point,
				const WGS84Point& line_start,
				const WGS84Point& line_end);

			/**
			 * @brief 生成两点间的大圆路径
			 * @param start 起始点（WGS84）
			 * @param end 终点（WGS84）
			 * @param max_segment_length_m 最大分段长度（米）
			 * @return 路径点列表（WGS84）
			 * @throws std::runtime_error 计算失败时抛出异常
			 */
			static std::vector<WGS84Point> generateGreatCirclePath(const WGS84Point& start,
				const WGS84Point& end,
				double max_segment_length_m = 1000.0);

			/**
			 * @brief 计算两个向量之间的夹角
			 * @param v1_start 向量1起点（WGS84）
			 * @param v1_end 向量1终点（WGS84）
			 * @param v2_start 向量2起点（WGS84）
			 * @param v2_end 向量2终点（WGS84）
			 * @return 夹角（弧度）
			 */
			static double calculateAngleBetweenVectors(const WGS84Point& v1_start, const WGS84Point& v1_end,
				const WGS84Point& v2_start, const WGS84Point& v2_end);

			/**
			 * @brief 检查点集是否共面
			 * @param points 待检查的点集（WGS84）
			 * @param max_deviation 输出参数：最大偏差（米）
			 * @param tolerance_m 共面容差（米），默认1.0米
			 * @return 如果点集共面返回true
			 */
			static bool checkPointsCoplanar(const std::vector<WGS84Point>& points,
				double& max_deviation, double tolerance_m = 1.0);

			/**
			 * @brief 生成区域扫描路径
			 * @param area_boundary 区域边界点（WGS84）
			 * @param strip_width 条带宽度（米）
			 * @param overlap_ratio 重叠率（0-1）
			 * @param scan_angle_deg 扫描角度（度）
			 * @param height_above_plane 离面高度（米）
			 * @return 扫描路径点列表（WGS84）
			 */
			static std::vector<WGS84Point> generateScanPath(
				const std::vector<WGS84Point>& area_boundary,
				double strip_width,
				double overlap_ratio,
				double scan_angle_deg,
				double height_above_plane = 0.0);

			/**
			 * @brief 生成圆柱扫描路径
			 * @param center_bottom 圆柱底部中心点（WGS84）
			 * @param radius 圆柱半径（米）
			 * @param height 圆柱高度（米）
			 * @param line_spacing 垂直线间距（米）
			 * @param points_per_circle 每圈点数
			 * @param clockwise 是否顺时针
			 * @return 扫描路径点列表（WGS84）
			 */
			static std::vector<WGS84Point> generateCylinderScanPath(
				const WGS84Point& center_bottom,
				double radius,
				double height,
				double line_spacing,
				int points_per_circle = 36,
				bool clockwise = true);

			// === 4. 空间查询 ===

			/**
			 * @brief 在指定半径内查找点
			 * @param center 中心点（WGS84）
			 * @param radius_m 半径(米)
			 * @param candidates 候选点列表（WGS84）
			 * @return 在半径内的点的索引列表
			 */
			static std::vector<size_t> findPointsInRadius(
				const WGS84Point& center,
				double radius_m,
				const std::vector<WGS84Point>& candidates);

			/**
			 * @brief 计算点集的边界框
			 * @param points 点集（WGS84）
			 * @return 边界框（WGS84）
			 * @throws std::invalid_argument 如果点集为空
			 */
			static WGS84BoundingBox calculateBoundingBox(const std::vector<WGS84Point>& points);

			/**
			 * @brief 判断两个WGS84边界框是否相交
			 * @param box1 第一个边界框
			 * @param box2 第二个边界框
			 * @return 如果相交返回true
			 */
			static bool doBoundingBoxesIntersect(const WGS84BoundingBox& box1, const WGS84BoundingBox& box2);

			// === 5. 高级几何计算 ===

			/**
			 * @brief 三角剖分多边形
			 * @param polygon 多边形顶点（WGS84）
			 * @param holes 孔洞列表（可选）
			 * @return 三角形索引列表
			 * @note 内部转换为局部坐标系进行剖分
			 */
			std::vector<TriangleIndices> triangulatePolygon(
				const std::vector<WGS84Point>& polygon,
				const std::vector<std::vector<WGS84Point>>& holes = {});

			/**
			 * @brief 生成网格点
			 * @param bounds 边界框（WGS84）
			 * @param spacing_m 网格间距（米）
			 * @return 网格点列表（WGS84）
			 * @throws std::invalid_argument 如果spacing_m <= 0
			 */
			static std::vector<WGS84Point> generateGridPoints(const WGS84BoundingBox& bounds,
				double spacing_m);

			// === 6. 碰撞检测几何（ECEF坐标系静态方法） ===

			/**
			 * @brief 检查ECEF点是否在ECEF多边形内部（投影到XY平面进行2D检测）
			 * @param ecef_point 待判断的ECEF点
			 * @param ecef_polygon ECEF多边形顶点
			 * @param tolerance 容差
			 * @return 如果点在多边形内返回true
			 * @note 静态方法，用于碰撞检测系统
			 */
			static bool isPointInPolygonECEF(const EcefPoint& ecef_point,
				const std::vector<EcefPoint>& ecef_polygon,
				double tolerance = Constants::GEOMETRY_EPSILON);

			/**
			 * @brief 检查两条线段是否相交（ECEF坐标版本）
			 * @param ecef_p1 第一条线段的起点
			 * @param ecef_p2 第一条线段的终点
			 * @param ecef_p3 第二条线段的起点
			 * @param ecef_p4 第二条线段的终点
			 * @param include_endpoints 是否包含端点
			 * @param tolerance 容差
			 * @return 如果线段相交则返回true
			 * @note 静态方法，用于碰撞检测系统
			 */
			static bool isSegmentsIntersect2D(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2,
				const EcefPoint& ecef_p3, const EcefPoint& ecef_p4,
				bool include_endpoints = true,
				double tolerance = Constants::GEOMETRY_EPSILON);

			/**
			 * @brief 检查ECEF点是否在球体内
			 * @param ecef_point 待检查的ECEF点
			 * @param sphere_center 球体中心（ECEF坐标）
			 * @param radius 球体半径
			 * @param tolerance 容差
			 * @return 如果点在球体内返回true
			 * @note 静态方法，用于碰撞检测系统
			 */
			static bool isPointInSphere(const EcefPoint& ecef_point, const EcefPoint& sphere_center,
				double radius, double tolerance = Constants::GEOMETRY_EPSILON);

			/**
			 * @brief 检查ECEF点是否在盒子内
			 * @param ecef_point 待检查的ECEF点
			 * @param box_center 盒子中心（ECEF坐标）
			 * @param box_dimensions 盒子尺寸（长宽高）
			 * @param tolerance 容差
			 * @return 如果点在盒子内返回true
			 * @note 静态方法，用于碰撞检测系统
			 */
			static bool isPointInBox(const EcefPoint& ecef_point, const EcefPoint& box_center,
				const Vector3D& box_dimensions, double tolerance = Constants::GEOMETRY_EPSILON);

			/**
			 * @brief 检查ECEF线段是否与盒子相交
			 * @param line_start 线段起点（ECEF坐标）
			 * @param line_end 线段终点（ECEF坐标）
			 * @param box_center 盒子中心（ECEF坐标）
			 * @param box_dimensions 盒子尺寸（长宽高）
			 * @param tolerance 容差
			 * @return 如果线段与盒子相交返回true
			 * @note 静态方法，用于碰撞检测系统
			 */
			static bool isLineIntersectBox(const EcefPoint& line_start, const EcefPoint& line_end,
				const EcefPoint& box_center, const Vector3D& box_dimensions,
				double tolerance = Constants::GEOMETRY_EPSILON);

			/**
			 * @brief 对ECEF多边形进行三角剖分
			 * @param ecef_polygon ECEF多边形顶点
			 * @return 三角形索引列表
			 * @note 静态方法，用于碰撞检测系统
			 */
			static std::vector<TriangleIndices> triangulatePolygon2D(const std::vector<EcefPoint>& ecef_polygon);

		private:
			// === 私有公共方法（坐标转换和局部坐标系建立） ===

			/**
			 * @brief 建立局部坐标系并转换WGS84点集到ECEF
			 * @param points WGS84点集
			 * @param local_cart 输出的局部坐标系对象
			 * @return 转换后的ECEF点集
			 */
			static std::vector<EcefPoint> setupLocalCoordinateSystem(
				const std::vector<WGS84Point>& points,
				GeographicLib::LocalCartesian& local_cart);

			/**
			 * @brief 将单个WGS84点转换到已建立的局部坐标系
			 * @param point WGS84点
			 * @param local_cart 局部坐标系对象
			 * @return 转换后的ECEF点
			 */
			static EcefPoint convertToLocalECEF(const WGS84Point& point,
				const GeographicLib::LocalCartesian& local_cart);

			/**
			 * @brief 将ECEF点转换回WGS84坐标
			 * @param ecef_point ECEF点
			 * @param local_cart 局部坐标系对象
			 * @return WGS84点
			 */
			static WGS84Point convertFromLocalECEF(const EcefPoint& ecef_point,
				const GeographicLib::LocalCartesian& local_cart);

			// === 私有几何计算方法（ECEF坐标系） ===

			/**
			 * @brief 在ECEF坐标系中计算多边形面积（投影到XY平面）
			 * @param polygon_ecef ECEF坐标的多边形顶点
			 * @return 面积（平方米）
			 */
			static double calculatePolygonAreaECEF(const std::vector<EcefPoint>& polygon_ecef);

			/**
			 * @brief 在ECEF坐标系中计算点到线段距离
			 * @param point_ecef ECEF坐标的点
			 * @param line_start_ecef ECEF坐标的线段起点
			 * @param line_end_ecef ECEF坐标的线段终点
			 * @return 距离（米）
			 */
			static double calculatePointToLineDistanceECEF(
				const EcefPoint& point_ecef,
				const EcefPoint& line_start_ecef,
				const EcefPoint& line_end_ecef);

			/**
			 * @brief 在ECEF坐标系中计算多边形质心
			 * @param polygon_ecef ECEF坐标的多边形顶点
			 * @return 质心（ECEF坐标）
			 */
			static EcefPoint calculatePolygonCentroidECEF(const std::vector<EcefPoint>& polygon_ecef);

			// === 私有算法实现方法 ===

			/**
			 * @brief Douglas-Peucker算法递归实现
			 * @param points 点集
			 * @param start 起始索引
			 * @param end 结束索引
			 * @param tolerance_m 容差
			 * @param keep_flags 保留标记数组
			 */
			static void douglasPeuckerRecursive(const std::vector<WGS84Point>& points,
				int start, int end, double tolerance_m, std::vector<bool>& keep_flags);

			// === 边界框计算方法 ===

			/**
			 * @brief 计算WGS84查询边界框
			 * @param center_position 中心位置（WGS84坐标）
			 * @param shape_aabb 形状轴对齐边界框
			 * @param safety_margin 安全边距（米）
			 * @return WGS84查询边界框
			 */
			static WGS84BoundingBox calculateQueryBounds(
				const WGS84Point& center_position,
				const fcl::AABBd& shape_aabb,
				double safety_margin
			);

			// === 插值计算方法 ===

			/**
			 * @brief ECEF坐标系中的点插值
			 * @param start 起始点（ECEF坐标）
			 * @param end 结束点（ECEF坐标）
			 * @param t 插值参数（0-1）
			 * @return 插值结果点（ECEF坐标）
			 */
			static EcefPoint interpolateECEFPoints(
				const EcefPoint& start,
				const EcefPoint& end,
				double t
			);

			/**
			 * @brief WGS84坐标系中的点插值
			 * @param start 起始点（WGS84坐标）
			 * @param end 结束点（WGS84坐标）
			 * @param t 插值参数（0-1）
			 * @return 插值结果点（WGS84坐标）
			 */
			static WGS84Point interpolateWGS84Points(
				const WGS84Point& start,
				const WGS84Point& end,
				double t
			);

			// === FCL变换矩阵方法 ===

			/**
			 * @brief 创建FCL变换矩阵
			 * @param ecef_position ECEF坐标位置
			 * @param orientation 几何体朝向（四元数）
			 * @return FCL变换矩阵
			 */
			static fcl::Transform3d createFCLTransform(
				const EcefPoint& ecef_position,
				const Orientation& orientation
			);
		};
	} // namespace NSEnvironment
} // namespace NSDrones

