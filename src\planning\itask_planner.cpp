// src/planning/itask_planner.cpp
#include "planning/itask_planner.h"
#include "environment/entities/zone.h"
#include "environment/environment.h"
#include "environment/collision/collision_engine.h"
#include "mission/mission.h"
#include "mission/task.h"
#include "mission/control_point.h"
#include "mission/task_strategies.h"
#include "planning/planning_types.h"
#include "planning/planning_result.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "uav/uav.h"
#include "uav/idynamic_model.h"
#include "utils/logging.h"
#include "utils/geometry_manager.h"
#include "utils/coordinate_converter.h"
#include "utils/object_id.h"
#include "core/types.h"
#include <utility>
#include <cmath>
#include <stdexcept>
#include <map>
#include <vector>
#include <optional>
#include <algorithm>
#include <unordered_map>

namespace NSDrones {
	namespace NSPlanning {

		// === 构造函数实现 ===

		ITaskPlanner::ITaskPlanner() {
			LOG_DEBUG("任务规划器: 基类初始化完成");
		}

		// === 环境和算法组件访问方法实现 ===

		std::shared_ptr<NSEnvironment::Environment> ITaskPlanner::getEnvironment() const {
			return NSEnvironment::Environment::getInstance();
		}

		IPathPlannerPtr ITaskPlanner::getPathPlanner() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getPathPlanner();
		}

		ITrajectoryOptimizerPtr ITaskPlanner::getTrajectoryOptimizer() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryOptimizer();
		}

		ITrajectoryEvaluatorPtr ITaskPlanner::getTrajectoryEvaluator() const {
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return nullptr;
			}
			return environment->getTrajectoryEvaluator();
		}

		// === 辅助方法实现 ===

		std::pair<NSCore::EcefPoint, bool> ITaskPlanner::getAbsolutePosition(
			const NSMission::ControlPoint& cp,
			const NSMission::Task& task) const
		{
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("任务规划器: 无法获取环境实例");
				return { NSCore::EcefPoint(0, 0, 0), false };
			}

			// 获取控制点的WGS84位置
			NSCore::WGS84Point wgs84_position = cp.position;

			// 转换为ECEF坐标进行内部计算
			NSCore::EcefPoint absolute_position = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_position);

			// 根据任务高度策略调整高度
			NSMission::AltitudeType height_strategy = task.getDesiredHeightType();

			auto adjusted_height = environment->getAdjustedHeightAtPoint(
				wgs84_position, height_strategy, wgs84_position.altitude);

			if (adjusted_height.has_value()) {
				absolute_position.setZ(*adjusted_height);
				LOG_DEBUG("任务规划器: 控制点绝对位置计算成功 ({:.1f}, {:.1f}, {:.1f})",
					absolute_position.x(), absolute_position.y(), absolute_position.z());
			}
			else {
				// 高度调整失败的回退策略
				if (height_strategy == NSMission::AltitudeType::ABOVE_GROUND_LEVEL) {
					LOG_WARN("任务规划器: AGL高度策略失败，使用绝对高度作为回退");
				}
				else {
					LOG_WARN("任务规划器: 高度策略{}失败，使用原始高度",
						NSUtils::enumToString(height_strategy));
				}
				absolute_position.setZ(wgs84_position.altitude);
			}

			return { absolute_position, true };
		}

		PlannedRoute ITaskPlanner::generateLinearSegment(const RoutePoint& start_point,
			const NSCore::WGS84Point& end_wgs84_pos,
			double speed,
			const NSUav::IDynamicModel& dynamics,
			const NSUtils::ObjectID& uav_id) const
		{
			PlannedRoute segment(uav_id);

			// 速度验证
			if (speed <= NSCore::Constants::VELOCITY_EPSILON) {
				LOG_WARN("任务规划器: 生成线性航段失败，速度({:.3f})无效", speed);
				return segment;
			}

			segment.addWaypoint(start_point);

			// 使用GeometryManager计算距离
			double distance = NSEnvironment::GeometryManager::calculateDistance(
				start_point.position, end_wgs84_pos);

			if (distance < NSCore::Constants::GEOMETRY_EPSILON) {
				LOG_WARN("任务规划器: 起点和终点距离过近({:.3e}m)，只包含起点", distance);
				return segment;
			}

			// 计算飞行时间和方向
			double duration = distance / speed;
			double bearing_deg = NSEnvironment::GeometryManager::calculateBearing(
				start_point.position, end_wgs84_pos);
			double bearing_rad = bearing_deg * NSCore::Constants::DEG_TO_RAD;

			// 创建终点航路点
			RoutePoint end_waypoint;
			end_waypoint.position = end_wgs84_pos;
			end_waypoint.time_stamp = start_point.time_stamp + duration;
			end_waypoint.velocity = NSCore::Vector3D(
				std::cos(bearing_rad) * speed,
				std::sin(bearing_rad) * speed,
				0.0
			);

			// 设置姿态
			if (end_waypoint.velocity.squaredNorm() > NSCore::Constants::VELOCITY_EPSILON * NSCore::Constants::VELOCITY_EPSILON) {
				end_waypoint.orientation = NSCore::Orientation::FromTwoVectors(
					NSCore::Vector3D::UnitX(), end_waypoint.velocity.normalized());
			}
			else {
				end_waypoint.orientation = start_point.orientation;
			}

			segment.addWaypoint(end_waypoint);

			LOG_DEBUG("任务规划器: 成功生成线性航段，无人机{}，{}个航点，耗时{:.2f}s",
				uav_id, segment.getWaypoints().size(), segment.getTotalTime());

			return segment;
		}


		void ITaskPlanner::checkSegmentWarnings(const PlannedRoute& segment,
			const NSUtils::ObjectID& uav_id,
			PlanningResult& result,
			const NSUtils::ObjectID& task_id) const
		{
			auto environment = getEnvironment();
			if (!environment || segment.getWaypoints().size() < 2) {
				return;
			}

			const auto& zones = environment->getAllZones();
			if (zones.empty()) {
				return;
			}

			const auto& waypoints = segment.getWaypoints();
			for (size_t i = 0; i < waypoints.size() - 1; ++i) {
				const auto& p1 = waypoints[i].position;
				const auto& p2 = waypoints[i + 1].position;
				const auto t1 = waypoints[i].time_stamp;
				const auto t2 = waypoints[i + 1].time_stamp;

				// 转换为ECEF坐标进行区域检查
				NSCore::EcefPoint p1_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(p1);
				NSCore::EcefPoint p2_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(p2);

				std::vector<NSEnvironment::ConstZonePtr> zones_at_p1 = environment->getViolatedZones(p1_ecef);
				std::vector<NSEnvironment::ConstZonePtr> zones_at_p2 = environment->getViolatedZones(p2_ecef);
				std::vector<NSEnvironment::ConstZonePtr> zones_intersecting = environment->getIntersectingZones(p1_ecef, p2_ecef);

				// 收集涉及的区域
				std::unordered_map<NSUtils::ObjectID, NSEnvironment::ConstZonePtr> involved_zones;
				for (const auto& z_ptr : zones_at_p1) {
					if (z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
				}
				for (const auto& z_ptr : zones_at_p2) {
					if (z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
				}
				for (const auto& z_ptr : zones_intersecting) {
					if (z_ptr) involved_zones[z_ptr->getId()] = z_ptr;
				}

				// 检查每个涉及的区域
				for (const auto& pair : involved_zones) {
					const auto& zone = pair.second;
					if (!zone) continue;

					// 检查起点和终点是否在区域内
					bool was_inside = std::find_if(zones_at_p1.begin(), zones_at_p1.end(),
						[&](const NSEnvironment::ConstZonePtr& z_ptr) {
							return z_ptr && z_ptr->getId() == zone->getId();
						}) != zones_at_p1.end();

						bool is_inside = std::find_if(zones_at_p2.begin(), zones_at_p2.end(),
							[&](const NSEnvironment::ConstZonePtr& z_ptr) {
								return z_ptr && z_ptr->getId() == zone->getId();
							}) != zones_at_p2.end();

							// 生成告警事件
							WarningType wtype = WarningType::UNKNOWN;
							std::string description;
							NSCore::WGS84Point location = p1;
							NSCore::Time time = t1;
							bool generate_warning = false;

							NSCore::ZoneType zone_type = zone->getType();

							// 根据区域类型和进入/离开状态生成告警
							switch (zone_type) {
							case NSCore::ZoneType::ENTER_WARNING:
							case NSCore::ZoneType::LEAVE_WARNING:
								if (!was_inside && is_inside) {
									wtype = WarningType::ENTERED_WARNING_ZONE;
									description = "进入告警区域 " + NSUtils::toString(zone->getId());
									location = p2; time = t2; generate_warning = true;
								}
								else if (was_inside && !is_inside) {
									wtype = WarningType::LEFT_WARNING_ZONE;
									description = "离开告警区域 " + NSUtils::toString(zone->getId());
									location = p2; time = t2; generate_warning = true;
								}
								break;

							case NSCore::ZoneType::KEEPOUT:
								if (!was_inside && is_inside) {
									wtype = WarningType::ENTERED_KEEPOUT_ZONE;
									description = "进入禁飞区域 " + NSUtils::toString(zone->getId());
									location = p2; time = t2; generate_warning = true;
									LOG_WARN("任务规划器: 无人机{}进入禁飞区域{}",
										NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
								}
								else if (zone->intersects(p1_ecef, p2_ecef)) {
									wtype = WarningType::CROSS_KEEPOUT_BOUNDARY;
									description = "穿越禁飞区域 " + NSUtils::toString(zone->getId()) + " 边界";
									generate_warning = true;
									LOG_WARN("任务规划器: 无人机{}穿越禁飞区域{}边界",
										NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
								}
								break;

							case NSCore::ZoneType::THREAT:
								if (!was_inside && is_inside) {
									wtype = WarningType::ENTERED_THREAT_ZONE;
									description = "进入威胁区域 " + NSUtils::toString(zone->getId());
									location = p2; time = t2; generate_warning = true;
									LOG_WARN("任务规划器: 无人机{}进入威胁区域{}",
										NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
								}
								else if (zone->intersects(p1_ecef, p2_ecef)) {
									wtype = WarningType::CROSS_THREAT_BOUNDARY;
									description = "穿越威胁区域 " + NSUtils::toString(zone->getId()) + " 边界";
									generate_warning = true;
									LOG_WARN("任务规划器: 无人机{}穿越威胁区域{}边界",
										NSUtils::toString(uav_id), NSUtils::toString(zone->getId()));
								}
								break;

							default:
								break;
							}

							if (generate_warning && wtype != WarningType::UNKNOWN) {
								result.addWarning(WarningEvent(wtype, description, time, location, uav_id, zone->getId(), task_id));
							}
				}
			}
		}

		/**
		 * @brief 平滑几何路径并进行时间参数化。
		 * @param geometric_path 输入几何路径。
		 * @param uav 无人机指针。
		 * @param start_state 起始状态。
		 * @param desired_speed 期望速度。
		 * @param optimized_segment 输出优化后的轨迹段。
		 * @param result_ptr (可选) 用于添加告警。
		 * @param strategies (可选) 应用于此轨迹段的策略。
		 * @return 如果成功生成有效轨迹返回 true。
		 */
		bool ITaskPlanner::smoothAndTimeParameterize(const std::vector<WGS84Point>& geometric_path,
			const NSUav::UavPtr& uav,
			const NSUav::UavState& start_state,
			double desired_speed,
			RouteSegment& optimized_segment,
			PlanningResult* result_ptr,
			const NSMission::ITaskStrategyMap& strategies) // 添加 strategies 参数
		{
			if (!uav) {
				LOG_ERROR("轨迹平滑和时间参数化失败：UAV 指针无效。");
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "内部错误：UAV 指针无效", start_state.time_stamp)); // 使用构造函数，移除 ID
				return false;
			}
			if (geometric_path.size() < 2) {
				LOG_WARN("轨迹平滑和时间参数化：几何路径点数 ({}) 不足，无法处理。", geometric_path.size());
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_ERROR, "几何路径点数不足", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
				return false;
			}
			// 修正: 使用 VELOCITY_EPSILON
			if (desired_speed <= Constants::VELOCITY_EPSILON) {
				LOG_ERROR("轨迹平滑和时间参数化失败：期望速度 ({:.3f}) 无效。", desired_speed);
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_ERROR, "期望速度无效", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
				return false;
			}

			LOG_DEBUG("开始为 UAV {} 平滑和时间参数化包含 {} 个点的几何路径，期望速度 {:.2f} m/s", uav->getId(), geometric_path.size(), desired_speed);

			// 1. 匀速时间参数化 (基础)
			RouteSegment initial_segment; // 这是 std::vector<RoutePoint>
			// 移除对 id, is_valid, start_time 的访问

			// 修正: 使用 getDynamicsModel() 并检查返回值
			const auto dynamic_model = uav->getDynamicsModel(); // 返回 ConstIDynamicModelPtr
			if (!dynamic_model) {
				LOG_ERROR("无法为 UAV {} 获取动力学模型，无法进行时间参数化。", uav->getId());
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "无法获取动力学模型", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
				return false;
			}

			// 修正: 调用 timeParameterizeConstantSpeed，传递 dynamic_model.get()
			if (!timeParameterizeConstantSpeed(geometric_path, dynamic_model.get(), start_state.time_stamp, start_state.velocity, desired_speed, initial_segment)) {
				LOG_ERROR("UAV {} 匀速时间参数化失败。", uav->getId());
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_ERROR, "匀速时间参数化失败", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
				return false;
			}

			// 移除对 id, getDuration 的访问
			if (initial_segment.empty()) {
				LOG_ERROR("UAV {} 匀速时间参数化结果为空。", uav->getId());
				return false;
			}
			else {
				LOG_DEBUG("UAV {} 匀速时间参数化完成，生成 {} 个航点，耗时 {:.2f}s",
					uav->getId(), initial_segment.size(), initial_segment.back().time_stamp - initial_segment.front().time_stamp);
			}

			optimized_segment = initial_segment; // 默认使用匀速结果

			// 2. 轨迹优化 (如果配置了优化器)
			auto trajectory_optimizer = getTrajectoryOptimizer();
			if (trajectory_optimizer) {
				LOG_DEBUG("检测到轨迹优化器，尝试优化包含 {} 个点的航段...", initial_segment.size());
				// 移除 TrajectoryOptimizerInput 相关代码
				RouteSegment optimized_result_segment;
				PlanningResult optimization_warnings; // 存储优化过程中的告警

				// 检查环境实例是否有效
				auto environment = getEnvironment();
				if (!environment) {
					LOG_ERROR("优化失败：环境实例不存在。");
					if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "内部错误：环境实例不存在", start_state.time_stamp, {}, uav->getId()));
					// 继续使用未优化的结果
				}
				else {
					// 调用轨迹优化器
					optimized_result_segment = trajectory_optimizer->optimizer(
						initial_segment,      // 初始航段 (const ref)
						*dynamic_model,       // 动力学模型 (const ref)
						strategies          // 策略 (const ref)
					);

					// 检查优化结果是否有效（例如，点数 > 1）
					if (optimized_result_segment.size() >= 2) {
						LOG_INFO("UAV {} 的轨迹优化成功，使用优化后的航段 ({} 个点)，耗时 {:.2f}s",
							uav->getId(), optimized_result_segment.size(),
							optimized_result_segment.back().time_stamp - optimized_result_segment.front().time_stamp);
						optimized_segment = optimized_result_segment; // 使用优化结果
					}
					else {
						LOG_WARN("UAV {} 的轨迹优化结果无效 (点数 {} < 2)，将使用原始匀速航段。",
							uav->getId(), optimized_result_segment.size());
						// 添加优化失败的告警到主结果中
						if (result_ptr) {
							// 可以根据需要从轨迹优化器获取更详细的失败原因
							result_ptr->addWarning(WarningEvent(WarningType::OPTIMIZATION_FAILED, "轨迹优化结果无效", start_state.time_stamp, {}, uav->getId())); // 使用构造函数，移除 ID
						}
					}
				}
			}
			else {
				LOG_DEBUG("未配置轨迹优化器，跳过优化步骤。");
			}

			// 3. 基础轨迹评估（TaskPlanner层面）
			auto trajectory_evaluator = getTrajectoryEvaluator();
			if (trajectory_evaluator && !optimized_segment.empty()) {
				LOG_DEBUG("任务规划器: 对UAV{}的轨迹进行基础可行性评估", uav->getId());

				// 将RouteSegment转换为Trajectory进行评估
				Trajectory trajectory_to_evaluate;
				TrajectorySegment segment_to_evaluate;
				segment_to_evaluate.states.reserve(optimized_segment.size());

				if (!optimized_segment.empty()) {
					segment_to_evaluate.duration = optimized_segment.back().time_stamp - optimized_segment.front().time_stamp;
					for (const auto& rp : optimized_segment) {
						segment_to_evaluate.states.push_back(NSUav::stateFromRoutePt(rp));
					}
					trajectory_to_evaluate.push_back(segment_to_evaluate);
				}

				// 进行基础可行性评估
				TrajectoryCost eval_result = trajectory_evaluator->evaluate(uav, trajectory_to_evaluate, nullptr);

				if (!eval_result.is_feasible) {
					LOG_WARN("任务规划器: UAV{}的轨迹基础评估失败: {}", uav->getId(), eval_result.message);
					if (result_ptr) {
						NSCore::WGS84Point warn_location = optimized_segment.empty() ?
							NSCore::WGS84Point(NAN, NAN, NAN) : optimized_segment.front().position;
						result_ptr->addWarning(WarningEvent(WarningType::TRAJECTORY_INFEASIBLE,
							"轨迹基础评估失败: " + eval_result.message,
							start_state.time_stamp, warn_location, uav->getId(), NSUtils::INVALID_OBJECT_ID, NSUtils::INVALID_OBJECT_ID));
					}
					// 注意：这里不直接返回false，而是添加警告，让上层决定是否继续
					LOG_WARN("任务规划器: 轨迹评估失败但继续返回结果，由上层决定处理策略");
				} else {
					LOG_DEBUG("任务规划器: UAV{}的轨迹基础评估通过", uav->getId());
				}
			}

			// 4. 最终检查
			if (optimized_segment.empty()) {
				LOG_ERROR("任务规划器: 最终生成的航段为空");
				return false;
			}

			LOG_INFO("任务规划器: 成功为UAV{}平滑和时间参数化路径，生成{}个航点，耗时{:.2f}s",
				uav->getId(), optimized_segment.size(),
				optimized_segment.back().time_stamp - optimized_segment.front().time_stamp);
			return true;
		}

		// isZoneConstraintSatisfied 实现
		bool ITaskPlanner::isZoneConstraintSatisfied(const RouteSegment& segment, const ConstZonePtr& zone) const
		{
			if (!zone) {
				LOG_WARN("检查区域约束时传入了无效的 Zone 指针。");
				return true; // 无效区域无法违反，视为满足
			}
			if (segment.size() < 2) {
				// 修正: 使用 getId()
				LOG_TRACE("检查区域 {} 约束：航段点数不足 ({})，视为满足。", zone->getId(), segment.size());
				return true; // 无效航段不违反约束
			}

			LOG_TRACE("检查包含 {} 个点的航段是否满足区域 {} (ID:{}, 类型:{}) 的约束...",
				segment.size(), zone->getId(), zone->getId(), NSUtils::enumToString(zone->getType()));

			ZoneType zone_type = zone->getType();
			// 移除 getTolerance() 调用，Zone 类没有此方法，暂时硬编码或使用常量
			double tolerance = Constants::GEOMETRY_EPSILON;

			if (zone_type == ZoneType::KEEPOUT || zone_type == ZoneType::THREAT /*RESTRICTED_FLIGHT_ZONE*/) { // 假设类型名称
				LOG_TRACE("  区域类型为禁飞/威胁区，检查航段是否进入或穿越...");
				// 检查航段的任何部分是否在区域内（考虑容差）
				for (size_t i = 0; i < segment.size(); ++i) {
					const auto& pt = segment[i].position; // 直接访问 vector 元素
					EcefPoint pt_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(pt);
					if (zone->isInside(pt_ecef, tolerance)) { // 使用 isInside 检查点
						LOG_WARN("航段的航路点 {} ({}) 位于禁飞/威胁区 {} (容差 {:.1e}m) 内部，约束违反！",
							i, pt.toString(), zone->getId(), tolerance);
						return false;
					}
					if (i < segment.size() - 1) {
						const auto& next_pt = segment[i + 1].position;
						EcefPoint next_pt_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(next_pt);
						if (zone->intersects(pt_ecef, next_pt_ecef, tolerance)) { // 使用 intersects 检查段
							LOG_WARN("航段的子航段 {}->{} 与禁飞/威胁区 {} (容差 {:.1e}m) 相交，约束违反！",
								i, i + 1, zone->getId(), tolerance);
							return false;
						}
					}
				}
				// 修正: 使用 getId()
				LOG_TRACE("  航段未进入或穿越禁飞/威胁区 {}。", zone->getId());
			}
			else if (zone_type == ZoneType::OPERATIONAL/*_AREA*/
				|| zone_type == ZoneType::ENTER_WARNING /*GEOFENCE*/) { // 假设类型名称
				LOG_TRACE("  区域类型为作业区/进入告警区，检查航段是否完全位于内部...");
				// 检查航段的所有点是否都在区域内
				for (size_t i = 0; i < segment.size(); ++i) {
					const auto& pt = segment[i].position;
					EcefPoint pt_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(pt);
					// 对于作业区/围栏，通常用负容差或零容差确保在边界内
					if (!zone->isInside(pt_ecef, -tolerance)) {
						LOG_WARN("航段的航路点 {} ({}) 位于作业区/进入告警区 {} (容差 {:.1e}m) 外部，约束违反！",
							i, pt.toString(), zone->getId(), -tolerance);
						return false;
					}
				}
				LOG_TRACE("  航段完全位于作业区/进入告警区 {} 内部。", zone->getId());
			}
			else {
				LOG_TRACE("  区域类型 {} 无特定约束检查逻辑，视为满足。", NSUtils::enumToString(zone_type));
				// 其他区域类型可能没有航段约束，或者需要特定逻辑
			}

			// 移除对 segment.id 的访问
			LOG_TRACE("航段满足区域 {} 的约束。", zone->getId());
			return true; // 如果没有检测到违反，则满足约束
		}

		bool ITaskPlanner::checkSafetyConstraints(const RoutePoint& point, const ObjectID& uav_id, const ObjectID& task_id, PlanningResult& result, const std::vector<ZoneType>& zone_types_to_check) const
		{
			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("ITaskPlanner checkSafetyConstraints(WGS84Point): Environment not set! Cannot check safety.");
				return false; // Or throw, depending on desired behavior
			}
			if (zone_types_to_check.empty()) {
				return true; // No specific zone types to check against, point is considered safe from this perspective
			}

			const auto& all_zones = environment->getAllZones(); // 修正 all_zones 的迭代问题
			for (const auto& zone_ptr : all_zones) { // 使用 auto&
				if (!zone_ptr) continue;

				ZoneType current_zone_logic_type = zone_ptr->getType();
				// 移除复杂的类型推断，直接使用 getType()
				// if (current_zone_logic_type == ZoneType::UNKNOWN) { ... } // 移除这部分逻辑

				if (current_zone_logic_type != ZoneType::UNKNOWN) { // 只检查已知类型的区域
					bool should_check_this_zone_type = false;
					for (ZoneType target_type_to_check_from_task : zone_types_to_check) {
						if (current_zone_logic_type == target_type_to_check_from_task) {
							should_check_this_zone_type = true;
							break;
						}
					}

					if (should_check_this_zone_type) {
						LOG_TRACE("ITaskPlanner checkSafetyConstraints(WGS84Point): Checking point against zone '{}' (Type: {})...", zone_ptr->getId(), NSUtils::enumToString(current_zone_logic_type));
						EcefPoint point_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(point.position);
						if (zone_ptr->isInside(point_ecef, 0.0)) { // tolerance 0.0 for exact check, GEOMETRY_EPSILON for slight tolerance
							LOG_WARN("ITaskPlanner checkSafetyConstraints(WGS84Point): Point {} is INSIDE zone '{}' (Type: {}). Constraint violated.", point.position.toString(), zone_ptr->getId(), NSUtils::enumToString(current_zone_logic_type));
							return false; // Point is inside a prohibited zone type
						}
					}
				}
			}
			// 根据策略，如果无法获取地面高程，可能也视为不安全

			LOG_INFO("ITaskPlanner：点 ({})@t={:.2f} 安全约束检查通过 (UAV:{}, Task:{})",
				point.position.toString(), point.time_stamp, toString(uav_id), toString(task_id));
			return true;
		}

		// 确保函数签名与 itask_planner.h 中的声明一致
		bool ITaskPlanner::isPathSegmentSafe(const WGS84Point& wgs84_p1, const WGS84Point& wgs84_p2, Time t1, Time t2, const ObjectID& uav_id, const ObjectID& task_id, PlanningResult& result, const std::vector<ZoneType>& zone_types_to_check) const
		{
			LOG_TRACE("ITaskPlanner 检查航段 {}@t={:.2f} -> {}@t={:.2f} 的安全约束 (UAV:{}, Task:{})",
				wgs84_p1.toString(), t1, wgs84_p2.toString(), t2, toString(uav_id), toString(task_id));

			auto environment = getEnvironment();
			if (!environment) {
				LOG_ERROR("  检查航段安全失败: 环境实例不存在。");
				// result.addWarning(WarningType::PLANNING_ERROR, "环境数据不可用，无法检查航段安全。", uav_id, task_id, t1, p1); // 修正 addWarning 调用
				result.addWarning(WarningEvent(WarningType::PLANNING_ERROR, "环境数据不可用，无法检查航段安全。",
					t1, wgs84_p1, uav_id, INVALID_OBJECT_ID, task_id));
				return false;
			}

			// 转换为ECEF坐标进行内部几何计算
			EcefPoint ecef_p1 = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p1);
			EcefPoint ecef_p2 = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p2);

			// 1. 离散化检查：沿航段采样多个点进行检查
			//    更鲁棒的检查应该使用环境的 isSegmentValid 方法，但这里我们先实现一个基础版本
			int num_samples = 5; // 可以根据航段长度和时间动态调整
			Vector3D segment_vec = ecef_p2 - ecef_p1;
			double segment_len = segment_vec.norm();
			if (segment_len < Constants::GEOMETRY_EPSILON) { // 如果是点，直接检查该点
				LOG_TRACE("  航段长度接近零，检查单点 {}@t={:.2f}", wgs84_p1.toString(), t1);
				RoutePoint rp;
				rp.position = wgs84_p1;
				rp.time_stamp = t1;
				// rp.velocity, rp.attitude 等可以不设置，因为 checkSafetyConstraints 主要关注位置和时间
				return checkSafetyConstraints(rp, uav_id, task_id, result, zone_types_to_check);
			}

			for (int i = 0; i <= num_samples; ++i) {
				double ratio = static_cast<double>(i) / num_samples;
				EcefPoint current_ecef = EcefPoint(ecef_p1.toVector3D() + segment_vec * ratio);
				WGS84Point current_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(current_ecef);
				Time current_t = t1 + (t2 - t1) * ratio;
				RoutePoint current_rp;
				current_rp.position = current_wgs84;
				current_rp.time_stamp = current_t;

				if (!checkSafetyConstraints(current_rp, uav_id, task_id, result, zone_types_to_check)) {
					LOG_WARN("  航段在采样点 {} {}@t={:.2f} 处不安全。",
						i, current_wgs84.toString(), current_t);
					// 告警已在 checkSafetyConstraints 中添加
					return false;
				}
			}
			LOG_TRACE("  航段所有采样点均满足安全约束。");

			// 2. (更优) 直接检查整个航段与区域的相交情况
			const auto& all_zones = environment->getAllZones();
			if (all_zones.empty()) {
				LOG_TRACE("  环境中无区域，跳过航段区域相交检查。");
			}
			else {
				LOG_TRACE("  环境中有 {} 个区域，开始检查航段与禁飞区和危险区的相交...", all_zones.size());
				for (const auto& zone_ptr : all_zones) { // zone_ptr 现在是 const ZonePtr&
					if (!zone_ptr) continue;

					if (zone_ptr->getType() == ZoneType::KEEPOUT || zone_ptr->getType() == ZoneType::THREAT) {
						EcefPoint wgs84_p1_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p1);
						EcefPoint wgs84_p2_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_p2);
						if (zone_ptr->intersects(wgs84_p1_ecef, wgs84_p2_ecef)) {
							LOG_WARN("  航段 {}->{} 与 {} 区域 [{}] 相交。",
								wgs84_p1.toString(), wgs84_p2.toString(),
								NSUtils::enumToString(zone_ptr->getType()), zone_ptr->getId());
							WarningType wt = (zone_ptr->getType() == ZoneType::KEEPOUT) ?
								WarningType::ENTERED_KEEPOUT_ZONE : WarningType::ENTERED_THREAT_ZONE;
							std::string msg = "航段与" + NSUtils::enumToString(zone_ptr->getType()) + "区域 [" + zone_ptr->getId() + "] 相交。";
							result.addWarning(WarningEvent(wt, msg, t1, wgs84_p1, uav_id, zone_ptr->getId(), task_id));

							return false; // 一旦发现不安全，立即返回
						}
					}
				}
				LOG_TRACE("  航段未与任何禁飞区或危险区相交。");
			}

			LOG_INFO("ITaskPlanner：航段 {}@t={:.2f} -> {}@t={:.2f} 安全检查通过 (UAV:{}, Task:{})",
				wgs84_p1.toString(), t1, wgs84_p2.toString(), t2, uav_id, task_id);
			return true;
		}

		bool ITaskPlanner::smoothAndTimeParameterizeECEF(const std::vector<EcefPoint>& geometric_path,
			const NSUav::UavPtr& uav,
			const NSUav::UavState& start_state,
			double desired_speed,
			RouteSegment& optimized_segment,
			PlanningResult* result_ptr,
			const NSMission::ITaskStrategyMap& strategies)
		{
			if (!uav) {
				LOG_ERROR("轨迹平滑和时间参数化失败：UAV 指针无效。");
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::CRITICAL, "内部错误：UAV 指针无效", start_state.time_stamp));
				return false;
			}

			if (geometric_path.size() < 2) {
				LOG_ERROR("轨迹平滑和时间参数化失败：几何路径点数不足 (需要至少2个点，实际{}个)。", geometric_path.size());
				if (result_ptr) result_ptr->addWarning(WarningEvent(WarningType::PLANNING_FAILURE, "几何路径点数不足", start_state.time_stamp));
				return false;
			}

			// 将ECEF路径转换为WGS84路径进行处理（RouteSegment使用WGS84坐标）
			std::vector<WGS84Point> wgs84_path;
			wgs84_path.reserve(geometric_path.size());
			for (const auto& ecef_point : geometric_path) {
				wgs84_path.push_back(NSUtils::CoordinateConverter::ecefToWGS84(ecef_point));
			}

			// 简化实现：直接生成基于时间的航路点
			optimized_segment.clear();
			optimized_segment.reserve(wgs84_path.size());

			Time current_time = start_state.time_stamp;
			WGS84Point current_wgs84_pos = start_state.position;

			for (size_t i = 0; i < wgs84_path.size(); ++i) {
				RoutePoint rp;
				rp.position = wgs84_path[i];
				rp.time_stamp = current_time;
				rp.velocity = (i < wgs84_path.size() - 1) ?
					Vector3D(desired_speed, 0, 0) : Vector3D::Zero(); // 简化速度设置
				rp.orientation = start_state.orientation; // 简化姿态设置

				optimized_segment.push_back(rp);

				// 计算到下一个点的时间
				if (i < wgs84_path.size() - 1) {
					// 使用ECEF坐标计算距离（更精确）
					double distance = (geometric_path[i + 1] - geometric_path[i]).norm();
					double time_increment = distance / desired_speed;
					current_time += time_increment;
				}
			}

			LOG_DEBUG("ECEF轨迹平滑和时间参数化完成，生成 {} 个航路点。", optimized_segment.size());
			return true;
		}

	} // namespace NSPlanning
} // namespace NSDrones