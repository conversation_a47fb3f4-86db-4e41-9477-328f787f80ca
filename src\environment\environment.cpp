// src/environment/environment.cpp
#include "environment/environment.h"
#include "core/entity_object.h"
#include "environment/entities/zone.h"
#include "environment/entities/obstacle.h"
#include "environment/storage/object_storage.h"
#include "environment/storage/object_storage.tpp"
#include "environment/maps/tiled_gridmap.h"
#include "environment/indices/attribute_index.h"
#include "environment/indices/bvh_spatial_index.h"
#include "environment/indices/ispatial_index.h"
#include "environment/collision/collision_engine.h"
#include "environment/geometry/geometry_manager.h"
#include "environment/coordinate/coordinate_manager.h"
#include "core/geometry/ishape.h" 
#include "core/geometry/shapes/sphere_shape.h" 
#include "utils/logging.h"
#include "params/parameters.h"
// 算法接口头文件包含
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "algorithm/evaluator/itrajectory_evaluator.h"
#include "algorithm/allocator/itask_allocator.h"
#include <sstream>
#include <iomanip>
#include <unordered_set>
#include <numeric>
#include <algorithm>

namespace NSDrones {
namespace NSEnvironment {

// 使用Environment类中定义的ObjectMap类型
using ObjectMap = std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>;

// 使用NSParams命名空间中的函数
using NSParams::toString;

// === 静态成员定义 ===
std::shared_ptr<Environment> Environment::instance_ = nullptr;
std::mutex Environment::instance_mutex_;

// === 单例模式实现 ===

std::shared_ptr<Environment> Environment::createInstance(NSParams::ParamRegistry& registry) {
	std::lock_guard<std::mutex> lock(instance_mutex_);
	if (instance_) {
		throw std::runtime_error("Environment: 实例已存在，不能重复创建");
	}

	// 使用 new 创建实例，因为构造函数是私有的
	instance_ = std::shared_ptr<Environment>(new Environment(registry));
	LOG_INFO("Environment: 全局实例已创建");
	return instance_;
}

std::shared_ptr<Environment> Environment::getInstance() {
	std::lock_guard<std::mutex> lock(instance_mutex_);
	if (!instance_) {
		throw std::runtime_error("Environment: 全局实例尚未创建，请先调用 createInstance()");
	}
	return instance_;
}

bool Environment::hasInstance() {
	std::lock_guard<std::mutex> lock(instance_mutex_);
	return instance_ != nullptr;
}

void Environment::destroyInstance() {
	std::lock_guard<std::mutex> lock(instance_mutex_);
	if (instance_) {
		LOG_INFO("Environment: 销毁全局实例");
		instance_.reset();
	}
}

long Environment::getInstanceRefCount() {
	std::lock_guard<std::mutex> lock(instance_mutex_);
	return instance_ ? instance_.use_count() : 0;
}

Environment::Environment(NSParams::ParamRegistry& registry)
    : param_registry_(registry)
    , unified_cache_(cache_max_age_, cache_max_size_) {
    LOG_INFO("环境: 创建环境实例，ThreadSafeCache统一缓存系统已初始化");
    LOG_DEBUG("环境: ThreadSafeCache配置 - 最大存活时间: {}分钟, 最大条目数: {}",
             cache_max_age_.count(), cache_max_size_);
}

bool Environment::initialize(const nlohmann::json& config_json) {
    LOG_INFO("环境: 开始初始化环境");

    // 步骤0.5: 配置缓存参数（直接从config_json读取，只配置一次）
    if (!preConfigureCacheSettings(config_json)) {
        LOG_WARN("环境: 缓存参数配置失败，将使用默认值");
    }

    // 步骤1: 使用 params 模块的高级接口加载全局参数
    if (!loadGlobalParameters(config_json)) {
        LOG_ERROR("环境: 全局参数加载失败");
        return false;
    }

    // 步骤2: 初始化坐标系统管理器（必须在地图加载之前）
    LOG_INFO("环境: 初始化坐标系统管理器");
    coordinate_manager_ = std::make_shared<CoordinateManager>();
    if (!coordinate_manager_->initialize(global_params_)) {
        LOG_ERROR("环境: CoordinateManager初始化失败");
        coordinate_manager_.reset();
        return false;
    }
    LOG_INFO("环境: CoordinateManager初始化成功");

    // 步骤2.5: 初始化全局任务空间（使用配置的WGS84原点）
    try {
        WGS84Point wgs84_origin;

        // 获取参数定义中的缺省值
        WGS84Point backup_default(118.0, 32.0, 0.0); // 备用缺省值 (经度, 纬度, 高度)
        // 使用三参数版本的getValueOrDefault，它会自动从ParamRegistry获取参数定义中的缺省值
        WGS84Point default_origin = global_params_->getValueOrDefault<WGS84Point>("wgs84_origin", backup_default, &param_registry_);
        LOG_DEBUG("环境: 获取到wgs84_origin缺省值 {}",
                 default_origin.toString());

        // 直接读取 WGS84Point 类型的 wgs84_origin 参数，若配置有问题则使用参数缺省值
        if (global_params_->hasParam("wgs84_origin")) {
            try {
                // 直接读取 WGS84Point 类型，使用参数定义中的缺省值作为fallback
                wgs84_origin = global_params_->getValueOrDefault<WGS84Point>("wgs84_origin", default_origin);
                LOG_INFO("环境: 使用配置的WGS84原点 {}",
                        wgs84_origin.toString());
            } catch (const std::exception& e) {
                LOG_WARN("环境: 读取 wgs84_origin 参数失败: {}，使用参数定义中的缺省值", e.what());
                wgs84_origin = default_origin;
                LOG_INFO("环境: 使用参数缺省WGS84原点 {}",
                        wgs84_origin.toString());
            }
        } else {
            // 使用参数定义中的缺省值
            wgs84_origin = default_origin;
            LOG_INFO("环境: 未找到 wgs84_origin 参数，使用参数定义中的缺省值 {}",
                    wgs84_origin.toString());
        }

        // 查找全局任务空间（如果不存在，需要通过Config创建）
        std::string space_id = Constants::GLOBAL_TASK_SPACE_ID;
        auto task_space = getOrCreateTaskSpace(wgs84_origin, space_id);
        if (!task_space) {
            LOG_WARN("环境: 未找到全局任务空间，需要通过Config的defines/objects配置创建");
            LOG_INFO("环境: 继续初始化，全局任务空间将在Config加载对象时创建");
        } else {
            LOG_INFO("环境: 找到全局任务空间 '{}'", space_id);
        }
        LOG_INFO("环境: 全局任务空间初始化成功");

        // TaskSpace的创建将由CoordinateManager的initialize方法处理
    } catch (const std::exception& e) {
        LOG_ERROR("环境: 初始化全局任务空间时发生异常: {}", e.what());
        return false;
    }

    // 步骤3: 初始化地图数据源（现在坐标系统已经准备好了）
    LOG_INFO("环境: 初始化地图数据源");
    if (!loadMapData()) {
        LOG_WARN("环境: 地图数据加载失败，环境将在没有地图数据的情况下运行");
    }

    // 步骤4: 初始化空间索引管理器
    LOG_INFO("环境: 初始化空间索引管理器");
    auto bvhSpatialIndex = std::make_shared<BvhSpatialIndex>();

    // 如果地图已加载，将地图边界信息添加到全局参数中
    std::optional<BoundingBox> map_bounds;
    if (mapDataSource_) {
        auto metadata = mapDataSource_->getMetadata();
        if (metadata.isValid() && metadata.world_bounds.volume() > 0) {
            // 使用地图的实际世界坐标边界
            map_bounds = metadata.world_bounds;

            // 将地图边界信息设置到全局参数中
            global_params_->setValueVariant("spatial_index.map_bounds_min_x", map_bounds->min_.x(), param_registry_);
            global_params_->setValueVariant("spatial_index.map_bounds_min_y", map_bounds->min_.y(), param_registry_);
            global_params_->setValueVariant("spatial_index.map_bounds_min_z", map_bounds->min_.z(), param_registry_);
            global_params_->setValueVariant("spatial_index.map_bounds_max_x", map_bounds->max_.x(), param_registry_);
            global_params_->setValueVariant("spatial_index.map_bounds_max_y", map_bounds->max_.y(), param_registry_);
            global_params_->setValueVariant("spatial_index.map_bounds_max_z", map_bounds->max_.z(), param_registry_);

            LOG_INFO("环境: 地图边界信息已设置到全局参数: min=({:.1f}, {:.1f}, {:.1f}), max=({:.1f}, {:.1f}, {:.1f})",
                map_bounds->min_.x(), map_bounds->min_.y(), map_bounds->min_.z(),
                map_bounds->max_.x(), map_bounds->max_.y(), map_bounds->max_.z());
        } else {
            LOG_WARN("环境: 地图元数据无效或世界边界为空，无法设置地图边界信息");
        }
    }

    // 创建空的 JSON 配置，完全基于全局参数初始化
    nlohmann::json empty_config = nlohmann::json::object();

    // 添加调试信息
    if (map_bounds.has_value()) {
        LOG_INFO("环境: 传递给BvhSpatialIndex的地图边界: min=({:.1f}, {:.1f}, {:.1f}), max=({:.1f}, {:.1f}, {:.1f})",
            map_bounds->min_.x(), map_bounds->min_.y(), map_bounds->min_.z(),
            map_bounds->max_.x(), map_bounds->max_.y(), map_bounds->max_.z());
    } else {
        LOG_WARN("环境: 没有有效的地图边界传递给BvhSpatialIndex，将使用默认边界");
    }

    // 准备BvhSpatialIndex的配置
    nlohmann::json bvh_config;
    if (config_json.contains("spatial_index")) {
        bvh_config = config_json["spatial_index"];
    }

    if (bvhSpatialIndex->initialize(bvh_config, *global_params_, map_bounds)) {
        spatial_index_ = std::static_pointer_cast<ISpatialIndex>(bvhSpatialIndex);
        LOG_INFO("环境: BvhSpatialIndex初始化成功");
    } else {
        LOG_ERROR("环境: BvhSpatialIndex初始化失败");
        return false;
    }

    // 步骤4: 初始化碰撞检测系统
    LOG_INFO("环境: 初始化碰撞检测系统");
    if (spatial_index_) {
        collision_engine_ = std::make_shared<CollisionEngine<ObjectMap>>(object_storage_, *spatial_index_, coordinate_manager_);
        if (collision_engine_->initialize(global_params_)) {
            LOG_INFO("环境: CollisionEngine初始化成功，已配置坐标转换器");
        } else {
            LOG_ERROR("环境: CollisionEngine初始化失败");
            collision_engine_.reset();
            return false;
        }
    } else {
        LOG_ERROR("环境: 空间索引未初始化，无法创建碰撞引擎");
        return false;
    }

    // 步骤5: 初始化几何计算管理器（坐标系统管理器已在步骤2初始化）
    LOG_INFO("环境: 初始化几何计算管理器");
    geometry_manager_ = std::make_shared<GeometryManager>();
    LOG_INFO("环境: GeometryManager初始化成功");

    LOG_INFO("环境: 环境初始化完成");
    return true;
}

// --- 私有初始化方法实现 ---



bool Environment::loadGlobalParameters(const nlohmann::json& config_json) {
    LOG_INFO("环境: 开始加载全局参数");

    if (config_json.empty()) {
        LOG_INFO("环境: 未提供全局参数配置，将使用默认值");
        return true;
    }

    try {
        // 使用 params 模块的高级接口加载参数
        // 首先确定 type_tag
        std::string global_type_tag = "global_environment_params"; // 默认值
        if (config_json.contains("type_tag") && config_json["type_tag"].is_string()) {
            global_type_tag = config_json["type_tag"].get<std::string>();
            LOG_DEBUG("环境: 从配置读取全局参数 type_tag: '{}'", global_type_tag);
        } else {
            LOG_DEBUG("环境: 配置中未找到 type_tag，使用默认值: '{}'", global_type_tag);
        }

        // 直接使用共享指针，避免复制
        auto new_params = param_registry_.createDefaultParamValues(global_type_tag);
        if (!new_params) {
            LOG_ERROR("环境: 无法为 type_tag '{}' 创建默认参数值", global_type_tag);
            return false;
        }

        LOG_INFO("环境: 全局参数默认值已创建，type_tag: '{}'", global_type_tag);

        // 使用 params 模块的高级接口从 JSON 加载参数值
        if (!new_params->loadFromJson(config_json, param_registry_)) {
            LOG_ERROR("环境: 从 JSON 加载全局参数值失败");
            return false;
        }

        // 直接更新全局参数共享指针（ParamValues 内部已线程安全）
        global_params_ = new_params;  // 共享所有权，避免复制

        LOG_INFO("环境: 成功加载 {} 个全局参数", global_params_->size());
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR("环境: 加载全局参数时发生异常: {}", e.what());
        return false;
    }
}

bool Environment::preConfigureCacheSettings(const nlohmann::json& config_json) {
    LOG_INFO("环境: 开始预配置缓存参数");
    try {
        // 从config_json直接读取缓存配置，如果不存在则使用默认值
        int env_max_age = 5;  // 默认值
        int env_max_size = 1000;  // 默认值
        int reg_max_age = 10;  // 默认值
        int reg_max_size = 500;  // 默认值

        // 尝试从config_json读取缓存配置
        if (config_json.contains("environment_cache_max_age_minutes") &&
            config_json["environment_cache_max_age_minutes"].is_number_integer()) {
            env_max_age = config_json["environment_cache_max_age_minutes"].get<int>();
        }
        if (config_json.contains("environment_cache_max_size") &&
            config_json["environment_cache_max_size"].is_number_integer()) {
            env_max_size = config_json["environment_cache_max_size"].get<int>();
        }
        if (config_json.contains("param_registry_cache_max_age_minutes") &&
            config_json["param_registry_cache_max_age_minutes"].is_number_integer()) {
            reg_max_age = config_json["param_registry_cache_max_age_minutes"].get<int>();
        }
        if (config_json.contains("param_registry_cache_max_size") &&
            config_json["param_registry_cache_max_size"].is_number_integer()) {
            reg_max_size = config_json["param_registry_cache_max_size"].get<int>();
        }

        // 设置Environment缓存配置
        cache_max_age_ = std::chrono::minutes(env_max_age);
        cache_max_size_ = static_cast<size_t>(env_max_size);
        unified_cache_.updateConfiguration(cache_max_age_, cache_max_size_);

        // 设置ParamRegistry缓存配置（在参数加载之前）
        param_registry_.configureCacheSettings(
            std::chrono::minutes(reg_max_age), static_cast<size_t>(reg_max_size));

        LOG_INFO("环境: 缓存配置完成 - Environment({}分钟,{}条目), ParamRegistry({}分钟,{}条目)",
                cache_max_age_.count(), cache_max_size_, reg_max_age, reg_max_size);

        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("环境: 预配置缓存设置时发生异常: {}", e.what());
        return false;
    }
}



bool Environment::configureCacheSettings() {
    LOG_INFO("环境: 开始配置缓存参数");
    try {
        // Environment缓存参数
        auto env_max_age = global_params_->getValueOrDefault<int>(
            "environment_cache_max_age_minutes", 5);
        auto env_max_size = global_params_->getValueOrDefault<int>(
            "environment_cache_max_size", 1000);

        cache_max_age_ = std::chrono::minutes(env_max_age);
        cache_max_size_ = static_cast<size_t>(env_max_size);

        // 重新配置Environment缓存
        unified_cache_.updateConfiguration(cache_max_age_, cache_max_size_);

        // ParamRegistry缓存参数
        auto reg_max_age = global_params_->getValueOrDefault<int>(
            "param_registry_cache_max_age_minutes", 10);
        auto reg_max_size = global_params_->getValueOrDefault<int>(
            "param_registry_cache_max_size", 500);

        param_registry_.configureCacheSettings(
            std::chrono::minutes(reg_max_age), static_cast<size_t>(reg_max_size));

        LOG_INFO("环境: 缓存设置已配置 - Environment({}分钟,{}条目), ParamRegistry({}分钟,{}条目)",
                cache_max_age_.count(), cache_max_size_, reg_max_age, reg_max_size);

        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("环境: 配置缓存设置时发生异常: {}", e.what());
        return false;
    }
}



// --- 核心组件访问 ---
std::shared_ptr<IGridMap> Environment::getMapDataSource() const {
    return mapDataSource_;
}



NSParams::ParamRegistry& Environment::getParamRegistry() {
    return param_registry_;
}

const NSParams::ParamRegistry& Environment::getParamRegistry() const {
    return param_registry_;
}

std::shared_ptr<CollisionEngine<ObjectMap>> Environment::getCollisionEngine() const {
    return collision_engine_;
}

std::shared_ptr<GeometryManager> Environment::getGeometryManager() const {
    return geometry_manager_;
}

std::shared_ptr<CoordinateManager> Environment::getCoordinateManager() const {
    return coordinate_manager_;
}

std::shared_ptr<TaskSpace> Environment::getTaskSpace(const std::string& space_id) const {
    if (!coordinate_manager_) {
        LOG_WARN("环境: 坐标系统管理器未初始化，无法获取任务空间");
        return nullptr;
    }
    return coordinate_manager_->getTaskSpace(space_id);
}

void Environment::setCoordinateManager(std::shared_ptr<CoordinateManager> manager) {
    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    unified_cache_.executeUpdateWithTypeInvalidation([this, &manager]() {
        // 数据更新函数：设置坐标系统管理器
        coordinate_manager_ = std::move(manager);
        LOG_INFO("环境: 已设置新的坐标系统管理器");
        return true;
    }, {"spatial"}); // 坐标系统管理器变化主要影响空间查询
}

// --- 算法对象管理方法实现 ---
void Environment::setTaskAllocator(std::shared_ptr<NSAlgorithm::ITaskAllocator> allocator) {
    task_allocator_ = allocator;
    LOG_DEBUG("环境: 任务分配器已设置");
}

std::shared_ptr<NSAlgorithm::ITaskAllocator> Environment::getTaskAllocator() const {
    return task_allocator_;
}

void Environment::setPathPlanner(std::shared_ptr<NSAlgorithm::IPathPlanner> planner) {
    path_planner_ = planner;
    LOG_DEBUG("环境: 路径规划器已设置");
}

std::shared_ptr<NSAlgorithm::IPathPlanner> Environment::getPathPlanner() const {
    return path_planner_;
}

void Environment::setTrajectoryOptimizer(std::shared_ptr<NSAlgorithm::ITrajectoryOptimizer> optimizer) {
    trajectory_optimizer_ = optimizer;
    LOG_DEBUG("环境: 轨迹优化器已设置");
}

std::shared_ptr<NSAlgorithm::ITrajectoryOptimizer> Environment::getTrajectoryOptimizer() const {
    return trajectory_optimizer_;
}

void Environment::setTrajectoryEvaluator(std::shared_ptr<NSAlgorithm::ITrajectoryEvaluator> evaluator) {
    trajectory_evaluator_ = evaluator;
    LOG_DEBUG("环境: 轨迹评估器已设置");
}

std::shared_ptr<NSAlgorithm::ITrajectoryEvaluator> Environment::getTrajectoryEvaluator() const {
    return trajectory_evaluator_;
}

// --- 全局参数访问 ---
std::optional<NSParams::ParamValue> Environment::getGlobalParam(const std::string& key) const {
    if (global_params_ && global_params_->hasParam(key)) {
        return global_params_->getValueVariant(key);
    }
    return std::nullopt;
}

bool Environment::setGlobalParam(const std::string& key, const NSParams::ParamValue& value) {
    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    return unified_cache_.executeUpdateWithFullInvalidation([this, &key, &value]() {
        // 数据更新函数：执行实际的参数设置操作
        try {
            if (!global_params_) {
                LOG_ERROR("环境: 全局参数未初始化，无法设置参数 '{}'", key);
                return false;
            }
            bool success = global_params_->setValueVariant(key, value, param_registry_);
            if (success) {
                LOG_DEBUG("环境: 设置全局参数 '{}' 成功", key);
            } else {
                LOG_WARN("环境: 设置全局参数 '{}' 失败，可能是类型不匹配或约束违反", key);
            }
            return success;
        } catch (const std::exception& e) {
            LOG_ERROR("环境: 设置全局参数 '{}' 时发生异常: {}", key, e.what());
            return false;
        }
    });
}

// --- 对象管理 ---
template<typename T>
bool Environment::addObject(std::shared_ptr<T> obj) {
    if (!obj) {
        LOG_ERROR("环境: 尝试添加空对象指针");
        return false;
    }

    LOG_INFO("环境: 添加对象 '{}' (ID: {})", obj->getName(), obj->getId());

    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    return unified_cache_.executeUpdateWithFullInvalidation([this, &obj]() {
        // 数据更新函数：执行实际的对象添加操作，需要保护数据源访问
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);

        // 添加到对象存储
        bool success = object_storage_.addObject(obj);

        if (success) {
            // 如果有空间索引，添加到空间索引
            if (spatial_index_) {
                try {
                    // 获取对象的WGS84位置
                    auto entity_obj = std::dynamic_pointer_cast<EntityObject>(obj);
                    if (entity_obj) {
                        SpatialObject spatial_obj;
                        spatial_obj.id = obj->getId();
                        spatial_obj.position = entity_obj->getWGS84Position();
                        spatial_obj.localBounds = entity_obj->getBoundingBox();

                        spatial_index_->addObject(spatial_obj);
                        LOG_DEBUG("环境: 对象 {} 已添加到空间索引", obj->getId());
                    } else {
                        LOG_DEBUG("环境: 对象 {} 不是EntityObject，跳过空间索引添加", obj->getId());
                    }
                } catch (const std::exception& e) {
                    LOG_WARN("环境: 向空间索引添加对象 {} 失败: {}", obj->getId(), e.what());
                }
            }

            // 添加到属性索引
            try {
                attribute_index_.addOrUpdateAttribute(obj->getId(), Attributes::TYPE, obj->getTypeTag());
                LOG_DEBUG("环境: 对象 {} 已添加到属性索引", obj->getId());
            } catch (const std::exception& e) {
                LOG_WARN("环境: 向属性索引添加对象 {} 失败: {}", obj->getId(), e.what());
            }

            LOG_DEBUG("环境: 对象 {} 添加成功", obj->getId());
        }

        return success;
    });
}

bool Environment::removeObject(const ObjectID& id) {
    LOG_INFO("环境: 移除对象 ID: {}", id);

    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    return unified_cache_.executeUpdateWithFullInvalidation([this, &id]() {
        // 数据更新函数：执行实际的对象移除操作，需要保护数据源访问
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);

        // 首先检查对象是否存在
        auto obj = object_storage_.getObject(id);
        if (!obj) {
            LOG_DEBUG("环境: 尝试移除不存在的对象 ID: {}，可能已被移除", id);
            return false;
        }

        bool removal_success = true;
        std::vector<std::string> failed_operations;

        // 1. 从属性索引中移除
        try {
            attribute_index_.removeAllAttributes(id);
            LOG_DEBUG("环境: 对象 {} 已从属性索引中移除", id);
        } catch (const std::exception& e) {
            LOG_WARN("环境: 从属性索引移除对象 {} 失败: {}", id, e.what());
            failed_operations.push_back("属性索引");
            removal_success = false;
        }

        // 2. 从空间索引中移除
        if (spatial_index_) {
            try {
                spatial_index_->removeObject(id);
                LOG_DEBUG("环境: 对象 {} 已从空间索引中移除", id);
            } catch (const std::exception& e) {
                LOG_WARN("环境: 从空间索引移除对象 {} 失败: {}", id, e.what());
                failed_operations.push_back("空间索引");
                removal_success = false;
            }
        }

        // 3. 从对象存储中移除（只有在所有索引移除成功后才执行）
        bool storage_success = false;
        if (removal_success) {
            try {
                storage_success = object_storage_.removeObject(id);
                if (storage_success) {
                    LOG_DEBUG("环境: 对象 {} 已从对象存储中移除", id);
                } else {
                    LOG_WARN("环境: 对象 {} 从对象存储移除失败", id);
                }
            } catch (const std::exception& e) {
                LOG_ERROR("环境: 从对象存储移除对象 {} 时发生异常: {}", id, e.what());
                storage_success = false;
            }
        } else {
            LOG_WARN("环境: 由于索引移除失败，跳过对象存储移除以避免不一致。失败的操作: {}",
                     std::accumulate(failed_operations.begin(), failed_operations.end(), std::string(),
                         [](const std::string& a, const std::string& b) {
                             return a.empty() ? b : a + ", " + b;
                         }));
        }

        // 返回操作是否完全成功
        bool success = removal_success && storage_success;
        if (success) {
            LOG_INFO("环境: 对象 {} 完全移除成功", id);
        } else {
            LOG_ERROR("环境: 对象 {} 移除失败，数据一致性得到保护", id);
        }
        return success;
    });
}

// --- 内部方法 ---
void Environment::notifyObjectUpdate(const ObjectID& object_id, const EntityStateSnapshot& snapshot) {
    // LOG_TRACE("环境: 收到对象 {} 的状态更新通知", object_id);

    // 根据更新类型精确失效缓存
    bool position_changed = snapshot.hasPositionChanged();
    bool attributes_changed = snapshot.hasAttributesChanged();

    // 确定受影响的缓存类型
    std::vector<std::string> affected_types;

    if (position_changed) {
        // 位置变化影响空间查询和区域查询
        affected_types.push_back("spatial");
        affected_types.push_back("zones");
        // LOG_TRACE("环境: 对象 {} 位置变化，将失效空间和区域缓存", object_id);
    }

    if (attributes_changed) {
        // 属性变化影响属性查询
        affected_types.push_back("attribute");

        // 检查是否是类型属性变化，这会影响对象分类查询
        if (snapshot.old_object_type.has_value()) {
            affected_types.push_back("object");
            affected_types.push_back("zones");
            LOG_TRACE("环境: 对象 {} 类型属性变化，将失效对象和区域缓存", object_id);
        }
    }

    // 对象状态变化总是影响对象查询缓存
    affected_types.push_back("object");

    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    unified_cache_.executeUpdateWithTypeInvalidation([this, &object_id, &snapshot]() {
        // 数据更新函数：执行实际的索引更新操作，需要保护数据源访问
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);

        bool update_success = true;

        try {
            // 更新属性索引
            updateAttributeIndexes(snapshot);
            LOG_TRACE("环境: 对象 {} 属性索引更新成功", object_id);
        } catch (const std::exception& e) {
            LOG_ERROR("环境: 对象 {} 属性索引更新失败: {}", object_id, e.what());
            update_success = false;
        }

        try {
            // 更新关系索引
            updateRelationshipIndexes(snapshot);
            LOG_TRACE("环境: 对象 {} 关系索引更新成功", object_id);
        } catch (const std::exception& e) {
            LOG_ERROR("环境: 对象 {} 关系索引更新失败: {}", object_id, e.what());
            update_success = false;
        }

        // 更新空间索引（如果有的话）
        if (spatial_index_) {
            try {
                updateSpatialIndexes(snapshot);
                LOG_TRACE("环境: 对象 {} 空间索引更新成功", object_id);
            } catch (const std::exception& e) {
                LOG_ERROR("环境: 对象 {} 空间索引更新失败: {}", object_id, e.what());
                update_success = false;
            }
        }

        if (update_success) {
            LOG_DEBUG("环境: 对象 {} 所有索引更新成功", object_id);
        } else {
            LOG_WARN("环境: 对象 {} 部分索引更新失败", object_id);
        }

        return update_success;
    }, affected_types);
}

void Environment::updateAttributeIndexes(const EntityStateSnapshot& snapshot) {
    LOG_TRACE("环境: 增量更新对象 {} 的属性索引", snapshot.id);

    bool attributes_updated = false;

    // 1. 更新对象类型属性（如果类型发生变化）
    if (snapshot.old_object_type.has_value() &&
        snapshot.old_object_type.value() != snapshot.object_type) {
        // 移除旧类型索引
        attribute_index_.removeAttribute(snapshot.id, Attributes::TYPE);
        LOG_DEBUG("环境: 移除对象 {} 的旧类型索引: {}", snapshot.id, snapshot.old_object_type.value());
        attributes_updated = true;
    }

    // 添加/更新新类型索引
    if (!snapshot.object_type.empty()) {
        attribute_index_.addOrUpdateAttribute(snapshot.id, Attributes::TYPE, snapshot.object_type);
        LOG_DEBUG("环境: 更新对象 {} 的类型索引: {}", snapshot.id, snapshot.object_type);
        attributes_updated = true;
    }

    // 属性索引更新结果记录（缓存失效由notifyObjectUpdate统一处理）
    if (attributes_updated) {
        LOG_DEBUG("环境: 对象 {} 属性索引更新完成", snapshot.id);
    }

    // 2. 更新状态属性（仅在状态实际发生变化时）
    if (snapshot.old_status.has_value() &&
        snapshot.old_status.value() != snapshot.current_status) {
        // 移除旧状态索引
        attribute_index_.removeAttribute(snapshot.id, Attributes::STATUS);
        LOG_DEBUG("环境: 移除对象 {} 的旧状态索引: {}", snapshot.id, snapshot.old_status.value());

        // 添加新状态索引
        if (!snapshot.current_status.empty()) {
            attribute_index_.addOrUpdateAttribute(snapshot.id, Attributes::STATUS, snapshot.current_status);
            LOG_DEBUG("环境: 更新对象 {} 的状态索引: {}", snapshot.id, snapshot.current_status);
        }
    } else if (!snapshot.current_status.empty() && !snapshot.old_status.has_value()) {
        // 首次设置状态
        attribute_index_.addOrUpdateAttribute(snapshot.id, Attributes::STATUS, snapshot.current_status);
        LOG_DEBUG("环境: 首次设置对象 {} 的状态索引: {}", snapshot.id, snapshot.current_status);
    }

    // 3. 更新能力属性（仅在能力发生变化时）
    if (snapshot.old_capabilities.has_value() &&
        snapshot.old_capabilities.value() != snapshot.current_capabilities) {

        // 移除旧能力索引
        for (const auto& old_capability : snapshot.old_capabilities.value()) {
            attribute_index_.removeAttribute(snapshot.id, "capability_" + old_capability);
        }
        // 移除旧能力列表JSON
        attribute_index_.removeAttribute(snapshot.id, "capabilities");
        LOG_DEBUG("环境: 移除对象 {} 的旧能力索引", snapshot.id);

        // 添加新能力索引
        for (const auto& capability : snapshot.current_capabilities) {
            attribute_index_.addOrUpdateAttribute(snapshot.id, "capability_" + capability, "true");
        }
        // 添加新能力列表JSON
        if (!snapshot.current_capabilities.empty()) {
            nlohmann::json capabilities_json = snapshot.current_capabilities;
            attribute_index_.addOrUpdateAttribute(snapshot.id, "capabilities", capabilities_json.dump());
        }
        LOG_DEBUG("环境: 更新对象 {} 的能力索引，新增 {} 个能力",
                 snapshot.id, snapshot.current_capabilities.size());
    } else if (!snapshot.current_capabilities.empty() && !snapshot.old_capabilities.has_value()) {
        // 首次设置能力
        for (const auto& capability : snapshot.current_capabilities) {
            attribute_index_.addOrUpdateAttribute(snapshot.id, "capability_" + capability, "true");
        }
        nlohmann::json capabilities_json = snapshot.current_capabilities;
        attribute_index_.addOrUpdateAttribute(snapshot.id, "capabilities", capabilities_json.dump());
        LOG_DEBUG("环境: 首次设置对象 {} 的能力索引，共 {} 个能力",
                 snapshot.id, snapshot.current_capabilities.size());
    }
}

void Environment::updateRelationshipIndexes(const EntityStateSnapshot& snapshot) {
    LOG_TRACE("环境: 增量更新对象 {} 的关系索引", snapshot.id);

    // 检查父子关系是否发生变化
    bool parent_changed = false;
    ObjectID old_parent = NSUtils::INVALID_OBJECT_ID;
    ObjectID new_parent = snapshot.current_parent_id;

    if (snapshot.old_parent_id.has_value()) {
        old_parent = snapshot.old_parent_id.value();
        parent_changed = (old_parent != new_parent);
    } else {
        // 如果没有旧父对象信息，但当前有父对象，则认为是新建关系
        parent_changed = (new_parent != NSUtils::INVALID_OBJECT_ID);
    }

    if (parent_changed) {
        LOG_DEBUG("环境: 对象 {} 的父对象从 {} 变更为 {}", snapshot.id, old_parent, new_parent);

        // 1. 移除旧的父子关系索引
        if (old_parent != NSUtils::INVALID_OBJECT_ID) {
            // 移除子对象的parent属性
            attribute_index_.removeAttribute(snapshot.id, "parent");

            // 移除父对象的child属性
            attribute_index_.removeAttribute(old_parent, "child_" + snapshot.id);

            LOG_DEBUG("环境: 移除对象 {} 与旧父对象 {} 的关系索引", snapshot.id, old_parent);
        }

        // 2. 添加新的父子关系索引
        if (new_parent != NSUtils::INVALID_OBJECT_ID) {
            // 为子对象设置parent属性
            attribute_index_.addOrUpdateAttribute(snapshot.id, "parent", new_parent);

            // 为父对象添加child属性
            attribute_index_.addOrUpdateAttribute(new_parent, "child_" + snapshot.id, "true");

            LOG_DEBUG("环境: 建立对象 {} 与新父对象 {} 的关系索引", snapshot.id, new_parent);
        }

        // 3. 关系索引更新结果记录（缓存失效由notifyObjectUpdate统一处理）
        LOG_DEBUG("环境: 对象 {} 关系索引更新完成", snapshot.id);
    } else {
        LOG_TRACE("环境: 对象 {} 的父子关系未发生变化", snapshot.id);
    }

    // 4. 更新其他可能的关系索引（如兄弟关系、层级关系等）
    // 这里可以根据需要添加更多关系索引逻辑
}

void Environment::updateSpatialIndexes(const EntityStateSnapshot& snapshot) {
    LOG_TRACE("环境: 增量更新对象 {} 的空间索引", snapshot.id);

    if (!spatial_index_) {
        LOG_WARN("环境: 空间索引管理器未初始化，跳过空间索引更新");
        return;
    }

    // 检查位置或包围盒是否发生变化
    bool position_changed = false;
    bool bounds_changed = false;

    if (snapshot.old_position.has_value()) {
        // 转换为ECEF坐标进行几何计算
        EcefPoint current_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(snapshot.current_position);
        EcefPoint old_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(snapshot.old_position.value());
        Vector3D pos_diff = current_ecef - old_ecef;
        position_changed = pos_diff.squaredNorm() > (Constants::GEOMETRY_EPSILON * Constants::GEOMETRY_EPSILON);
    } else {
        // 如果没有旧位置信息，但当前有有效位置，则认为是新增
        position_changed = !snapshot.current_position.hasNaN();
    }

    if (snapshot.old_bounds.has_value() && snapshot.current_bounds.has_value()) {
        // 检查包围盒是否发生变化
        const auto& old_bounds = snapshot.old_bounds.value();
        const auto& new_bounds = snapshot.current_bounds.value();
        // 检查AABB是否近似相等（FCL AABB没有isApprox方法）
        fcl::Vector3d old_min = old_bounds.min_;
        fcl::Vector3d old_max = old_bounds.max_;
        fcl::Vector3d new_min = new_bounds.min_;
        fcl::Vector3d new_max = new_bounds.max_;
        bounds_changed = !old_min.isApprox(new_min, Constants::GEOMETRY_EPSILON) ||
                        !old_max.isApprox(new_max, Constants::GEOMETRY_EPSILON);
    } else if (!snapshot.old_bounds.has_value() && snapshot.current_bounds.has_value()) {
        // 新增包围盒
        bounds_changed = true;
    } else if (snapshot.old_bounds.has_value() && !snapshot.current_bounds.has_value()) {
        // 移除包围盒
        bounds_changed = true;
    }

    if (position_changed || bounds_changed) {
        LOG_DEBUG("环境: 对象 {} 的空间属性发生变化 (位置变化: {}, 包围盒变化: {})",
                 snapshot.id, position_changed, bounds_changed);

        bool spatial_update_success = true;

        // 1. 从旧位置移除对象
        if (snapshot.old_position.has_value() || snapshot.old_bounds.has_value()) {
            try {
                spatial_index_->removeObject(snapshot.id);
                LOG_DEBUG("环境: 从空间索引中移除对象 {}", snapshot.id);
            } catch (const std::exception& e) {
                LOG_WARN("环境: 从空间索引移除对象 {} 失败: {}", snapshot.id, e.what());
                spatial_update_success = false;
            }
        }

        // 2. 在新位置添加对象
        if (!snapshot.current_position.hasNaN()) {
            try {
                // 获取对象以添加到空间索引
                auto obj = object_storage_.getObject(snapshot.id);
                if (obj) {
                    auto entity_obj = std::dynamic_pointer_cast<EntityObject>(obj);
                    if (entity_obj) {
                        // 创建SpatialObject包装器
                        SpatialObject spatial_obj;
                        spatial_obj.id = obj->getId();
                        spatial_obj.position = entity_obj->getWGS84Position();
                        spatial_obj.localBounds = entity_obj->getBoundingBox();

                        spatial_index_->addObject(spatial_obj);
                        LOG_DEBUG("环境: 将对象 {} 添加到新的空间索引位置", snapshot.id);
                    } else {
                        LOG_DEBUG("环境: 对象 {} 不是EntityObject，跳过空间索引更新", snapshot.id);
                    }
                } else {
                    LOG_DEBUG("环境: 无法从对象存储中找到对象 {} 以更新空间索引，可能已被移除", snapshot.id);
                    spatial_update_success = false;
                }
            } catch (const std::exception& e) {
                LOG_WARN("环境: 向空间索引添加对象 {} 失败: {}", snapshot.id, e.what());
                spatial_update_success = false;
            }
        }

        // 3. 记录空间索引更新结果（缓存失效由notifyObjectUpdate统一处理）
        if (spatial_update_success) {
            LOG_DEBUG("环境: 对象 {} 空间索引更新成功", snapshot.id);
        } else {
            LOG_DEBUG("环境: 对象 {} 空间索引更新失败", snapshot.id);
        }
    } else {
        LOG_TRACE("环境: 对象 {} 的空间属性未发生变化", snapshot.id);
    }
}

// --- 属性管理 ---
bool Environment::setObjectAttribute(const ObjectID& obj_id, const std::string& key, const std::string& value) {
    // 检查对象是否存在
    auto obj = this->getObjectById(obj_id);
    if (!obj) {
        LOG_WARN("环境: 尝试为不存在的对象ID: {} 设置属性", obj_id);
        return false;
    }

    // 确定受影响的缓存类型
    std::vector<std::string> affected_types = {"attribute"};
    if (key == Attributes::TYPE) {
        affected_types.push_back("object");
        affected_types.push_back("zones");
    }

    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    return unified_cache_.executeUpdateWithTypeInvalidation([this, &obj_id, &key, &value, &obj]() {
        // 数据更新函数：执行实际的属性设置操作，需要保护数据源访问
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        try {
            attribute_index_.addOrUpdateAttribute(obj_id, key, value);
            LOG_DEBUG("环境: 对象 '{}' (ID: {}) 设置属性'{}' = '{}'", obj->getName(), obj_id, key, value);
            return true;
        } catch (const std::exception& e) {
            LOG_ERROR("环境: 设置对象 {} 属性 '{}' 失败: {}", obj_id, key, e.what());
            return false;
        }
    }, affected_types);
}

bool Environment::setObjectAttributes(const ObjectID& obj_id,
                                    const std::unordered_map<std::string, std::string>& attributes) {
    // 检查对象是否存在
    auto obj = this->getObjectById(obj_id);
    if (!obj) {
        LOG_WARN("环境: 尝试为不存在的对象ID: {} 设置属性", obj_id);
        return false;
    }

    // 确定受影响的缓存类型
    std::vector<std::string> affected_types = {"attribute"};
    for (const auto& [key, value] : attributes) {
        if (key == Attributes::TYPE) {
            affected_types.push_back("object");
            affected_types.push_back("zones");
            break; // 只需要添加一次
        }
    }

    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    return unified_cache_.executeUpdateWithTypeInvalidation([this, &obj_id, &attributes, &obj]() {
        // 数据更新函数：执行实际的属性设置操作，需要保护数据源访问
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        try {
            attribute_index_.addOrUpdateAttributes(obj_id, attributes);
            LOG_DEBUG("环境: 对象 '{}' (ID: {}) 批量设置 {} 个属性", obj->getName(), obj_id, attributes.size());
            return true;
        } catch (const std::exception& e) {
            LOG_ERROR("环境: 批量设置对象 {} 属性失败: {}", obj_id, e.what());
            return false;
        }
    }, affected_types);
}

bool Environment::removeObjectAttribute(const ObjectID& obj_id, const std::string& key) {
    // 检查对象是否存在
    auto obj = this->getObjectById(obj_id);
    if (!obj) {
        LOG_WARN("环境: 尝试为不存在的对象ID: {} 移除属性", obj_id);
        return false;
    }

    // 确定受影响的缓存类型
    std::vector<std::string> affected_types = {"attribute"};
    if (key == Attributes::TYPE) {
        affected_types.push_back("object");
        affected_types.push_back("zones");
    }

    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    return unified_cache_.executeUpdateWithTypeInvalidation([this, &obj_id, &key, &obj]() {
        // 数据更新函数：执行实际的属性移除操作，需要保护数据源访问
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);
        try {
            bool result = attribute_index_.removeAttribute(obj_id, key);
            if (result) {
                LOG_DEBUG("环境: 对象 '{}' (ID: {}) 移除属性'{}'", obj->getName(), obj_id, key);
            } else {
                LOG_WARN("环境: 对象 '{}' (ID: {}) 没有属性'{}'", obj->getName(), obj_id, key);
            }
            return result;
        } catch (const std::exception& e) {
            LOG_ERROR("环境: 移除对象 {} 属性 '{}' 失败: {}", obj_id, key, e.what());
            return false;
        }
    }, affected_types);
}

std::optional<std::string> Environment::getObjectAttributeValue(const ObjectID& obj_id, const std::string& key) const {
    // 保护属性索引访问
    std::shared_lock<std::shared_mutex> lock(objects_mutex_);
    return attribute_index_.getAttributeValue(obj_id, key);
}

std::unordered_map<std::string, std::string> Environment::getAllObjectAttributes(const ObjectID& obj_id) const {
    // 保护属性索引访问
    std::shared_lock<std::shared_mutex> lock(objects_mutex_);
    return attribute_index_.getAllAttributes(obj_id);
}

bool Environment::hasObjectAttribute(const ObjectID& obj_id, const std::string& key) const {
    // 保护属性索引访问
    std::shared_lock<std::shared_mutex> lock(objects_mutex_);
    return attribute_index_.hasAttribute(obj_id, key);
}

// --- 基于函数注入的查询接口 ---
std::vector<ObjectID> Environment::findObjectIdsByAttribute(
    const std::string& attribute_key, const std::string& attribute_value) const {

    // 生成缓存键
    std::string cache_key = generateCacheKey("attr", {attribute_key, attribute_value});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    return unified_cache_.getOrCompute(cache_key, [this, &attribute_key, &attribute_value]() {
        // 数据提供函数：执行实际查询，需要保护数据源访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto result = attribute_index_.findByAttribute(attribute_key, attribute_value);
        LOG_DEBUG("环境查询: 按属性'{}' = '{}' 找到 {} 个对象（已缓存）",
                 attribute_key, attribute_value, result.size());
        return result;
    }, "attribute");
}

AttributeQueryResult Environment::findObjectsByAttribute(
    const std::string& attribute_key, const std::string& attribute_value) const {
    AttributeQueryResult result;

    // 复用已缓存的findObjectIdsByAttribute方法
    result.object_ids = findObjectIdsByAttribute(attribute_key, attribute_value);

    LOG_DEBUG("环境查询: 按属性'{}' = '{}' 找到 {} 个对象（使用缓存）",
             attribute_key, attribute_value, result.object_ids.size());

    return result;
}

AttributeQueryResult Environment::findObjectsByFilter(
    const std::function<bool(const EntityObject&)>& filter_func) const {
    AttributeQueryResult result;

    // 注意：自定义过滤器无法缓存，因为函数对象无法序列化为缓存键
    // 如果需要缓存，建议使用基于属性的查询方法
    LOG_TRACE("环境查询: 执行自定义过滤器查询（无缓存）");

    // 保护对象存储访问
    std::shared_lock<std::shared_mutex> lock(objects_mutex_);
    auto all_objects = object_storage_.getObjectsByType<EntityObject>();

    for (const auto& obj : all_objects) {
        if (filter_func(*obj)) {
            result.object_ids.push_back(obj->getId());
        }
    }

    LOG_DEBUG("环境查询: 按自定义过滤器找到 {} 个对象（无缓存）", result.object_ids.size());

    return result;
}

std::vector<std::weak_ptr<const EntityObject>>
Environment::getObjectsByAttribute(const std::string& attribute_name, const std::string& attribute_value) const {
    std::vector<std::weak_ptr<const EntityObject>> result;

    // 复用已缓存的findObjectIdsByAttribute方法
    auto ids = findObjectIdsByAttribute(attribute_name, attribute_value);
    result.reserve(ids.size());

    // 添加对象存在性检查
    {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        for (const auto& id : ids) {
            auto obj = this->getObjectById(id);
            if (obj) {
                result.push_back(obj);
            } else {
                // 对象已被删除，记录警告但继续处理其他对象
                LOG_WARN("环境查询: 缓存中的对象ID {} 对应的对象已不存在", id);
            }
        }
    }

    LOG_DEBUG("环境查询: 按属性'{}' = '{}' 找到 {} 个对象(弱指针，使用缓存)",
             attribute_name, attribute_value, result.size());

    return result;
}

// --- 区域管理 ---
bool Environment::removeZone(const ObjectID& zone_id) {
    LOG_INFO("环境: 移除区域 ID: {}", zone_id);

    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    return unified_cache_.executeUpdateWithFullInvalidation([this, &zone_id]() {
        // 数据更新函数：执行实际的区域移除操作，需要保护数据源访问
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);

        // 1. 首先验证对象是否存在且为Zone类型
        auto zone = this->getObjectById<Zone>(zone_id);
        if (!zone) {
            LOG_WARN("环境: 尝试移除不存在的区域或对象不是Zone类型，ID: {}", zone_id);
            return false;
        }

        LOG_DEBUG("环境: 验证区域 '{}' (ID: {}) 存在，开始移除", zone->getName(), zone_id);

        bool removal_success = true;
        std::vector<std::string> failed_operations;

        // 2. 从属性索引中移除
        try {
            attribute_index_.removeAllAttributes(zone_id);
            LOG_DEBUG("环境: 区域 {} 已从属性索引中移除", zone_id);
        } catch (const std::exception& e) {
            LOG_WARN("环境: 从属性索引移除区域 {} 失败: {}", zone_id, e.what());
            failed_operations.push_back("属性索引");
            removal_success = false;
        }

        // 3. 从空间索引中移除
        if (spatial_index_) {
            try {
                spatial_index_->removeObject(zone_id);
                LOG_DEBUG("环境: 区域 {} 已从空间索引中移除", zone_id);
            } catch (const std::exception& e) {
                LOG_WARN("环境: 从空间索引移除区域 {} 失败: {}", zone_id, e.what());
                failed_operations.push_back("空间索引");
                removal_success = false;
            }
        }

        // 4. 从对象存储中移除（只有在所有索引移除成功后才执行）
        bool storage_success = false;
        if (removal_success) {
            try {
                storage_success = object_storage_.removeObject(zone_id);
                if (storage_success) {
                    LOG_DEBUG("环境: 区域 {} 已从对象存储中移除", zone_id);
                } else {
                    LOG_WARN("环境: 区域 {} 从对象存储移除失败", zone_id);
                }
            } catch (const std::exception& e) {
                LOG_ERROR("环境: 从对象存储移除区域 {} 时发生异常: {}", zone_id, e.what());
                storage_success = false;
            }
        } else {
            LOG_WARN("环境: 由于索引移除失败，跳过对象存储移除以避免不一致。失败的操作: {}",
                     std::accumulate(failed_operations.begin(), failed_operations.end(), std::string(),
                         [](const std::string& a, const std::string& b) {
                             return a.empty() ? b : a + ", " + b;
                         }));
        }

        // 返回操作是否完全成功
        bool success = removal_success && storage_success;
        if (success) {
            LOG_INFO("环境: 区域 '{}' (ID: {}) 完全移除成功", zone->getName(), zone_id);
        } else {
            LOG_ERROR("环境: 区域 {} 移除失败，数据一致性得到保护", zone_id);
        }
        return success;
    });
}

// 添加缺失的getZonesByType方法实现
std::vector<ConstZonePtr> Environment::getZonesByType(ZoneType type) const {
    // 生成缓存键
    std::string cache_key = generateCacheKey("zones_by_type", {std::to_string(static_cast<int>(type))});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    auto zone_ids = unified_cache_.getOrCompute(cache_key, [this, type]() {
        // 数据提供函数：执行实际查询并提取ObjectID，需要保护数据源访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto zones = object_storage_.getZonesByType(type);
        std::vector<ObjectID> zone_ids;
        zone_ids.reserve(zones.size());
        for (const auto& zone : zones) {
            zone_ids.push_back(zone->getId());
        }
        LOG_DEBUG("环境查询: 按类型 {} 获取区域 {} 个（已缓存）", static_cast<int>(type), zone_ids.size());
        return zone_ids;
    }, "zones");

    // 根据缓存的ObjectID构建结果，添加对象存在性检查
    std::vector<ConstZonePtr> result;
    result.reserve(zone_ids.size());
    {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        for (const auto& id : zone_ids) {
            auto zone = this->getObjectById<Zone>(id);
            if (zone) {
                result.push_back(zone);
            } else {
                // 对象已被删除，记录警告但继续处理其他对象
                LOG_WARN("环境查询: 缓存中的区域ID {} 对应的对象已不存在", id);
            }
        }
    }
    return result;
}

std::vector<ConstZonePtr> Environment::getAllZones() const {
    // 生成缓存键
    std::string cache_key = generateCacheKey("zones", {"all"});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    auto zone_ids = unified_cache_.getOrCompute(cache_key, [this]() {
        // 数据提供函数：执行实际查询并提取ObjectID，需要保护数据源访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto zones = object_storage_.getZones();
        std::vector<ObjectID> zone_ids;
        zone_ids.reserve(zones.size());
        for (const auto& zone : zones) {
            zone_ids.push_back(zone->getId());
        }
        LOG_DEBUG("环境查询: 获取所有区域 {} 个（已缓存）", zone_ids.size());
        return zone_ids;
    }, "zones");

    // 根据缓存的ObjectID构建结果，添加对象存在性检查
    std::vector<ConstZonePtr> result;
    result.reserve(zone_ids.size());
    {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        for (const auto& id : zone_ids) {
            auto zone = this->getObjectById<Zone>(id);
            if (zone) {
                result.push_back(zone);
            } else {
                // 对象已被删除，记录警告但继续处理其他对象
                LOG_WARN("环境查询: 缓存中的区域ID {} 对应的对象已不存在", id);
            }
        }
    }
    return result;
}

// --- 区域管理方法 ---
bool Environment::addZone(std::shared_ptr<Zone> zone) {
    if (!zone) {
        LOG_ERROR("环境: 尝试添加空区域指针");
        return false;
    }

    LOG_INFO("环境: 添加区域 '{}' (ID: {})", zone->getName(), zone->getId());

    // 直接委托给addObject方法，它已经使用函数注入模式处理所有缓存失效
    bool success = addObject(zone);

    if (success) {
        LOG_DEBUG("环境: 区域 {} 添加成功", zone->getId());
    }

    return success;
}

std::shared_ptr<const Zone> Environment::getZoneById(const ObjectID& zone_id) const {
    return this->getObjectById<Zone>(zone_id);
}
// --- 空间查询 ---
SpatialQueryResult Environment::findObjectsInRegion(const BoundingBox& region) const {
    SpatialQueryResult result;

    // 生成缓存键（基于区域坐标，使用固定精度和线程安全的格式化）
    std::string region_str = generateSpatialCacheKey(region);
    std::string cache_key = generateCacheKey("spatial_region", {region_str});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    result.object_ids = unified_cache_.getOrCompute(cache_key, [this, &region]() {
        // 数据提供函数：执行实际的空间查询，需要保护数据源访问
        std::vector<ObjectID> object_ids;

        // 保护空间索引访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        if (spatial_index_ && coordinate_manager_) {
            // 将本地坐标区域转换为WGS84区域
            auto task_space = coordinate_manager_->getTaskSpace(Constants::GLOBAL_TASK_SPACE_ID);
            if (task_space) {
                // 转换区域的最小和最大点（直接从ECEF坐标转换为WGS84）
                fcl::Vector3d region_min = region.min_;
                fcl::Vector3d region_max = region.max_;
                // 将ECEF坐标直接转换为WGS84坐标
                EcefPoint min_ecef(region_min.x(), region_min.y(), region_min.z());
                EcefPoint max_ecef(region_max.x(), region_max.y(), region_max.z());
                auto min_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(min_ecef);
                auto max_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(max_ecef);

                // 创建WGS84边界盒
                WGS84BoundingBox wgs84_region;
                wgs84_region.minLongitude = (std::min)(min_wgs84.longitude, max_wgs84.longitude);
                wgs84_region.maxLongitude = (std::max)(min_wgs84.longitude, max_wgs84.longitude);
                wgs84_region.minLatitude = (std::min)(min_wgs84.latitude, max_wgs84.latitude);
                wgs84_region.maxLatitude = (std::max)(min_wgs84.latitude, max_wgs84.latitude);
                wgs84_region.minAltitude = (std::min)(min_wgs84.altitude, max_wgs84.altitude);
                wgs84_region.maxAltitude = (std::max)(min_wgs84.altitude, max_wgs84.altitude);

                object_ids = spatial_index_->findObjectsInRegion(wgs84_region);
                LOG_DEBUG("环境查询: 在区域({}, {}, {}) - ({}, {}, {}) [WGS84: {:.6f},{:.6f} - {:.6f},{:.6f}] 中找到 {} 个对象（已缓存）",
                         region_min.x(), region_min.y(), region_min.z(),
                         region_max.x(), region_max.y(), region_max.z(),
                         wgs84_region.minLongitude, wgs84_region.minLatitude,
                         wgs84_region.maxLongitude, wgs84_region.maxLatitude,
                         object_ids.size());
            } else {
                LOG_WARN("环境: 无法获取全局任务空间，无法执行坐标转换");
            }
        } else {
            LOG_WARN("环境: 空间索引管理器或坐标管理器未初始化，无法执行空间查询");
        }

        return object_ids;
    }, "spatial");

    return result;
}

std::vector<ObjectID> Environment::findObjectsInRegion(
    const WGS84BoundingBox& region,
    const std::optional<SpatialQueryFilter>& filter) const {

    LOG_TRACE("环境查询: WGS84区域查询 [{:.6f},{:.6f}] - [{:.6f},{:.6f}]",
             region.minLongitude, region.minLatitude,
             region.maxLongitude, region.maxLatitude);

    // 生成缓存键
    std::string cache_key = generateWGS84RegionCacheKey(region, filter);

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    return unified_cache_.getOrCompute(cache_key, [this, &region, &filter]() {
        // 数据提供函数：执行实际的空间查询，需要保护数据源访问
        std::vector<ObjectID> object_ids;

        // 保护空间索引访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        if (spatial_index_) {
            object_ids = spatial_index_->findObjectsInRegion(region, filter);
            LOG_DEBUG("环境查询: WGS84区域查询找到 {} 个对象（已缓存）", object_ids.size());
        } else {
            LOG_WARN("环境: 空间索引管理器未初始化，无法执行WGS84空间查询");
        }

        return object_ids;
    }, "spatial_wgs84");
}

std::vector<ObjectID> Environment::findObjectsNearPoint(
    const WGS84Point& point, double radius,
    const std::optional<SpatialQueryFilter>& filter) const {

    LOG_TRACE("环境查询: WGS84点查询 {} 半径 {:.1f}m", point.toString(), radius);

    // 生成缓存键
    std::string cache_key = generateWGS84PointCacheKey(point, radius, filter);

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    return unified_cache_.getOrCompute(cache_key, [this, &point, radius, &filter]() {
        // 数据提供函数：执行实际的空间查询，需要保护数据源访问
        std::vector<ObjectID> object_ids;

        // 保护空间索引访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        if (spatial_index_) {
            object_ids = spatial_index_->findObjectsNearPoint(point, radius, filter);
            LOG_DEBUG("环境查询: WGS84点查询找到 {} 个对象（已缓存）", object_ids.size());
        } else {
            LOG_WARN("环境: 空间索引管理器未初始化，无法执行WGS84点查询");
        }

        return object_ids;
    }, "spatial_wgs84");
}

bool Environment::objectsIntersect(const ObjectID& objectId1, const ObjectID& objectId2) const {
    LOG_TRACE("环境查询: 检查对象 {} 和 {} 是否相交", objectId1, objectId2);

    if (spatial_index_) {
        return spatial_index_->objectsIntersect(objectId1, objectId2);
    } else {
        LOG_WARN("环境: 空间索引管理器未初始化，无法检查对象相交");
        return false;
    }
}

std::optional<WGS84BoundingBox> Environment::getObjectBounds(const ObjectID& objectId) const {
    LOG_TRACE("环境查询: 获取对象 {} 的WGS84边界盒", objectId);

    if (spatial_index_) {
        return spatial_index_->getObjectBounds(objectId);
    } else {
        LOG_WARN("环境: 空间索引管理器未初始化，无法获取对象边界");
        return std::nullopt;
    }
}

std::vector<ObjectID> Environment::getAllSpatialObjectIds() const {
    LOG_TRACE("环境查询: 获取空间索引中的所有对象ID");

    if (spatial_index_) {
        return spatial_index_->getAllObjectIds();
    } else {
        LOG_WARN("环境: 空间索引管理器未初始化，返回空列表");
        return {};
    }
}

bool Environment::configureSpatialIndexCache(bool enable, size_t maxSize, std::chrono::milliseconds maxAge) {
    LOG_INFO("环境: 配置空间索引缓存 - 启用: {}, 最大大小: {}, 最大生存时间: {}ms",
             enable, maxSize, maxAge.count());

    if (spatial_index_) {
        return spatial_index_->configureQueryCache(enable, maxSize, maxAge);
    } else {
        LOG_WARN("环境: 空间索引管理器未初始化，无法配置缓存");
        return false;
    }
}

void Environment::clearSpatialIndexCache() {
    LOG_DEBUG("环境: 清除空间索引缓存");

    if (spatial_index_) {
        spatial_index_->clearQueryCache();
    } else {
        LOG_WARN("环境: 空间索引管理器未初始化，无法清除缓存");
    }
}

size_t Environment::getSpatialIndexMemoryUsage() const {
    if (spatial_index_) {
        return spatial_index_->getEstimatedMemoryUsage();
    } else {
        LOG_WARN("环境: 空间索引管理器未初始化，返回0");
        return 0;
    }
}

std::vector<ConstZonePtr> Environment::getZonesContainingPoint(const EcefPoint& ecef_point) const {
    LOG_DEBUG("环境查询: 查找包含{}的所有区域", ecef_point.toString());

    // 生成缓存键（基于ECEF点坐标，使用固定精度和线程安全的格式化）
    std::string point_str = generateEcefPointCacheKey(ecef_point);
    std::string cache_key = generateCacheKey("zones_containing_point", {point_str});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    auto zone_ids = unified_cache_.getOrCompute(cache_key, [this, &ecef_point]() {
        // 数据提供函数：执行实际查询并提取ObjectID，需要保护数据源访问
        std::vector<ObjectID> zone_ids;

        // 直接访问object_storage_而不是调用getAllZones()，避免嵌套缓存调用
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto zones = object_storage_.getZones();

        for (const auto& zone : zones) {
            if (zone->containsPoint(ecef_point)) {
                zone_ids.push_back(zone->getId());
            }
        }

        LOG_DEBUG("环境查询: 找到 {} 个包含点的区域（已缓存）", zone_ids.size());
        return zone_ids;
    }, "zones");

    // 根据缓存的ObjectID构建结果，添加对象存在性检查
    std::vector<ConstZonePtr> result;
    result.reserve(zone_ids.size());
    {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        for (const auto& id : zone_ids) {
            auto zone = this->getObjectById<Zone>(id);
            if (zone) {
                result.push_back(zone);
            } else {
                // 对象已被删除，记录警告但继续处理其他对象
                LOG_WARN("环境查询: 缓存中的区域ID {} 对应的对象已不存在", id);
            }
        }
    }
    return result;
}

std::vector<ConstZonePtr> Environment::getIntersectingZones(const EcefPoint& ecef_point) const {
    LOG_DEBUG("环境查询: 查找与{}相交的所有区域", ecef_point.toString());

    // 这个方法目前与getZonesContainingPoint 相同，保留以兼容旧代码
    return getZonesContainingPoint(ecef_point);
}

std::vector<ConstZonePtr> Environment::getIntersectingZones(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2) const {
    LOG_DEBUG("环境查询: 查找与ECEF线段 {} - {} 相交的所有区域",
             ecef_p1.toString(), ecef_p2.toString());

    // 生成缓存键（基于线段两个端点）
    std::string p1_str = generateEcefPointCacheKey(ecef_p1);
    std::string p2_str = generateEcefPointCacheKey(ecef_p2);
    std::string cache_key = generateCacheKey("zones_intersecting_segment", {p1_str, p2_str});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    auto zone_ids = unified_cache_.getOrCompute(cache_key, [this, &ecef_p1, &ecef_p2]() {
        // 数据提供函数：执行实际查询并提取ObjectID，需要保护数据源访问
        std::vector<ObjectID> zone_ids;

        // 直接访问object_storage_而不是调用getAllZones()，避免嵌套缓存调用
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto zones = object_storage_.getZones();

        for (const auto& zone : zones) {
            if (zone->intersects(ecef_p1, ecef_p2)) {
                zone_ids.push_back(zone->getId());
            }
        }

        LOG_DEBUG("环境查询: 找到 {} 个与线段相交的区域（已缓存）", zone_ids.size());
        return zone_ids;
    }, "zones");

    // 根据缓存的ObjectID构建结果，添加对象存在性检查
    std::vector<ConstZonePtr> result;
    result.reserve(zone_ids.size());
    {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        for (const auto& id : zone_ids) {
            auto zone = this->getObjectById<Zone>(id);
            if (zone) {
                result.push_back(zone);
            } else {
                // 对象已被删除，记录警告但继续处理其他对象
                LOG_WARN("环境查询: 缓存中的区域ID {} 对应的对象已不存在", id);
            }
        }
    }
    return result;
}

std::vector<ConstZonePtr> Environment::getViolatedZones(const EcefPoint& ecef_point) const {
    LOG_DEBUG("环境查询: 查找{}违反的所有禁飞区", ecef_point.toString());

    // 生成缓存键（基于点坐标）
    std::string point_str = generateEcefPointCacheKey(ecef_point);
    std::string cache_key = generateCacheKey("violated_zones", {point_str});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    auto zone_ids = unified_cache_.getOrCompute(cache_key, [this, &ecef_point]() {
        // 数据提供函数：执行实际查询并提取ObjectID，需要保护数据源访问
        std::vector<ObjectID> zone_ids;

        // 直接访问object_storage_而不是调用getZonesByType()，避免嵌套缓存调用
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto no_fly_zones = object_storage_.getZonesByType(ZoneType::KEEPOUT);

        for (const auto& zone : no_fly_zones) {
            if (zone->containsPoint(ecef_point)) {
                zone_ids.push_back(zone->getId());
            }
        }

        LOG_DEBUG("环境查询: 点违反了 {} 个禁飞区（已缓存）", zone_ids.size());
        return zone_ids;
    }, "zones");

    // 根据缓存的ObjectID构建结果，添加对象存在性检查
    std::vector<ConstZonePtr> result;
    result.reserve(zone_ids.size());
    {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        for (const auto& id : zone_ids) {
            auto zone = this->getObjectById<Zone>(id);
            if (zone) {
                result.push_back(zone);
            } else {
                // 对象已被删除，记录警告但继续处理其他对象
                LOG_WARN("环境查询: 缓存中的区域ID {} 对应的对象已不存在", id);
            }
        }
    }
    return result;
}

// --- 碰撞检测 ---
bool Environment::isPositionValid(const EcefPoint& ecef_point, Time time_stamp, double safety_margin,
                                bool check_threats, bool check_no_fly,
                                const std::unordered_set<ObjectID>& ignored_object_ids) const {
    LOG_DEBUG("环境: 检查{}位置有效性, 时间: {}, 安全距离: {}",
             ecef_point.toString(), time_stamp, safety_margin);

    // 检查地图数据源是否已加载 - 如果未加载，记录警告但继续其他检查
    bool map_check_available = (mapDataSource_ != nullptr);
    if (!map_check_available) {
        LOG_WARN("环境: 地图数据源未加载，跳过地图范围检查，继续其他有效性检查");
    }

    // 检查点是否在地图范围内（仅在地图数据源可用时）
    if (map_check_available) {
        // 直接将ECEF坐标转换为WGS84坐标
        auto wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(ecef_point);

            LOG_DEBUG("环境: 位置有效性检查 - 局部坐标({:.1f}, {:.1f}, {:.1f}) → WGS84({:.6f}, {:.6f}, {:.1f})",
                ecef_point.x(), ecef_point.y(), ecef_point.z(), wgs84.longitude, wgs84.latitude, wgs84.altitude);

        // 使用 getElevation 方法检查点是否在地图范围内
        auto elevation = mapDataSource_->getElevation(wgs84.latitude, wgs84.longitude);
        if (!elevation.has_value()) {
            LOG_WARN("环境: 点({:.1f}, {:.1f}, {:.1f}) [WGS84: {:.6f}, {:.6f}] 不在地图范围内，地图查询失败",
                ecef_point.x(), ecef_point.y(), ecef_point.z(), wgs84.longitude, wgs84.latitude);

            // 添加地图边界信息用于调试
            if (mapDataSource_) {
                auto metadata = mapDataSource_->getMetadata();
                if (metadata.isValid()) {
                    LOG_DEBUG("环境: 地图WGS84边界: {}", metadata.wgs84_bounds.toString());
                } else {
                    LOG_WARN("环境: 地图元数据无效");
                }
            }
            return false;
        } else {
            LOG_DEBUG("环境: 地图查询成功，地面高程: {:.1f}m", elevation.value());
        }
    } else if (!map_check_available) {
        LOG_DEBUG("环境: 地图数据源不可用，跳过地图范围检查");
    }

    // 检查是否在禁飞区内
    if (check_no_fly) {
        auto violated_zones = this->getViolatedZones(ecef_point);
        if (!violated_zones.empty()) {
            LOG_DEBUG("环境: 点({}) 在禁飞区", ecef_point.toString());
            return false;
        }
    }

    // 检查与动态障碍物的碰撞
    if (check_threats) {
        if (collision_engine_) {
            // 使用碰撞引擎 - 直接使用ECEF坐标进行碰撞检测
            CollisionOptions options;
            options.safetyMargin = safety_margin;
            options.timePoint = time_stamp;
            options.ignoredObjectIds = ignored_object_ids;

            auto sphere_shape = std::make_shared<SphereShape>(0.1);
            auto results = collision_engine_->checkGeometryAgainstEnvironment(
                *sphere_shape, ecef_point, Orientation::Identity(), options);
            if (!results.empty() && results[0].hasCollision) {
                LOG_DEBUG("环境: {}与对象'{}'发生碰撞",
                         ecef_point.toString(), results[0].object1Id);
                return false;
            }
        } else {
            LOG_WARN("环境: 碰撞引擎未设置，跳过碰撞检测");
        }
    }

    return true;
}

bool Environment::isSegmentValid(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2, Time t1, Time t2,
                              double safety_margin, bool check_threats, bool check_no_fly,
                              const std::unordered_set<ObjectID>& ignored_object_ids) const {
    LOG_DEBUG("环境: 检查线段有效性- 线段 {}@{} - {}@{}, 安全距离: {}",
             ecef_p1.toString(), t1, ecef_p2.toString(), t2, safety_margin);

    // 检查线段的起点和终点是否有冲突
    if (!isPositionValid(ecef_p1, t1, safety_margin, check_threats, check_no_fly, ignored_object_ids) ||
        !isPositionValid(ecef_p2, t2, safety_margin, check_threats, check_no_fly, ignored_object_ids)) {
        return false;
    }

    // 检查线段与动态障碍物的碰撞
    if (check_threats) {
        if (collision_engine_) {
            // 使用碰撞引擎 - 直接使用ECEF坐标进行碰撞检测
            CollisionOptions options;
            options.safetyMargin = safety_margin;
            options.ignoredObjectIds = ignored_object_ids;

            // 简化为检查线段的起点和终点
            auto sphere_shape = std::make_shared<SphereShape>(0.1);
            auto start_results = collision_engine_->checkGeometryAgainstEnvironment(
                *sphere_shape, ecef_p1, Orientation::Identity(), options);
            auto end_results = collision_engine_->checkGeometryAgainstEnvironment(
                *sphere_shape, ecef_p2, Orientation::Identity(), options);

            if ((!start_results.empty() && start_results[0].hasCollision) ||
                (!end_results.empty() && end_results[0].hasCollision)) {
                LOG_DEBUG("环境: 线段与动态障碍物发生碰撞");
                return false;
            }
        } else {
            LOG_WARN("环境: 碰撞引擎未设置，跳过线段碰撞检测");
        }
    }

    // 检查线段是否穿过禁飞区
    if (check_no_fly) {
        // 参数中已经是ECEF坐标，直接使用
        // EcefPoint ecef_p1 = ecef_p1; // 已经是ECEF坐标
        // EcefPoint ecef_p2 = ecef_p2; // 已经是ECEF坐标

        // 线段离散化采样检查
        int num_samples = 10; // 可以根据线段长度调整
        for (int i = 1; i < num_samples; ++i) {
            double t = static_cast<double>(i) / static_cast<double>(num_samples);

            // 在ECEF坐标系中进行线性插值
            EcefPoint interpolated_ecef(
                ecef_p1.toVector3D() * (1.0 - t) + ecef_p2.toVector3D() * t
            );

            // 转换回WGS84坐标用于外部接口
            WGS84Point interpolated_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(interpolated_ecef);
            Time time = t1 + (t2 - t1) * t;

            // 将WGS84坐标转换为ECEF坐标用于getViolatedZones
            EcefPoint interpolated_ecef_for_zones = NSUtils::CoordinateConverter::wgs84ToECEF(interpolated_wgs84);
            auto violated_zones = this->getViolatedZones(interpolated_ecef_for_zones);
            if (!violated_zones.empty()) {
                LOG_DEBUG("环境: 线段在插值点 {} 穿过禁飞区",
                         interpolated_wgs84.toString());
                return false;
            }
        }
    }

    return true;
}

bool Environment::checkDynamicStaticCollisions(double safety_margin,
                                            const std::unordered_set<ObjectID>& ignored_object_ids) const {
    LOG_DEBUG("环境: 检查动态对象与静态对象之间的碰撞, 安全距离: {}", safety_margin);

    if (collision_engine_) {
        // 使用碰撞引擎
        CollisionOptions options;
        options.safetyMargin = safety_margin;
        options.ignoredObjectIds = ignored_object_ids;

        // 检查所有动态对象与静态对象的碰撞，需要保护数据源访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto movable_objects = object_storage_.getMovableObjects();

        for (const auto& obj : movable_objects) {
            if (ignored_object_ids.count(obj->getId()) > 0) continue;

            auto results = collision_engine_->checkObjectAgainstEnvironment(obj, options);
            if (!results.empty() && results[0].hasCollision) {
                LOG_INFO("环境: 检测到动态对象与静态对象之间发生碰撞, 碰撞对象ID: {}", results[0].object2Id);
                return true;
            }
        }
    }

    LOG_TRACE("环境: 未检测到动态对象与静态对象之间的碰撞");
    return false;
}

bool Environment::checkDynamicDynamicCollisions(double safety_margin,
                                             const std::unordered_set<ObjectID>& ignored_object_ids) const {
    LOG_DEBUG("环境: 检查动态对象之间的碰撞, 安全距离: {}", safety_margin);

    if (collision_engine_) {
        // 使用碰撞引擎
        CollisionOptions options;
        options.safetyMargin = safety_margin;
        options.ignoredObjectIds = ignored_object_ids;

        // 检查所有动态对象间的碰撞，需要保护数据源访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto movable_objects = object_storage_.getMovableObjects();

        for (size_t i = 0; i < movable_objects.size(); ++i) {
            auto& obj1 = movable_objects[i];
            if (ignored_object_ids.count(obj1->getId()) > 0) continue;

            for (size_t j = i + 1; j < movable_objects.size(); ++j) {
                auto& obj2 = movable_objects[j];
                if (ignored_object_ids.count(obj2->getId()) > 0) continue;

                auto results = collision_engine_->checkObjectAgainstObject(obj1, obj2, options);
                if (!results.empty() && results[0].hasCollision) {
                    LOG_INFO("环境: 检测到动态对象之间发生碰撞，涉及对象ID: {} 和 {}",
                            obj1->getId(), obj2->getId());
                    return true;
                }
            }
        }
    }

    LOG_TRACE("环境: 未检测到动态对象之间的碰撞");
    return false;
}

bool Environment::setParentRelationship(
    const ObjectID& child_id,
    const ObjectID& new_parent_id)
{
    LOG_DEBUG("环境: 设置对象 '{}' 的父对象为 '{}'",
             NSUtils::isValidObjectID(child_id) ? child_id : "None",
             NSUtils::isValidObjectID(new_parent_id) ? new_parent_id : "None");

    // 使用函数注入模式：ThreadSafeCache处理缓存失效逻辑
    return unified_cache_.executeUpdateWithFullInvalidation([this, &child_id, &new_parent_id]() {
        // 数据更新函数：执行实际的关系设置操作，需要保护数据源访问
        std::unique_lock<std::shared_mutex> lock(objects_mutex_);

        // 1. 验证子对象是否存在
        auto child_obj = object_storage_.getObject<EntityObject>(child_id);
        if (!child_obj) {
            LOG_ERROR("环境: 设置父对象关系失败 - 未找到子对象 ID: {}", child_id);
            return false;
        }

        // 2. 获取当前父对象ID
        ObjectID old_parent_id = child_obj->getParentId();

        // 3. 如果新旧父对象相同，则无需操作
        if (old_parent_id == new_parent_id) {
            LOG_DEBUG("环境: 对象 '{}' 的父对象未改变，无需更新", child_id);
            return true;
        }

        // 4. 验证新父对象是否存在（如果指定了有效ID）
        std::shared_ptr<EntityObject> new_parent_obj = nullptr;
        if (NSUtils::isValidObjectID(new_parent_id)) {
            new_parent_obj = object_storage_.getObject<EntityObject>(new_parent_id);
            if (!new_parent_obj) {
                LOG_ERROR("环境: 设置父对象关系失败 - 未找到新父对象 ID: {}", new_parent_id);
                return false;
            }

            // 检查循环引用
            if (this->hasCircularReference(child_obj, new_parent_obj)) {
                LOG_ERROR("环境: 检测到循环引用，无法将 '{}' 设置为 '{}' 的父对象",
                         new_parent_id, child_id);
                return false;
            }
        }

        // 5. 更新子对象的父对象引用
        child_obj->internal_setParent(new_parent_id);

        // 6. 更新旧父对象的子对象列表
        if (NSUtils::isValidObjectID(old_parent_id)) {
            if (auto old_parent = object_storage_.getObject<EntityObject>(old_parent_id)) {
                old_parent->internal_removeChild(child_id);
            }
        }

        // 7. 更新新父对象的子对象列表
        if (new_parent_obj) {
            new_parent_obj->internal_addChild(child_id);
        }

        // 8. 更新关系索引
        EntityStateSnapshot snapshot;
        snapshot.id = child_id;
        snapshot.current_parent_id = new_parent_id;
        snapshot.old_parent_id = old_parent_id;
        updateRelationshipIndexes(snapshot);

        LOG_INFO("环境: 成功更新对象 '{}' 的父对象从 '{}' 到 '{}'",
                child_id,
                NSUtils::isValidObjectID(old_parent_id) ? old_parent_id : "None",
                NSUtils::isValidObjectID(new_parent_id) ? new_parent_id : "None");

        return true;
    });
}



// 注意：checkCollisionWithDynamicObstacles方法已在头文件中声明为接受EcefPoint参数
// 这里不需要重复定义WGS84Point版本的方法

// 注意：第二个checkCollisionWithDynamicObstacles方法也已在头文件中声明为接受EcefPoint参数
// 这里不需要重复定义WGS84Point版本的方法
// --- 地图数据访问 ---
bool Environment::loadMapData() {
    LOG_INFO("环境: 开始加载地图数据");

    if (!global_params_) {
        LOG_ERROR("环境: 全局参数未初始化，无法加载地图数据");
        return false;
    }

    // 检查地图功能是否启用
    auto map_enabled = global_params_->getValueOrDefault<bool>("map.enabled", true);
    if (!map_enabled) {
        LOG_INFO("环境: 地图功能已禁用");
        return true;
    }

    // 获取地图类型（默认为 tiled）
    std::string map_type_str = "tiled";

    // 获取WGS84参考原点（从全局参数中读取）
    std::optional<WGS84Point> common_ref_origin_opt;
    try {
        // 直接读取 WGS84Point 类型的 wgs84_origin 参数
        if (global_params_->hasParam("wgs84_origin")) {
            try {
                // 直接读取 WGS84Point 类型
                // 使用三参数版本的getValueOrDefault，它会自动从ParamRegistry获取参数定义中的缺省值
                WGS84Point backup_default(118.0, 32.0, 0.0); // 备用缺省值 (经度, 纬度, 高度)
                common_ref_origin_opt = global_params_->getValueOrDefault<WGS84Point>("wgs84_origin", backup_default, &param_registry_);
                LOG_INFO("环境: 从全局参数获取WGS84参考原点 {}",
                        common_ref_origin_opt->toString());
            } catch (const std::exception& e) {
                LOG_WARN("环境: 读取 wgs84_origin 参数失败: {}，将使用默认原点", e.what());
            }
        } else {
            LOG_INFO("环境: 未找到 wgs84_origin 参数，将使用默认原点");
        }
    } catch (const std::exception& e) {
        LOG_WARN("环境: 读取 wgs84_origin 参数时发生异常: {}", e.what());
    }

    // 创建瓦片地图并委托给它处理所有配置解析
    LOG_INFO("环境: 创建瓦片地图管理器");
    try {
        auto tiledMap = std::make_shared<TiledGridMap>();

        if (!tiledMap->initialize(global_params_)) {
            LOG_ERROR("环境: 瓦片地图初始化失败");
            mapDataSource_ = nullptr;
            return false;
        }

        mapDataSource_ = std::static_pointer_cast<IGridMap>(tiledMap);
        LOG_INFO("环境: 成功创建瓦片地图");

    } catch (const std::exception& e) {
        LOG_ERROR("环境: 瓦片地图创建或初始化时发生标准异常: {}", e.what());
        mapDataSource_ = nullptr;
        return false;
    } catch (...) {
        LOG_ERROR("环境: 瓦片地图创建或初始化时发生未知异常");
        mapDataSource_ = nullptr;
        return false;
    }

    // 移除旧的geotiff分支，现在统一使用TiledGridMap
    // 旧的SingleGridMap代码已被移除，所有地图类型都通过TiledGridMap处理

    if (mapDataSource_ && mapDataSource_->isInitialized()) {
        // 如果地图数据源成功创建并初始化，则确保坐标系统管理器存在
        if (!coordinate_manager_) {
            LOG_INFO("环境: 地图数据源已加载，但坐标系统管理器尚未设置");

            if (common_ref_origin_opt.has_value()) {
                // 创建 CoordinateManager 并初始化
                coordinate_manager_ = std::make_shared<CoordinateManager>();
                if (!coordinate_manager_->initialize(global_params_)) {
                    LOG_ERROR("环境: 初始化坐标系统管理器失败");
                    return false;
                }

                // 创建或获取默认的全局坐标空间
                std::string space_id = Constants::GLOBAL_TASK_SPACE_ID;
                auto task_space = coordinate_manager_->getOrCreateTaskSpace(common_ref_origin_opt.value(), space_id);
                if (!task_space) {
                    LOG_WARN("环境: 未找到全局坐标空间 '{}'，将在Config加载时创建", space_id);
                } else {
                    LOG_INFO("环境: 成功找到全局坐标空间 '{}'", space_id);
                }
                LOG_INFO("环境: 已使用通用配置的WGS84原点 (纬度: {:.5f}, 经度: {:.5f}, 高度: {:.1f}) 初始化全局坐标转换器",
                        common_ref_origin_opt->latitude, common_ref_origin_opt->longitude,
                        common_ref_origin_opt->altitude);
            } else {
                // 尝试从地图元数据获取参考原点
                auto metadata = mapDataSource_->getMetadata();
                if (metadata.isValid() && !metadata.wgs84_corners.empty()) {
                    // 使用地图中心作为参考原点
                    auto& corners = metadata.wgs84_corners;
                    double center_lat = (corners[0].latitude + corners[2].latitude) / 2.0;
                    double center_lon = (corners[0].longitude + corners[2].longitude) / 2.0;
                    double center_alt = 0.0; // 默认高度

                    WGS84Point map_derived_origin(center_lon, center_lat, center_alt); // WGS84Point构造函数: (经度, 纬度, 高度)

                    // 创建 CoordinateManager 并初始化
                    coordinate_manager_ = std::make_shared<CoordinateManager>();
                    if (!coordinate_manager_->initialize(global_params_)) {
                        LOG_ERROR("环境: 初始化坐标系统管理器失败");
                        return false;
                    }

                    // 查找默认的全局坐标空间
                    std::string space_id = Constants::GLOBAL_TASK_SPACE_ID;
                    auto task_space = coordinate_manager_->getOrCreateTaskSpace(map_derived_origin, space_id);
                    if (!task_space) {
                        LOG_WARN("环境: 未找到全局坐标空间 '{}'，将在Config加载时创建", space_id);
                    } else {
                        LOG_INFO("环境: 成功找到全局坐标空间 '{}'", space_id);
                    }

                    LOG_INFO("环境: 已使用地图数据源派生的中心点 (纬度: {:.5f}, 经度: {:.5f}, 高度: {:.1f}) 初始化自适应坐标系统",
                            center_lat, center_lon, center_alt);
                } else {
                    // 尝试从全局参数中读取 wgs84_origin 配置
                    WGS84Point config_origin;
                    bool has_config_origin = false;

                    try {
                        auto origin_vector_opt = getGlobalParamOrDefault<std::vector<double>>("wgs84_origin", std::vector<double>{});
                        if (origin_vector_opt.size() >= 3) {
                            // 数据格式: [经度, 纬度, 高度]，WGS84Point构造函数现在也是(经度, 纬度, 高度)
                            config_origin = WGS84Point(origin_vector_opt[0], origin_vector_opt[1], origin_vector_opt[2]);
                            has_config_origin = true;
                            LOG_INFO("环境: 从全局参数读取到 wgs84_origin 配置: {}",
                                    config_origin.toString());
                        }
                    } catch (const std::exception& e) {
                        LOG_WARN("环境: 读取 wgs84_origin 参数时发生异常: {}", e.what());
                    }

                    // 如果没有配置，使用合理的默认基准点
                    WGS84Point final_origin = has_config_origin ?
                        config_origin :
                        WGS84Point(116.4074, 39.9042, 50.0); // 北京天安门广场，海拔50米 (经度, 纬度, 高度)

                    if (!has_config_origin) {
                        LOG_WARN("环境: 无法从配置获取 wgs84_origin，将使用默认基准点 ({}) 作为全局原点", final_origin.toString());
                    }

                    // 创建 CoordinateManager 并初始化
                    coordinate_manager_ = std::make_shared<CoordinateManager>();
                    if (!coordinate_manager_->initialize(global_params_)) {
                        LOG_ERROR("环境: 初始化坐标系统管理器失败");
                        return false;
                    }

                    // 查找默认的全局坐标空间
                    std::string space_id = Constants::GLOBAL_TASK_SPACE_ID;
                    auto task_space = coordinate_manager_->getOrCreateTaskSpace(final_origin, space_id);
                    if (!task_space) {
                        LOG_WARN("环境: 未找到全局坐标空间 '{}'，将在Config加载时创建", space_id);
                    } else {
                        LOG_INFO("环境: 成功找到全局坐标空间 '{}'", space_id);
                    }
                }
            }
        } else {
            LOG_INFO("环境: 坐标系统管理器已由外部设置或先前初始化");
        }

        is_map_loaded_ = true;
        return true;
    }

    LOG_ERROR("环境: 地图数据加载步骤结束，但地图数据源仍未成功初始化");
    return false;
}

std::optional<double> Environment::getElevationAtGlobalPoint(
    const WGS84Point& wgs84_point,
    const std::string& layer_name) const {

    // LOG_TRACE("环境: 查询WGS84点 {} 在图层'{}' 的高程",
    //          wgs84_point.toString(), layer_name);

    if (!mapDataSource_) {
        LOG_WARN("环境: 地图数据源未初始化，无法获取高程数据");
        return std::nullopt;
    }

    if (!wgs84_point.isValid()) {
        LOG_WARN("环境: WGS84点无效，无法获取高程数据");
        return std::nullopt;
    }

    auto elevation_opt = mapDataSource_->getElevation(wgs84_point.latitude, wgs84_point.longitude);
    bool success = elevation_opt.has_value();
    double elevation = success ? elevation_opt.value() : 0.0;

    if (success) {
        // LOG_TRACE("环境: WGS84点 ({}) 在图层'{}' 的高程为 {:.2f}m", wgs84_point.toString(), layer_name, elevation);
        return elevation;
    } else {
        LOG_DEBUG("环境: 无法获取WGS84点 ({}) 在图层'{}' 的高程", wgs84_point.toString(), layer_name);
        return std::nullopt;
    }
}

std::optional<double> Environment::getElevationAtGlobalPoint(
    const EcefPoint& ecef_point,
    const std::string& layer_name) const {

    LOG_TRACE("环境: 查询{}在图层'{}' 的高程",
             ecef_point.toString(), layer_name);

    // 转换ECEF坐标为WGS84坐标进行高程查询
    WGS84Point wgs84_point = NSUtils::CoordinateConverter::ecefToWGS84(ecef_point);

    return getElevationAtGlobalPoint(wgs84_point, layer_name);
}

std::pair<FeatureType, bool> Environment::getFeatureType(double latitude, double longitude) const {
    LOG_TRACE("环境: 查询地物类型 - 纬度: {:.6f}, 经度: {:.6f}", latitude, longitude);

    if (!mapDataSource_) {
        LOG_WARN("环境: 地图数据源未初始化，无法获取地物类型");
        return { FeatureType::UNKNOWN, false };
    }

    auto result_opt = mapDataSource_->getFeature(latitude, longitude);

    if (result_opt.has_value()) {
        LOG_TRACE("环境: 位置 (纬度: {:.6f}, 经度: {:.6f}) 的地物类型为 {} (有效)",
                 latitude, longitude, NSUtils::enumToString(result_opt.value()));
        return { result_opt.value(), true };
    } else {
        LOG_TRACE("环境: 位置 (纬度: {:.6f}, 经度: {:.6f}) 的地物类型无效或未找到",
                 latitude, longitude);
        return { FeatureType::UNKNOWN, false };
    }
}

std::pair<double, bool> Environment::getFeatureHeight(double latitude, double longitude) const {
    LOG_TRACE("环境: 查询地物高度 - 纬度: {:.6f}, 经度: {:.6f}", latitude, longitude);

    if (!mapDataSource_) {
        LOG_DEBUG("环境: 地图数据源未初始化，无法获取地物高度");
        return { 0.0, false };
    }

    auto result_opt = mapDataSource_->getFeatureHeight(latitude, longitude);

    if (result_opt.has_value()) {
        LOG_TRACE("环境: 位置 (纬度: {:.6f}, 经度: {:.6f}) 的地物高度为 {:.2f}m (有效)",
                 latitude, longitude, result_opt.value());
        return { result_opt.value(), true };
    } else {
        LOG_TRACE("环境: 位置 (纬度: {:.6f}, 经度: {:.6f}) 的地物高度无效或未找到",
                 latitude, longitude);
        return { 0.0, false };
    }
}

std::pair<double, bool> Environment::getGroundAltitude(const EcefPoint& ecef_point) const {
    LOG_DEBUG("环境: 查询ECEF点 {} 的地面高程", ecef_point.toString());

    try {
        // 转换ECEF坐标为WGS84坐标用于高程查询
        WGS84Point wgs84_point = NSUtils::CoordinateConverter::ecefToWGS84(ecef_point);
        // 使用 getElevationAtGlobalPoint 方法获取高程
        auto elevation = getElevationAtGlobalPoint(wgs84_point);

        if (elevation) {
            LOG_DEBUG("环境: 成功获取ECEF点 {} 的地面高程 {:.2f}m",
                     ecef_point.toString(), *elevation);
            return { *elevation, true };
        } else {
            LOG_DEBUG("环境: 无法获取ECEF点 {} 的地面高程",
                     ecef_point.toString());
            return { 0.0, false };
        }
    } catch (const std::exception& e) {
        LOG_ERROR("环境: 获取地面高度时发生异常: {}", e.what());
        return { 0.0, false };
    } catch (...) {
        LOG_ERROR("环境: 获取地面高度时发生未知异常");
        return { 0.0, false };
    }
}

// --- 循环引用检查 ---
bool Environment::hasCircularReference(const std::shared_ptr<EntityObject>& child,
    const std::shared_ptr<EntityObject>& potential_parent) {

    if (!child || !potential_parent) {
        return false;
    }

    // 如果潜在父对象就是子对象本身，则存在循环引用
    if (child->getId() == potential_parent->getId()) {
        return true;
    }

    // 检查潜在父对象的父对象链，看是否包含子对象
    auto current = potential_parent;
    std::unordered_set<ObjectID> visited;

    while (current) {
        // 防止无限循环
        if (visited.count(current->getId())) {
            break;
        }
        visited.insert(current->getId());

        // 获取当前对象的父对象
        auto parent_obj = object_storage_.getObject(current->getParentId());
        if (!parent_obj) {
            break;
        }

        // 如果父对象是子对象，则存在循环引用
        if (parent_obj->getId() == child->getId()) {
            return true;
        }

        current = parent_obj;
    }

    return false;
}

// --- 缓存管理方法（使用函数注入模式） ---
// 注意：直接的缓存失效方法已被移除，以确保一致使用函数注入模式
// 所有缓存失效操作应通过ThreadSafeCache的executeUpdate系列方法进行

std::string Environment::generateCacheKey(const std::string& query_type,
    const std::vector<std::string>& parameters) const {

    std::string key = query_type;
    for (const auto& param : parameters) {
        // 检查参数中是否包含分隔符，如果包含则转义
        std::string escaped_param = param;
        size_t pos = 0;
        while ((pos = escaped_param.find("||", pos)) != std::string::npos) {
            escaped_param.replace(pos, 2, "\\|\\|");
            pos += 4; // 跳过替换的字符
        }
        key += "||" + escaped_param;
    }
    // 不包含版本号，因为版本号用于整体失效检测
    return key;
}

std::optional<std::vector<ObjectID>> Environment::getCachedObjectIds(const std::string& cache_key) const {
    // 直接使用ThreadSafeCache的get方法，它已经包含了所有必要的逻辑
    return unified_cache_.get(cache_key);
}

void Environment::setCachedObjectIds(const std::string& cache_key,
    const std::vector<ObjectID>& object_ids,
    const std::string& query_type) const {
    // 直接使用ThreadSafeCache的set方法，它已经包含了所有必要的逻辑
    unified_cache_.set(cache_key, object_ids, query_type);
}

bool Environment::validateCacheConsistency(const std::string& cache_key,
    const std::vector<ObjectID>& expected_ids) const {

    auto cached_ids = getCachedObjectIds(cache_key);
    if (!cached_ids.has_value()) {
        LOG_TRACE("环境: 缓存键 '{}' 未命中，无法验证一致性", cache_key);
        return false;
    }

    const auto& cached_vector = cached_ids.value();

    // 检查大小是否一致
    if (cached_vector.size() != expected_ids.size()) {
        LOG_WARN("环境: 缓存一致性验证失败 - 大小不匹配，缓存: {}, 期望: {}",
                cached_vector.size(), expected_ids.size());
        return false;
    }

    // 检查内容是否一致（顺序无关）
    std::unordered_set<ObjectID> cached_set(cached_vector.begin(), cached_vector.end());
    std::unordered_set<ObjectID> expected_set(expected_ids.begin(), expected_ids.end());

    if (cached_set != expected_set) {
        LOG_WARN("环境: 缓存一致性验证失败 - 内容不匹配");
        return false;
    }

    LOG_TRACE("环境: 缓存一致性验证通过，键: '{}'", cache_key);
    return true;
}

std::string Environment::generateSpatialCacheKey(const BoundingBox& region) const {
    // 使用线程安全的格式化，避免浮点数精度问题
    char buffer[256];
    std::snprintf(buffer, sizeof(buffer), "%.2f_%.2f_%.2f_%.2f_%.2f_%.2f",
                 region.min_.x(), region.min_.y(), region.min_.z(),
                 region.max_.x(), region.max_.y(), region.max_.z());
    return std::string(buffer);
}

std::string Environment::generateWGS84RegionCacheKey(const WGS84BoundingBox& region,
    const std::optional<SpatialQueryFilter>& filter) const {
    // 使用线程安全的格式化，避免浮点数精度问题
    char buffer[512];
    std::snprintf(buffer, sizeof(buffer), "wgs84_region_%.6f_%.6f_%.6f_%.6f_%.2f_%.2f",
                 region.minLongitude, region.minLatitude,
                 region.maxLongitude, region.maxLatitude,
                 region.minAltitude, region.maxAltitude);

    std::string key = std::string(buffer);
    if (filter.has_value()) {
        key += "_filtered";
        // 可以根据需要添加更详细的过滤器信息
    }

    return key;
}

std::string Environment::generateWGS84PointCacheKey(const WGS84Point& point, double radius,
    const std::optional<SpatialQueryFilter>& filter) const {
    // 使用线程安全的格式化，避免浮点数精度问题
    char buffer[512];
    std::snprintf(buffer, sizeof(buffer), "wgs84_point_%.6f_%.6f_%.2f_r%.2f",
                 point.longitude, point.latitude, point.altitude, radius);

    std::string key = std::string(buffer);
    if (filter.has_value()) {
        key += "_filtered";
        // 可以根据需要添加更详细的过滤器信息
    }

    return key;
}

std::string Environment::generateEcefPointCacheKey(const EcefPoint& ecef_point) const {
    // 使用线程安全的格式化，避免浮点数精度问题
    char buffer[128];
    std::snprintf(buffer, sizeof(buffer), "%.2f_%.2f_%.2f",
                 ecef_point.x(), ecef_point.y(), ecef_point.z());
    return std::string(buffer);
}

// --- 批量操作方法 ---
size_t Environment::removeObjects(const std::vector<ObjectID>& ids) {
    size_t success_count = 0;
    bool any_success = false;
    std::vector<ObjectID> successfully_removed_ids;

    for (const auto& id : ids) {
        bool removal_success = true;

        // 首先从属性索引中移除
        try {
            attribute_index_.removeAllAttributes(id);
            LOG_DEBUG("环境: 对象 {} 已从属性索引中移除", id);
        } catch (const std::exception& e) {
            LOG_WARN("环境: 从属性索引移除对象 {} 失败: {}", id, e.what());
            removal_success = false;
        }

        // 从空间索引中移除
        if (spatial_index_) {
            try {
                spatial_index_->removeObject(id);
                LOG_DEBUG("环境: 对象 {} 已从空间索引中移除", id);
            } catch (const std::exception& e) {
                LOG_WARN("环境: 从空间索引移除对象 {} 失败: {}", id, e.what());
                removal_success = false;
            }
        }

        // 然后从对象存储中移除
        try {
            if (object_storage_.removeObject(id)) {
                if (removal_success) {
                    ++success_count;
                    any_success = true;
                    successfully_removed_ids.push_back(id);
                    LOG_DEBUG("环境: 对象 {} 完全移除成功", id);
                } else {
                    LOG_WARN("环境: 对象 {} 从对象存储移除成功，但索引移除失败，可能导致不一致", id);
                }
            } else {
                LOG_WARN("环境: 对象 {} 从对象存储移除失败", id);
            }
        } catch (const std::exception& e) {
            LOG_ERROR("环境: 移除对象 {} 时发生异常: {}", id, e.what());
        }
    }

    // 只有在有成功移除的对象时才失效缓存
    if (any_success) {
        // 使用函数注入模式处理缓存失效
        unified_cache_.executeUpdateWithFullInvalidation([&successfully_removed_ids]() {
            // 这里不需要实际的数据更新，只是批量操作的缓存失效
            return true;
        });
        LOG_DEBUG("环境: 批量移除操作完成，成功移除 {} 个对象", successfully_removed_ids.size());
    }

    LOG_INFO("环境: 批量移除 {} 个对象，完全成功 {} 个", ids.size(), success_count);
    return success_count;
}

// --- 优化的碰撞检测方法 ---
bool Environment::checkCollisionOptimized(const WGS84Point& wgs84_point,
    Time time_stamp,
    double safety_margin,
    const std::unordered_set<ObjectID>& ignored_object_ids) const {

    LOG_TRACE("环境: 开始优化碰撞检测，WGS84点: {}",
             wgs84_point.toString());

    // 转换为ECEF坐标进行几何运算
    EcefPoint ecef_point = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_point);

    // 1. 首先检查静态对象碰撞（使用空间索引快速过滤）
    if (spatial_index_ && coordinate_manager_) {
        // 创建查询区域（ECEF点 + 安全边距）
        Vector3D margin_vec = Vector3D::Constant(safety_margin);
        fcl::Vector3d min_pt = ecef_point.toVector3D() - margin_vec;
        fcl::Vector3d max_pt = ecef_point.toVector3D() + margin_vec;
        BoundingBox query_region(min_pt, max_pt);

        // 直接基于WGS84点创建查询区域，避免不必要的ECEF转换
        WGS84BoundingBox wgs84_region;
        wgs84_region.minLongitude = wgs84_point.longitude - safety_margin / 111320.0; // 近似经度度数转换
        wgs84_region.maxLongitude = wgs84_point.longitude + safety_margin / 111320.0;
        wgs84_region.minLatitude = wgs84_point.latitude - safety_margin / 110540.0;   // 近似纬度度数转换
        wgs84_region.maxLatitude = wgs84_point.latitude + safety_margin / 110540.0;
        wgs84_region.minAltitude = wgs84_point.altitude - safety_margin;
        wgs84_region.maxAltitude = wgs84_point.altitude + safety_margin;

        std::vector<ObjectID> nearby_object_ids = spatial_index_->findObjectsInRegion(wgs84_region);

        // 检查附近的静态对象
        auto static_objects = object_storage_.getStaticObjects();
        for (const auto& obj : static_objects) {
            if (ignored_object_ids.count(obj->getId())) {
                continue; // 跳过忽略的对象
            }

            // 检查对象是否在查询区域内
            if (std::find(nearby_object_ids.begin(), nearby_object_ids.end(), obj->getId()) != nearby_object_ids.end()) {
                // 精确碰撞检测 - 使用ECEF坐标系进行几何运算
                if (obj->getShape()) {
                    WGS84Point obj_wgs84_pos = obj->getWGS84Position();
                    EcefPoint obj_ecef_pos = NSUtils::CoordinateConverter::wgs84ToECEF(obj_wgs84_pos);

                    // 使用EntityObject的getBoundingBox方法获取包围盒
                    auto bounds = obj->getBoundingBox();
                    // 扩展包围盒以考虑安全边距
                    fcl::Vector3d safety_offset(safety_margin, safety_margin, safety_margin);
                    bounds.min_ -= safety_offset;
                    bounds.max_ += safety_offset;

                    // 检查点是否在扩展的包围盒内
                    fcl::Vector3d point_vec = ecef_point.toVector3D();
                    bool contains = (point_vec.x() >= bounds.min_.x() && point_vec.x() <= bounds.max_.x() &&
                                    point_vec.y() >= bounds.min_.y() && point_vec.y() <= bounds.max_.y() &&
                                    point_vec.z() >= bounds.min_.z() && point_vec.z() <= bounds.max_.z());
                    if (contains) {
                        LOG_DEBUG("环境: 检测到与静态对象 {} 的碰撞", obj->getId());
                        return true;
                    }
                }
            }
        }
    }

    // 2. 检查动态对象碰撞（考虑时间因素）
    auto movable_objects = object_storage_.getMovableObjects();
    for (const auto& obj : movable_objects) {
        if (ignored_object_ids.count(obj->getId())) {
            continue; // 跳过忽略的对象
        }

        // 预测动态对象在指定时间的WGS84位置，然后转换为ECEF
        auto dt = time_stamp - obj->getTimeStamp();
        WGS84Point predicted_wgs84 = obj->predictWGS84Position(dt);
        EcefPoint predicted_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(predicted_wgs84);
        auto predicted_bounds = obj->predictBoundingBox(dt);

        // 检查预测位置是否与查询点碰撞（使用ECEF坐标）
        fcl::Vector3d point_vec = ecef_point.toVector3D();
        bool predicted_contains = (point_vec.x() >= predicted_bounds.min_.x() && point_vec.x() <= predicted_bounds.max_.x() &&
                                  point_vec.y() >= predicted_bounds.min_.y() && point_vec.y() <= predicted_bounds.max_.y() &&
                                  point_vec.z() >= predicted_bounds.min_.z() && point_vec.z() <= predicted_bounds.max_.z());
        if (predicted_contains) {
            // 更精确的形状碰撞检测
            if (obj->getShape()) {
                // 使用EntityObject的预测包围盒方法
                auto temp_bounds = obj->predictBoundingBox(dt);
                bool temp_contains = (point_vec.x() >= temp_bounds.min_.x() && point_vec.x() <= temp_bounds.max_.x() &&
                                     point_vec.y() >= temp_bounds.min_.y() && point_vec.y() <= temp_bounds.max_.y() &&
                                     point_vec.z() >= temp_bounds.min_.z() && point_vec.z() <= temp_bounds.max_.z());
                if (temp_contains) {
                    LOG_DEBUG("环境: 检测到与动态对象 {} 的碰撞（预测位置）", obj->getId());
                    return true;
                }
            }
        }
    }

    LOG_TRACE("环境: 优化碰撞检测完成，未发现碰撞");
    return false;
}

// --- 新的便捷碰撞检测方法 ---
bool Environment::isPositionSafe(const WGS84Point& wgs84_point, double safety_radius,
    Time time_stamp, const std::unordered_set<ObjectID>& ignored_object_ids) const {

    if (!collision_engine_) {
        LOG_WARN("Environment::isPositionSafe: 碰撞引擎未初始化");
        return false;
    }

    try {

        // 创建球形检测形状
        auto point_shape = std::make_shared<SphereShape>(std::max(safety_radius, 0.001)); // 最小半径

        // 设置碰撞检测选项
        CollisionOptions options;
        options.timePoint = time_stamp;
        options.ignoredObjectIds = ignored_object_ids;
        options.safetyMargin = safety_radius;

        // 转换为ECEF坐标进行内部碰撞检测
        EcefPoint ecef_point = NSUtils::CoordinateConverter::wgs84ToECEF(wgs84_point);

        // 检查与环境的碰撞
        auto results = collision_engine_->checkGeometryAgainstEnvironment(
            *point_shape, ecef_point, Orientation::Identity(), options);

        // 如果没有碰撞结果，则位置安全
        return results.empty();

    } catch (const std::exception& e) {
        LOG_ERROR("Environment::isPositionSafe: 检查位置安全时发生异常: {}", e.what());
        return false;
    }
}

bool Environment::isPathSegmentSafe(const WGS84Point& start_wgs84, const WGS84Point& end_wgs84,
    double safety_radius, Time start_time, Time end_time,
    int num_steps, const std::unordered_set<ObjectID>& ignored_object_ids) const {

    // 检查路径起点和终点
    if (!isPositionSafe(start_wgs84, safety_radius, start_time, ignored_object_ids) ||
        !isPositionSafe(end_wgs84, safety_radius, end_time, ignored_object_ids)) {
        return false;
    }

    // 检查路径上的中间点（直接在WGS84坐标系中插值）
    for (int i = 1; i < num_steps; ++i) {
        double t = static_cast<double>(i) / num_steps;
        // 在WGS84坐标系中进行线性插值
        WGS84Point interpolated_wgs84;
        interpolated_wgs84.longitude = start_wgs84.longitude + t * (end_wgs84.longitude - start_wgs84.longitude);
        interpolated_wgs84.latitude = start_wgs84.latitude + t * (end_wgs84.latitude - start_wgs84.latitude);
        interpolated_wgs84.altitude = start_wgs84.altitude + t * (end_wgs84.altitude - start_wgs84.altitude);
        Time time = start_time + (end_time - start_time) * t;

        if (!isPositionSafe(interpolated_wgs84, safety_radius, time, ignored_object_ids)) {
            return false;
        }
    }

    return true;
}
std::optional<double> Environment::getAdjustedHeightAtPoint(const WGS84Point& point, AltitudeType height_strategy, double reference_height) const {
    LOG_DEBUG("环境: 获取点{} 的调整后高度，策略: {}, 参考高度: {}",
             point.toString(), static_cast<int>(height_strategy), reference_height);

    switch (height_strategy) {
        case AltitudeType::ABSOLUTE_ALTITUDE:
            // 绝对高度，直接返回参考高度
            LOG_DEBUG("环境: 使用绝对高度策略，返回参考高度: {}", reference_height);
            return reference_height;

        case AltitudeType::ABOVE_GROUND_LEVEL:
        {
            // 相对地面高度，需要获取地面高度
            LOG_DEBUG("环境: AGL策略 - 尝试获取点{} 的地面高度", point.toString());

            // 转换为ECEF坐标进行地面高度查询
            EcefPoint ecef_point = NSUtils::CoordinateConverter::wgs84ToECEF(point);
            auto [ground_height, valid] = getGroundAltitude(ecef_point);
            if (!valid) {
                LOG_WARN("环境: 无法获取点{} 的地面高度，可能是地图数据不可用或点超出地图范围", point.toString());

                // 添加更详细的诊断信息
                if (!mapDataSource_) {
                    LOG_WARN("环境: 地图数据源不可用，AGL高度策略降级为绝对高度策略");
                    return reference_height; // 直接返回参考高度作为绝对高度
                } else {
                    LOG_DEBUG("环境: 地图数据源可用，但查询失败 - 可能是坐标转换问题或点超出地图范围");

                    // 尝试获取地图边界信息用于调试
                    auto metadata = mapDataSource_->getMetadata();
                    if (metadata.isValid()) {
                        LOG_DEBUG("环境: 地图WGS84边界: {}", metadata.wgs84_bounds.toString());

                        // 检查WGS84点是否在地图边界内
                        LOG_DEBUG("环境: 查询点的WGS84坐标: {}", point.toString());
                        bool in_bounds = metadata.wgs84_bounds.contains(point.latitude, point.longitude);
                        LOG_DEBUG("环境: 查询点是否在地图边界内: {}", in_bounds ? "是" : "否");
                    }
                }
                return std::nullopt;
            }
            double adjusted_height = ground_height + reference_height;
            LOG_DEBUG("环境: 点{} 的调整后高度: {} (地面高度: {} + 相对高度: {})",
                     point.toString(), adjusted_height, ground_height, reference_height);
            return adjusted_height;
        }

        case AltitudeType::ABOVE_LAUNCH_POINT:
            // 相对起飞点高度，这里简化处理，假设起飞点高度为0
            LOG_DEBUG("环境: 使用相对起飞点高度策略，返回参考高度: {}", reference_height);
            return reference_height;

        default:
            LOG_WARN("环境: 未知的高度策略: {}", static_cast<int>(height_strategy));
            return std::nullopt;
    }
}



// --- 对象查询方法实现 ---
std::vector<std::shared_ptr<const EntityObject>> Environment::getMovableObjects() const {
    // 生成缓存键
    std::string cache_key = generateCacheKey("movable_objects", {});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    auto object_ids = unified_cache_.getOrCompute(cache_key, [this]() {
        // 数据提供函数：执行实际查询并提取ObjectID，需要保护数据源访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto objects = object_storage_.getMovableObjects();
        std::vector<ObjectID> object_ids;
        object_ids.reserve(objects.size());
        for (const auto& obj : objects) {
            object_ids.push_back(obj->getId());
        }
        LOG_DEBUG("环境查询: 获取 {} 个可移动对象（已缓存）", object_ids.size());
        return object_ids;
    }, "object");

    // 根据缓存的ObjectID构建结果，添加对象存在性检查
    std::vector<std::shared_ptr<const EntityObject>> result;
    result.reserve(object_ids.size());
    {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        for (const auto& id : object_ids) {
            auto obj = this->getObjectById<EntityObject>(id);
            if (obj) {
                result.push_back(obj);
            } else {
                // 对象已被删除，记录调试信息但继续处理其他对象
                LOG_DEBUG("环境查询: 缓存中的可移动对象ID {} 对应的对象已不存在", id);
            }
        }
    }
    return result;
}

std::vector<std::shared_ptr<const EntityObject>> Environment::getStaticObjects() const {
    // 生成缓存键
    std::string cache_key = generateCacheKey("static_objects", {});

    // 使用函数注入模式：ThreadSafeCache处理所有缓存逻辑
    auto object_ids = unified_cache_.getOrCompute(cache_key, [this]() {
        // 数据提供函数：执行实际查询并提取ObjectID，需要保护数据源访问
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        auto objects = object_storage_.getStaticObjects();
        std::vector<ObjectID> object_ids;
        object_ids.reserve(objects.size());
        for (const auto& obj : objects) {
            object_ids.push_back(obj->getId());
        }
        LOG_DEBUG("环境查询: 获取 {} 个静态对象（已缓存）", object_ids.size());
        return object_ids;
    }, "object");

    // 根据缓存的ObjectID构建结果，添加对象存在性检查
    std::vector<std::shared_ptr<const EntityObject>> result;
    result.reserve(object_ids.size());
    {
        std::shared_lock<std::shared_mutex> lock(objects_mutex_);
        for (const auto& id : object_ids) {
            auto obj = this->getObjectById<EntityObject>(id);
            if (obj) {
                result.push_back(obj);
            } else {
                // 对象已被删除，记录调试信息但继续处理其他对象
                LOG_DEBUG("环境查询: 缓存中的静态对象ID {} 对应的对象已不存在", id);
            }
        }
    }
    return result;
}

std::shared_ptr<TaskSpace> Environment::getOrCreateTaskSpace(const WGS84Point& origin, std::string& space_id) {
    if (!coordinate_manager_) {
        LOG_ERROR("环境: 坐标系统管理器未初始化，无法查找TaskSpace '{}'", space_id);
        return nullptr;
    }

    // 委托给CoordinateManager处理
    return coordinate_manager_->getOrCreateTaskSpace(origin, space_id);
}



// 显式实例化模板方法
template bool Environment::addObject<EntityObject>(std::shared_ptr<EntityObject> obj);
template bool Environment::addObject<Zone>(std::shared_ptr<Zone> obj);
template bool Environment::addObject<Obstacle>(std::shared_ptr<Obstacle> obj);

} // namespace NSEnvironment
} // namespace NSDrones
