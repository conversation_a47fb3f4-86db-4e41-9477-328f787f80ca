// src/planning/task_planners/task_planner_loiterpoint.cpp
#include "planning/task_planners/loiterpoint_taskplanner.h"
#include "planning/planning_result.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "utils/coordinate_converter.h"
#include <GeographicLib/Geodesic.hpp>
#include <cmath>
#include <vector>
#include <map>
#include <string>
#include <memory>
#include <algorithm>
#include <numeric>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		/** @brief 构造函数 */
		LoiterPointTaskPlanner::LoiterPointTaskPlanner()
			: ITaskPlanner() {}

		// 新增: initialize 方法实现
		bool LoiterPointTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("[LoiterPointTaskPlanner] 开始初始化定点盘旋任务规划器");
			if (params) {
				// 加载盘旋特定参数
				points_per_circle_ = params->getValueOrDefault<int>("loiter.points_per_circle", 36);

				LOG_INFO("[LoiterPointTaskPlanner] 加载参数: 每圈航点数={}", points_per_circle_);
			}
			else {
				LOG_WARN("[LoiterPointTaskPlanner] 参数对象为空，使用默认参数值");
			}

			LOG_DEBUG("[LoiterPointTaskPlanner] 初始化完成");
			return true;
		}

		/**
		 * @brief 规划 LOITER_POINT 任务。
		 */
		PlanningResult LoiterPointTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<ObjectID, NSUav::UavState>& start_states)
		{
			LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}] 开始规划...", task.getId());
			PlanningResult result;
			result.setStatus(true);

			// --- 1. 验证任务类型 ---
			if (task.getType() != TaskType::LOITER_POINT) {
				result.setStatus(false, "内部错误：LoiterPointTaskPlanner 接收到非 LOITER_POINT 类型的任务 (" + NSUtils::enumToString(task.getType()) + ")");
				LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}] 类型错误: {}", task.getId(), result.getMessage());
				return result;
			}

			// --- 2. 检查是否有无人机分配 ---
			if (assigned_uavs.empty()) {
				result.setStatus(false, "无无人机分配给 LoiterPointTask [" + task.getId() + "]");
				LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}

			LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}] 分配给 {} 架无人机...", task.getId(), assigned_uavs.size());

			// --- 3. 获取并验证特定参数 ---
			auto params_ptr = task.getTaskParameters<NSMission::LoiterPointTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取 LoiterPointTask [" + task.getId() + "] 的有效参数结构体。");
				LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			const NSMission::ControlPoint& center_cp = params_ptr->center_point;
			double radius = params_ptr->radius;
			Time duration = params_ptr->duration_seconds;
			bool clockwise = params_ptr->clockwise;

			// 参数基本验证和修正
			if (radius <= Constants::GEOMETRY_EPSILON) {
				LOG_WARN("[LoiterPointTaskPlanner] 任务 [{}] 参数中的盘旋半径 ({:.3f}) 过小或无效，将使用最小允许半径。", task.getId(), radius);
				radius = Constants::GEOMETRY_EPSILON * 10; // 确保半径至少为一个很小的值
			}
			else {
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}] 参数: 盘旋半径={:.2f}m", task.getId(), radius);
			}
			if (duration < 0.0) {
				LOG_WARN("[LoiterPointTaskPlanner] 任务 [{}] 参数中的持续时间 ({:.3f}) 为负，将视为盘旋至少一圈。", task.getId(), duration);
				duration = 0.0; // 至少盘旋一圈
			}
			else {
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}] 参数: 盘旋持续时间={:.2f}s", task.getId(), duration);
			}
			LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}] 参数: 盘旋中心控制点类型={}, 原始WGS84=({}), 方向={}",
				task.getId(), NSUtils::enumToString(center_cp.type), center_cp.position.toString(), clockwise ? "顺时针" : "逆时针");

			// --- 4. 检查依赖项 (从 ITaskPlanner 基类获取) ---
			auto environment = getEnvironment();
			auto path_planner = getPathPlanner();
			if (!environment || !path_planner) {
				result.setStatus(false, "内部错误：规划器依赖项 (环境/路径规划器) 无效。");
				LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}

			// --- 5. 为每个分配的无人机独立规划 ---
			bool overall_success = true; // 跟踪整体规划是否成功
			for (const auto& uav : assigned_uavs) {
				if (!uav) {
					LOG_WARN("[LoiterPointTaskPlanner] 任务 [{}]: 跳过空的无人机指针。", task.getId());
					continue;
				}
				const ObjectID& uav_id = uav->getId();
				LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 开始为无人机 [{}] 规划盘旋路径...", task.getId(), uav_id);

				auto start_state_it = start_states.find(uav_id);
				if (start_state_it == start_states.end()) {
					std::string msg = "缺少无人机 [" + uav_id + "] 的起始状态。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少状态)。", task.getId(), uav_id);
					continue;
				}
				const NSUav::UavState& start_state = start_state_it->second;
				const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
				if (!dynamics) {
					std::string msg = "无人机 [" + uav_id + "] 缺少动力学模型。";
					result.addWarning(WarningEvent{ WarningType::INVALID_STATE, msg, 0.0, {}, uav_id, task.getId() });
					LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少动力学模型)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 起始状态 Pos=({}), Time={:.3f}",
					task.getId(), uav_id, start_state.position.toString(), start_state.time_stamp);

				// --- 5.1 获取盘旋中心绝对位置和高度 ---
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 1: 获取盘旋中心绝对位置...", task.getId(), uav_id);
				std::pair<EcefPoint, bool> center_ecef_res = getAbsolutePosition(center_cp, task); // 使用基类方法
				if (!center_ecef_res.second) {
					std::string msg = "无法确定盘旋任务 [" + task.getId() + "] 中心的绝对位置 (UAV: " + uav_id + ")。";
					LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					result.addWarning(WarningEvent(WarningType::PLANNING_FAILURE, msg, 0.0, WGS84Point(), uav_id, INVALID_OBJECT_ID, task.getId()));
					overall_success = false;
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (无法确定盘旋中心)。", task.getId(), uav_id);
					continue;
				}
				EcefPoint loiter_center_ecef = center_ecef_res.first;
				// 转换为WGS84坐标用于日志输出
				WGS84Point loiter_center_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(loiter_center_ecef);
				double loiter_altitude = loiter_center_wgs84.altitude;
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 盘旋中心绝对位置: {}", task.getId(), uav_id, loiter_center_wgs84.toString());

				// --- 5.2 计算盘旋入口点 --- (圆上距离起始点最近或特定方向的点)
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 2: 计算盘旋圆周入口点...", task.getId(), uav_id);
				// 转换当前位置为ECEF坐标进行几何计算
				EcefPoint start_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(start_state.position);
				Vector3D dir_from_center = start_ecef - loiter_center_ecef;
				dir_from_center.z() = 0; // 仅考虑水平方向
				EcefPoint entry_ecef;
				if (dir_from_center.norm() < Constants::GEOMETRY_EPSILON) {
					// 如果起始点就在中心，选择一个固定方向作为入口 (例如 +X 方向)
					entry_ecef = EcefPoint(loiter_center_ecef.x() + radius, loiter_center_ecef.y(), loiter_center_ecef.z());
					LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 起始点接近盘旋中心，选择默认入口点 (+X方向)。", task.getId(), uav_id);
				}
				else {
					// 入口点为圆心指向起始点的方向上，距离圆心为半径的点
					Vector3D entry_offset = dir_from_center.normalized() * radius;
					entry_ecef = EcefPoint(loiter_center_ecef.toVector3D() + entry_offset);
					entry_ecef = EcefPoint(entry_ecef.x(), entry_ecef.y(), loiter_center_ecef.z()); // 确保高度正确
				}
				// 转换为WGS84坐标用于路径规划和日志输出
				WGS84Point entry_wgs84 = NSUtils::CoordinateConverter::ecefToWGS84(entry_ecef);
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 计算盘旋入口点: {}", task.getId(), uav_id, entry_wgs84.toString());

				// --- 5.3 规划进入路径 (从 start_state 到 entry_point_on_circle) ---
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 3: 规划进入盘旋区的路径...", task.getId(), uav_id);
				std::vector<EcefPoint> entry_geom_path;
				auto path_constraints_opt = task.getStrategyByType<NSMission::PathConstraintStrategy>();
				const NSMission::PathConstraintStrategy* path_constraints_ptr = path_constraints_opt ? path_constraints_opt.get() : nullptr;
				// 使用之前已经计算的ECEF坐标
				// start_ecef 和 entry_ecef 已在前面计算过
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 调用路径规划器: 从 {} 到 {}",
					task.getId(), uav_id, start_state.position.toString(), entry_wgs84.toString());
				bool entry_found = path_planner->findPath(start_ecef, entry_ecef, dynamics, path_constraints_ptr, entry_geom_path);
				if (!entry_found || entry_geom_path.size() < 2) {
					std::string msg = "未能找到无人机 [" + uav_id + "] 进入盘旋区的路径。";
					LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, start_state.time_stamp, start_state.position, uav_id, task.getId() });
					overall_success = false;
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (无法规划进入路径)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 进入路径找到 {} 个点。", task.getId(), uav_id, entry_geom_path.size());

				// --- 5.4 生成盘旋部分的几何路径 --- (生成一个或多个完整圆周)
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 4: 生成盘旋圆周几何路径...", task.getId(), uav_id);
				std::vector<WGS84Point> single_loiter_geom_path = genLoiterWaypoints(loiter_center_wgs84, radius, loiter_altitude, points_per_circle_, clockwise); // 调用本类私有方法生成一圈的点
				if (single_loiter_geom_path.empty()) {
					std::string msg = "内部错误：生成盘旋圆周几何路径失败 (UAV: " + uav_id + ")。";
					result.setStatus(false, msg); // 这是一个内部错误，直接设置失败
					LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false;
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (内部错误：盘旋路径生成)。", task.getId(), uav_id);
					continue;
				}
				// 计算需要的圈数
				double desired_speed = task.getDesiredSpeed(8.0); // 获取任务期望速度，提供默认值
				double circumference = Constants::TWO_PI * radius;
				double time_per_loop = (desired_speed > Constants::VELOCITY_EPSILON) ? (circumference / desired_speed) : Constants::INF;
				int num_loops = 1; // 至少盘旋一圈
				if (time_per_loop > Constants::TIME_EPSILON && std::isfinite(time_per_loop) && duration > Constants::TIME_EPSILON) {
					num_loops = static_cast<int>(std::ceil(duration / time_per_loop)); // 向上取整确保满足时间要求
				}
				num_loops = std::max(1, num_loops); // 再次确保至少一圈
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 圆周长={:.1f}m, 期望速度={:.1f}m/s, 估算每圈时间={:.1f}s, 要求总持续时间={:.1f}s, 计算需盘旋 {} 圈",
					task.getId(), uav_id, circumference, desired_speed, time_per_loop, duration, num_loops);

				// --- 5.5 合并几何路径 (进入路径 + 盘旋路径 * N圈) ---
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 5: 合并几何路径...", task.getId(), uav_id);
				// 转换ECEF路径为WGS84路径
				std::vector<WGS84Point> full_geom_path;
				full_geom_path.reserve(entry_geom_path.size());
				for (const auto& ecef_pt : entry_geom_path) {
					full_geom_path.push_back(NSUtils::CoordinateConverter::ecefToWGS84(ecef_pt));
				}
				// 检查入口路径终点是否与盘旋路径起点重合，如果是，移除入口路径的终点以避免重复
				if (!full_geom_path.empty() && !single_loiter_geom_path.empty() && (full_geom_path.back() - single_loiter_geom_path.front()).norm() < Constants::GEOMETRY_EPSILON) {
					LOG_TRACE("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 合并几何路径：移除入口路径终点与盘旋路径起点的重合点。", task.getId(), uav_id);
					full_geom_path.pop_back();
				}
				// 添加N圈盘旋路径
				for (int i = 0; i < num_loops; ++i) {
					full_geom_path.insert(full_geom_path.end(), single_loiter_geom_path.begin(), single_loiter_geom_path.end());
				}
				// 如果盘旋了至少一圈，并且路径点数大于一圈的点数，移除最后一个点（因为它与盘旋起点重合）
				// 这样使得最终的几何路径是一个开放路径，终点不与起点重合，便于后续平滑处理
				if (num_loops > 0 && full_geom_path.size() > single_loiter_geom_path.size()) {
					LOG_TRACE("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 合并几何路径：移除最终盘旋路径的闭合点。", task.getId(), uav_id);
					full_geom_path.pop_back();
				}
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 生成完整几何路径，共 {} 个点。", task.getId(), uav_id, full_geom_path.size());

				// --- 5.6 平滑和时间参数化 --- (对完整的 进入+盘旋 路径进行处理)
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 6: 平滑和时间参数化完整路径...", task.getId(), uav_id);
				RouteSegment final_segment;
				if (full_geom_path.size() < 2) {
					std::string msg = "合并后的几何路径点数不足 (UAV: " + uav_id + ")，无法进行平滑和参数化。";
					LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, start_state.time_stamp, start_state.position, uav_id, task.getId() });
					overall_success = false;
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (几何路径点数不足)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 开始平滑和时间参数化, 速度 {:.1f} m/s, {} 个几何点...",
					task.getId(), uav_id, desired_speed, full_geom_path.size());
				if (!smoothAndTimeParameterize(full_geom_path, uav, start_state, desired_speed, final_segment, &result, task.getStrategies())) {
					// smoothAndTimeParameterize 内部会记录详细错误
					LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}] 盘旋路径处理失败 (平滑/参数化过程出错)。", task.getId(), uav_id);
					overall_success = false;
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (平滑/参数化过程出错)。", task.getId(), uav_id);
					continue;
				}
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 平滑和时间参数化完成，生成 {} 个航点。", task.getId(), uav_id, final_segment.size());

				// --- 5.7 添加最终结果 ---
				LOG_DEBUG("[LoiterPointTaskPlanner] 任务 [{}], 无人机 [{}]: 步骤 7: 添加最终航线到结果...", task.getId(), uav_id);
				if (final_segment.empty()) {
					std::string msg = "无人机 [" + uav_id + "] 规划流程完成，但最终生成的航段为空。";
					LOG_WARN("[LoiterPointTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					result.addWarning(WarningEvent{ WarningType::PLANNING_FAILURE, msg, 0.0, {}, uav_id, task.getId() });
					// 这种情况不标记 overall_success 为 false，但记录警告
				}
				else {
					PlannedRoute temp_route_for_warning(uav_id);
					temp_route_for_warning.addWaypoints(final_segment);
					checkSegmentWarnings(temp_route_for_warning, uav_id, result, task.getId()); // 检查航段警告
					PlannedRoute route(uav_id);
					route.addWaypoints(final_segment);
					result.addRoute(std::move(route));
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}]: 为无人机 [{}] 生成盘旋航线成功，{} 个点。", task.getId(), uav_id, final_segment.size());
				}
			}

			// --- 6. 设置最终状态 ---
			if (!overall_success) {
				if (result.wasSuccessful()) {
					result.setStatus(false, "部分或全部无人机的盘旋任务规划失败。");
				}
				LOG_ERROR("[LoiterPointTaskPlanner] 任务 [{}] 规划存在失败情况。", task.getId());
			}
			else {
				LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}] 所有分配的无人机规划流程完成。", task.getId());
				if (result.getAllRoutes().empty() && !assigned_uavs.empty()) {
					LOG_WARN("[LoiterPointTaskPlanner] 任务 [{}]: 规划流程成功完成，但最终未生成任何有效航线。", task.getId());
				}
				else if (result.getAllRoutes().size() != assigned_uavs.size()) {
					LOG_WARN("[LoiterPointTaskPlanner] 任务 [{}]: 最终生成的航线数量 ({}) 与成功完成规划的无人机数量 ({}) 不完全匹配。", task.getId(), result.getAllRoutes().size(), assigned_uavs.size());
				}
				else {
					LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}] 规划成功完成，为所有 {} 架无人机生成了航线。", task.getId(), result.getAllRoutes().size());
				}
			}
			LOG_INFO("[LoiterPointTaskPlanner] 任务 [{}] 规划结束，最终状态: {}", task.getId(), result.wasSuccessful() ? "成功" : "失败");
			return result;
		}

		// --- 生成盘旋几何路径点 (保持为本类私有辅助方法) ---
		/**
		 * @brief 生成一个完整圆周的几何路径点（不闭合，即起点不等于终点）。
		 *
		 * @param center 圆心位置 (XY平面)。
		 * @param radius 半径。
		 * @param altitude 高度 (Z坐标)。
		 * @param num_loiter_points 一个圆周使用的离散点数量。
		 * @param clockwise 是否顺时针。
		 * @return std::vector<WGS84Point> 包含圆周上点的列表。
		 */
		std::vector<WGS84Point> LoiterPointTaskPlanner::genLoiterWaypoints(const WGS84Point& center, double radius, double altitude, int num_loiter_points, bool clockwise) const
		{
			std::vector<WGS84Point> waypoints;
			radius = std::max(Constants::GEOMETRY_EPSILON * 10, radius); // 再次确保半径有效
			num_loiter_points = std::max(3, num_loiter_points); // 至少需要3个点才能形成一个圆

			LOG_TRACE("[LoiterPointTaskPlanner] 开始生成盘旋几何路径: 中心=({:.6f},{:.6f}), R={:.1f}, 高度={:.1f}, 点数={}, 方向={}",
				center.longitude, center.latitude, radius, altitude, num_loiter_points, clockwise ? "顺时针" : "逆时针");
			waypoints.reserve(num_loiter_points);

			double angle_step = Constants::TWO_PI / num_loiter_points;
			// 注意：起始角度的选择会影响入口点与第一个盘旋点的衔接。
			// 为简化，这里总是从 0 度（+X轴方向）开始生成点。
			// 更优化的方法可以根据入口点计算起始角度，使得入口路径的切线方向与盘旋路径的切线方向尽可能一致。
			double start_angle = 0.0;

			// 使用GeographicLib进行精确的地理计算
			const GeographicLib::Geodesic& geod = GeographicLib::Geodesic::WGS84();

			for (int i = 0; i < num_loiter_points; ++i) { // 生成 num_loiter_points 个点
				// 计算当前点的角度
				// 顺时针角度减小，逆时针角度增加
				double angle = start_angle + (clockwise ? -1.0 : 1.0) * i * angle_step;
				// 将角度转换为方位角（度）
				double bearing_deg = angle * 180.0 / Constants::PI;

				// 使用GeographicLib计算目标点
				double dest_lat, dest_lon;
				geod.Direct(center.latitude, center.longitude, bearing_deg, radius, dest_lat, dest_lon);

				// 添加到航路点列表
				waypoints.emplace_back(dest_lon, dest_lat, altitude);
			}
			// 返回的路径点不包含重复的起点，即 waypoints[0] != waypoints.back()
			LOG_TRACE("[LoiterPointTaskPlanner] 生成盘旋几何路径完成，共 {} 个点。", waypoints.size());
			return waypoints;
		}

	} // namespace NSPlanning
} // namespace NSDrones