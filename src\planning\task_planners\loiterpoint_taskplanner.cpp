// src/planning/task_planners/task_planner_loiterpoint.cpp
#include "planning/task_planners/loiterpoint_taskplanner.h"
#include "planning/planning_result.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "mission/control_point.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "utils/coordinate_converter.h"
#include <GeographicLib/Geodesic.hpp>
#include <cmath>
#include <vector>
#include <map>
#include <string>
#include <memory>
#include <algorithm>
#include <numeric>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		LoiterPointTaskPlanner::LoiterPointTaskPlanner()
			: ITaskPlanner() {}

		bool LoiterPointTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params,
												const nlohmann::json& raw_config) {
			LOG_DEBUG("盘旋任务规划器: 开始初始化");

			if (params) {
				// 加载盘旋相关参数
				default_points_per_circle_ = params->getValueOrDefault<int>("loiter.points_per_circle", 36);
				min_loiter_radius_ = params->getValueOrDefault<double>("loiter.min_radius", 10.0);

				LOG_INFO("盘旋任务规划器: 参数加载完成 - 每圈航点数:{}, 最小半径:{:.1f}m",
						default_points_per_circle_, min_loiter_radius_);
			} else {
				LOG_WARN("盘旋任务规划器: 参数对象为空，使用默认值");
			}

			LOG_DEBUG("盘旋任务规划器: 初始化完成");
			return true;
		}

		PlanningResult LoiterPointTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<NSUtils::ObjectID, NSUav::UavState>& start_states) {

			LOG_INFO("盘旋任务规划器: 开始规划任务[{}]", task.getId());
			PlanningResult result;
			result.setStatus(true);

			// 1. 基本验证
			if (!validateTaskBasics(task, NSMission::TaskType::LOITER_POINT, assigned_uavs, result)) {
				return result;
			}

			// 2. 获取和验证盘旋参数
			auto params_ptr = task.getTaskParameters<NSMission::LoiterPointTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取盘旋任务参数");
				LOG_ERROR("盘旋任务规划器: 任务[{}]参数获取失败", task.getId());
				return result;
			}

			if (!validateLoiterParams(*params_ptr, task.getId(), result)) {
				return result;
			}

			// 获取盘旋中心绝对位置
			auto center_result = getAbsolutePosition(params_ptr->center_point, task);
			if (!center_result.second) {
				result.setStatus(false, "无法确定盘旋中心的绝对位置");
				LOG_ERROR("盘旋任务规划器: 任务[{}]盘旋中心位置解析失败", task.getId());
				return result;
			}

			NSCore::WGS84Point loiter_center = NSUtils::CoordinateConverter::ecefToWGS84(center_result.first);
			double radius = std::max(params_ptr->radius, min_loiter_radius_);
			double duration = std::max(params_ptr->duration_seconds, 0.0);
			bool clockwise = params_ptr->clockwise;

			LOG_DEBUG("盘旋任务规划器: 任务[{}]盘旋参数 - 中心:{}, 半径:{:.1f}m, 持续时间:{:.1f}s, 方向:{}",
					 task.getId(), loiter_center.toString(), radius, duration,
					 clockwise ? "顺时针" : "逆时针");

			// 3. 为每个无人机规划路径
			bool overall_success = true;
			for (const auto& uav : assigned_uavs) {
				if (!validateUavState(uav, start_states, task.getId(), result)) {
					overall_success = false;
					continue;
				}

				const NSUtils::ObjectID& uav_id = uav->getId();
				const NSUav::UavState& start_state = start_states.at(uav_id);

				LOG_INFO("盘旋任务规划器: 为无人机[{}]规划盘旋路径", uav_id);

				if (!planLoiterPathForUav(uav, loiter_center, radius, duration, clockwise,
										 start_state, task, result)) {
					overall_success = false;
					continue;
				}
			}


			// 4. 设置最终状态
			if (!overall_success && result.wasSuccessful()) {
				result.setStatus(false, "部分无人机盘旋路径规划失败");
			}

			LOG_INFO("盘旋任务规划器: 任务[{}]规划完成，状态:{}, 生成{}条航线",
					task.getId(), result.wasSuccessful() ? "成功" : "失败", result.getAllRoutes().size());
			return result;
		}

		// === 私有辅助方法实现 ===

		bool LoiterPointTaskPlanner::validateLoiterParams(const NSMission::LoiterPointTaskParams& params,
														  const NSUtils::ObjectID& task_id,
														  PlanningResult& result) const {
			// 验证盘旋半径
			if (params.radius < min_loiter_radius_) {
				std::string error_msg = "盘旋半径(" + std::to_string(params.radius) +
									   ")小于最小值(" + std::to_string(min_loiter_radius_) + ")";
				result.setStatus(false, error_msg);
				LOG_ERROR("盘旋任务规划器: 任务[{}] {}", task_id, error_msg);
				return false;
			}

			// 验证持续时间
			if (params.duration_seconds < 0.0) {
				std::string error_msg = "盘旋持续时间(" + std::to_string(params.duration_seconds) + ")不能为负值";
				result.setStatus(false, error_msg);
				LOG_ERROR("盘旋任务规划器: 任务[{}] {}", task_id, error_msg);
				return false;
			}

			LOG_DEBUG("盘旋任务规划器: 任务[{}]参数验证通过 - 半径:{:.1f}m, 持续时间:{:.1f}s, 方向:{}",
					 task_id, params.radius, params.duration_seconds,
					 params.clockwise ? "顺时针" : "逆时针");
			return true;
		}

		bool LoiterPointTaskPlanner::planLoiterPathForUav(const NSUav::UavPtr& uav,
														  const NSCore::WGS84Point& loiter_center,
														  double radius,
														  double duration,
														  bool clockwise,
														  const NSUav::UavState& start_state,
														  const NSMission::Task& task,
														  PlanningResult& result) const {
			const NSUtils::ObjectID& uav_id = uav->getId();

			// 生成盘旋路径点
			std::vector<NSCore::WGS84Point> loiter_waypoints = generateLoiterWaypoints(
				loiter_center, radius, default_points_per_circle_, clockwise);

			if (loiter_waypoints.empty()) {
				LOG_ERROR("盘旋任务规划器: 无人机[{}]盘旋路径点生成失败", uav_id);
				return false;
			}

			// 计算需要的圈数
			double desired_speed = task.getDesiredSpeed(8.0);
			double circumference = NSCore::Constants::TWO_PI * radius;
			double time_per_loop = circumference / std::max(desired_speed, NSCore::Constants::VELOCITY_EPSILON);
			int num_loops = std::max(1, static_cast<int>(std::ceil(duration / time_per_loop)));

			// 规划进入盘旋起点的路径
			std::vector<NSCore::EcefPoint> entry_path = planPathToTarget(start_state, loiter_waypoints.front(), uav, task, result);
			if (entry_path.empty()) {
				LOG_ERROR("盘旋任务规划器: 无人机[{}]无法规划进入盘旋起点的路径", uav_id);
				return false;
			}

			// 构建完整路径
			std::vector<NSCore::WGS84Point> full_path;

			// 添加进入路径
			for (const auto& ecef_pt : entry_path) {
				full_path.push_back(NSUtils::CoordinateConverter::ecefToWGS84(ecef_pt));
			}

			// 移除可能的重合点
			if (!full_path.empty() && !loiter_waypoints.empty()) {
				if ((full_path.back() - loiter_waypoints.front()).norm() < NSCore::Constants::GEOMETRY_EPSILON) {
					full_path.pop_back();
				}
			}

			// 添加多圈盘旋路径
			for (int i = 0; i < num_loops; ++i) {
				full_path.insert(full_path.end(), loiter_waypoints.begin(), loiter_waypoints.end());
			}

			// 平滑和时间参数化
			RouteSegment final_segment;
			if (!smoothAndTimeParameterize(full_path, uav, start_state, desired_speed,
										  final_segment, &result, task.getStrategies())) {
				LOG_ERROR("盘旋任务规划器: 无人机[{}]路径平滑和时间参数化失败", uav_id);
				return false;
			}

			// 添加到结果
			if (final_segment.empty()) {
				LOG_WARN("盘旋任务规划器: 无人机[{}]生成的航段为空", uav_id);
				return false;
			}

			PlannedRoute route(uav_id);
			route.addWaypoints(final_segment);
			checkSegmentWarnings(route, uav_id, result, task.getId());
			result.addRoute(std::move(route));

			LOG_INFO("盘旋任务规划器: 无人机[{}]盘旋路径规划成功，生成{}个航点，盘旋{}圈",
					uav_id, final_segment.size(), num_loops);
			return true;
		}

		std::vector<NSCore::WGS84Point> LoiterPointTaskPlanner::generateLoiterWaypoints(
			const NSCore::WGS84Point& center,
			double radius,
			int num_points,
			bool clockwise) const {

			auto geometry_manager = getEnvironment()->getGeometryManager();
			if (!geometry_manager) {
				LOG_ERROR("盘旋任务规划器: 几何管理器未初始化");
				return {};
			}

			return geometry_manager->generateCircularPath(center, radius, num_points, clockwise);
		}

	} // namespace NSPlanning
} // namespace NSDrones