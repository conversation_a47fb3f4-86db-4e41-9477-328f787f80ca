// src/planning/task_planners/task_planner_followpath.cpp
#include "planning/task_planners/followpath_taskplanner.h"
#include "planning/planning_result.h"
#include "planning/planning_types.h"
#include "mission/task.h"
#include "mission/task_params.h"
#include "mission/task_strategies.h"
#include "uav/uav.h"
#include "environment/environment.h"
#include "algorithm/path_planner/ipath_planner.h"
#include "algorithm/trajectory_optimizer/itrajectory_optimizer.h"
#include "utils/logging.h"
#include "core/types.h"
#include "params/parameters.h"
#include "params/paramregistry.h"
#include <memory>
#include <algorithm>
#include <numeric>
#include <vector>
#include <map>
#include <string>
#include <utility>

namespace NSDrones {
	namespace NSPlanning {

		/** @brief 构造函数 */
		FollowPathTaskPlanner::FollowPathTaskPlanner()
			: ITaskPlanner() {}

		// 初始化方法实现
		bool FollowPathTaskPlanner::initialize(std::shared_ptr<NSParams::ParamValues> params, const nlohmann::json& raw_config) {
			LOG_DEBUG("[FollowPathTaskPlanner] 开始初始化路径跟随任务规划器");

			if (params) {
				// 加载路径跟随特定参数
				follow_speed_ = params->getValueOrDefault<double>("follow.speed", 15.0);
				path_tolerance_ = params->getValueOrDefault<double>("follow.path_tolerance", 2.0);
				waypoint_tolerance_ = params->getValueOrDefault<double>("follow.waypoint_tolerance", 1.0);
				smooth_path_ = params->getValueOrDefault<bool>("follow.smooth_path", true);

				LOG_INFO("[FollowPathTaskPlanner] 加载参数: 跟随速度={:.1f}m/s, 路径容差={:.1f}m, 航点容差={:.1f}m, 平滑路径={}",
						follow_speed_, path_tolerance_, waypoint_tolerance_, smooth_path_ ? "是" : "否");
			}

			// 从原始配置中加载额外参数
			if (!raw_config.empty()) {
				if (raw_config.contains("interpolation_method")) {
					interpolation_method_ = raw_config["interpolation_method"].get<std::string>();
					LOG_DEBUG("[FollowPathTaskPlanner] 插值方法: {}", interpolation_method_);
				}
			}

			LOG_DEBUG("[FollowPathTaskPlanner] 初始化完成");
			return true;
		}

		/**
		 * @brief 规划 FOLLOW_PATH 任务。
		 */
		PlanningResult FollowPathTaskPlanner::planTask(
			const NSMission::Task& task,
			const std::vector<NSUav::UavPtr>& assigned_uavs,
			const std::map<ObjectID, NSUav::UavState>& start_states)
		{
			LOG_INFO("[FollowPathTaskPlanner] 任务 [{}] 开始规划...", task.getId());
			PlanningResult result;
			result.setStatus(true);

			// --- 1. 验证任务类型和参数 ---
			if (task.getType() != TaskType::FOLLOW_PATH) {
				result.setStatus(false, "内部错误：FollowPathTaskPlanner 接收到非 FOLLOW_PATH 类型的任务 (" + NSUtils::enumToString(task.getType()) + ")");
				LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}] 类型错误: {}", task.getId(), result.getMessage());
				return result;
			}
			if (assigned_uavs.empty()) {
				result.setStatus(false, "无无人机分配给 FollowPathTask [" + task.getId() + "]");
				LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			auto params_ptr = task.getTaskParameters<NSMission::FollowPathTaskParams>();
			if (!params_ptr) {
				result.setStatus(false, "无法获取 FollowPathTask [" + task.getId() + "] 的有效参数结构体。");
				LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}
			if (params_ptr->waypoints.empty()) {
				result.setStatus(false, "FollowPath 任务 [" + task.getId() + "] 的路径点列表为空。");
				LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}

			LOG_INFO("[FollowPathTaskPlanner] 任务 [{}] 参数: {} 个路径点, 重复 {} 次, 分配给 {} 架无人机...",
				task.getId(), params_ptr->waypoints.size(), params_ptr->repeat_count, assigned_uavs.size());

			// --- 2. 检查依赖项 (从 ITaskPlanner 基类获取) ---
			auto environment = getEnvironment();
			auto path_planner = getPathPlanner();
			if (!environment || !path_planner) {
				result.setStatus(false, "内部错误：规划器依赖项 (环境/路径规划器) 无效。");
				LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}] {}", task.getId(), result.getMessage());
				return result;
			}

			// --- 3. 为每个分配的无人机独立规划 ---
			bool overall_success = true; // 跟踪整体规划是否成功
			for (const auto& uav : assigned_uavs) {
				if (!uav) {
					LOG_WARN("[FollowPathTaskPlanner] 任务 [{}]: 跳过空的无人机指针。", task.getId());
					continue;
				}
				const ObjectID& uav_id = uav->getId();
				LOG_INFO("[FollowPathTaskPlanner] 任务 [{}]: 开始为无人机 [{}] 规划路径...", task.getId(), uav_id);

				// 修正：在此处声明和初始化 planned_route
				PlannedRoute planned_route(uav_id);

				auto start_state_it = start_states.find(uav_id);
				if (start_state_it == start_states.end()) {
					std::string msg = "缺少无人机 [" + uav_id + "] 的起始状态。";
					result.addWarning({ WarningType::INVALID_STATE, msg, 0, {}, uav_id, task.getId() });
					LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false; // 标记失败
					LOG_INFO("[FollowPathTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少状态)。", task.getId(), uav_id);
					continue; // 处理下一个无人机
				}
				const NSUav::UavState& start_state = start_state_it->second;
				const NSUav::IDynamicModel* dynamics = uav->getDynamicsModel().get();
				if (!dynamics) {
					std::string msg = "无人机 [" + uav_id + "] 缺少动力学模型。";
					result.addWarning({ WarningType::INVALID_STATE, msg, 0, {}, uav_id, task.getId() });
					LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
					overall_success = false; // 标记失败
					LOG_INFO("[FollowPathTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (缺少动力学模型)。", task.getId(), uav_id);
					continue; // 处理下一个无人机
				}

				LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 起始状态 Pos=({}), Time={:.3f}",
					task.getId(), uav_id, start_state.position.toString(), start_state.time_stamp);

				// --- 3.1 获取参数 ---
				const NSMission::ControlPointList& control_points_orig = params_ptr->waypoints;
				int repeat = std::max(1, params_ptr->repeat_count); // 至少执行一次
				bool reverse_on_repeat = params_ptr->reverse_on_repeat;

				// --- 3.2 循环规划重复次数 ---
				RouteSegment total_route_segment; // 存储当前无人机的完整航线
				NSUav::UavState last_wp_state = start_state; // 从实际起始状态开始
				bool uav_plan_success = true; // 跟踪当前无人机的规划是否成功

				for (int rep = 0; rep < repeat; ++rep) {
					LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 开始规划路径重复次数 {}/{}...", task.getId(), uav_id, rep + 1, repeat);
					// 确定本次重复的路径点顺序
					NSMission::ControlPointList points_this_rep = control_points_orig;
					if (rep > 0 && reverse_on_repeat && (rep % 2 == 1)) { // 第 2, 4, 6... 次重复且需要反向
						std::reverse(points_this_rep.begin(), points_this_rep.end());
						LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 本次重复 ({}): 使用反向路径点。", task.getId(), uav_id, rep + 1);
					}
					else {
						LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 本次重复 ({}): 使用正向路径点。", task.getId(), uav_id, rep + 1);
					}

					// --- 3.3 依次规划到每个控制点 ---
					for (size_t i = 0; i < points_this_rep.size(); ++i) {
						const NSMission::ControlPoint& cp = points_this_rep[i];
						LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 重复 {}/{}, 规划到控制点 #{} (类型={}, 原始WGS84=({}))",
							task.getId(), uav_id, rep + 1, repeat, i, NSUtils::enumToString(cp.type), cp.position.toString());

						// 获取目标点的绝对ECEF位置
						std::pair<EcefPoint, bool> target_ecef_res = getAbsolutePosition(cp, task); // 使用基类方法获取绝对位置
						if (!target_ecef_res.second) {
							std::string msg = "无法确定路径点 #" + std::to_string(i) + " (UAV: " + uav_id + ", 重复次数 " + std::to_string(rep + 1) + ") 的绝对位置。";
							LOG_WARN("[FollowPathTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
							result.addWarning({ WarningType::PLANNING_FAILURE, msg, last_wp_state.time_stamp, last_wp_state.position, uav_id, task.getId() });
							uav_plan_success = false; // 标记当前 UAV 失败
							LOG_INFO("[FollowPathTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (无法确定目标点绝对位置)。", task.getId(), uav_id);
							goto next_uav; // 跳到下一个无人机
						}
						EcefPoint target_ecef = target_ecef_res.first;

						// 规划几何路径（使用ECEF坐标进行内部计算）
						std::vector<EcefPoint> geometric_path;
						auto path_constraints_opt = task.getStrategyByType<NSMission::PathConstraintStrategy>(); // 从 Task 获取策略
						const NSMission::PathConstraintStrategy* path_constraints_ptr = path_constraints_opt ? path_constraints_opt.get() : nullptr;

						// 转换当前位置和目标位置为ECEF坐标进行几何计算
						EcefPoint current_ecef = NSUtils::CoordinateConverter::wgs84ToECEF(last_wp_state.position);
						bool path_found = path_planner->findPath(current_ecef, target_ecef, dynamics, path_constraints_ptr, geometric_path);
						if (!path_found || geometric_path.size() < 2) {
							std::string msg = "未能找到无人机 [" + uav_id + "] 前往路径点 #" + std::to_string(i) + " (重复次数 " + std::to_string(rep + 1) + ") 的路径。";
							LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}]: {}", task.getId(), msg);
							result.addWarning({ WarningType::PLANNING_FAILURE, msg, last_wp_state.time_stamp, last_wp_state.position, uav_id, task.getId() });
							uav_plan_success = false; // 标记当前 UAV 失败
							LOG_INFO("[FollowPathTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (路径规划器未找到路径)。", task.getId(), uav_id);
							goto next_uav; // 跳到下一个无人机
						}
						LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 几何路径找到 {} 个点。", task.getId(), uav_id, geometric_path.size());

						// 平滑和时间参数化
						RouteSegment segment_to_add;
						// 获取控制点速度或任务默认速度
						double segment_speed = cp.hasSpeedRequirement() ? cp.getRequiredSpeedOrZero().norm() : task.getDesiredSpeed();
						if (segment_speed < Constants::VELOCITY_EPSILON) {
							segment_speed = 5.0; // 默认速度，防止速度为0
							LOG_TRACE("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 段 #{} (重复 {}) 速度要求为零或无效，使用默认速度 {:.1f} m/s。", task.getId(), uav_id, i + 1, rep + 1, segment_speed);
						}
						LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 开始平滑和时间参数化段 #{} (重复 {}), 速度 {:.1f} m/s, {} 个几何点...",
							task.getId(), uav_id, i + 1, rep + 1, segment_speed, geometric_path.size());
						// 使用基类方法，传入任务的策略列表以便获取优化器参数等
						if (!smoothAndTimeParameterizeECEF(geometric_path, uav, last_wp_state, segment_speed, segment_to_add, &result, task.getStrategies())) {
							// smoothAndTimeParameterize 会在 result 中添加告警并可能设置状态
							LOG_ERROR("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}] 路径段 #{} (重复 {}) 处理失败 (平滑/参数化过程出错)。", task.getId(), uav_id, i + 1, rep + 1);
							uav_plan_success = false; // 标记当前 UAV 失败
							LOG_INFO("[FollowPathTaskPlanner] 任务 [{}]: 无人机 [{}] 规划失败 (平滑/参数化过程出错)。", task.getId(), uav_id);
							goto next_uav; // 跳到下一个无人机
						}
						LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 段 #{} (重复 {}) 平滑和参数化完成，生成 {} 个航点。", task.getId(), uav_id, i + 1, rep + 1, segment_to_add.size());

						// 处理载荷动作
						if (cp.hasPayloadAction() && cp.action && !segment_to_add.empty()) {
							auto cmd_opt = cp.action->getCommand();
							if (cmd_opt) {
								LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 在控制点 #{} (重复 {}) 添加载荷动作: {}", task.getId(), uav_id, i, rep + 1, cmd_opt->command_name);
								// 将动作添加到新航段的最后一个点
								segment_to_add.back().payload_actions.push_back(*cmd_opt);
							}
						}

						// 合并航段: 将 segment_to_add 合并到 total_route_segment
						if (!total_route_segment.empty() && !segment_to_add.empty()) {
							// 检查新段的起点是否与总路径的终点非常接近 (位置和时间)
							if ((total_route_segment.back().position - segment_to_add.front().position).norm() < Constants::GEOMETRY_EPSILON &&
								std::abs(total_route_segment.back().time_stamp - segment_to_add.front().time_stamp) < Constants::TIME_EPSILON) {
								// 如果起点重合，合并载荷动作到前一个点
								if (!segment_to_add.front().payload_actions.empty()) {
									LOG_TRACE("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 合并航段: 将新段起点的 {} 个载荷动作合并到前一段终点。",
										task.getId(), uav_id, segment_to_add.front().payload_actions.size());
									total_route_segment.back().payload_actions.insert(total_route_segment.back().payload_actions.end(),
										segment_to_add.front().payload_actions.begin(),
										segment_to_add.front().payload_actions.end());
								}
								// 移除重复的起点
								segment_to_add.erase(segment_to_add.begin());
								LOG_TRACE("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 合并航段: 移除重复起点，新段剩余 {} 点。", task.getId(), uav_id, segment_to_add.size());
							}
							else if (segment_to_add.front().time_stamp < total_route_segment.back().time_stamp - Constants::TIME_EPSILON) {
								Time time_offset = total_route_segment.back().time_stamp - segment_to_add.front().time_stamp + Constants::TIME_EPSILON;
								LOG_WARN("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 路径段 {} (重复 {}) 时间戳不连续 (新起点 {:.3f} < 旧终点 {:.3f})，后续时间戳将增加 {:.4f}s",
									task.getId(), uav_id, i + 1, rep + 1, segment_to_add.front().time_stamp, total_route_segment.back().time_stamp, time_offset);
								for (auto& rp : segment_to_add) { rp.time_stamp += time_offset; }
							}
							// 其他情况（例如时间戳正常连续但位置不重合）不需要特殊处理
						}

						// 将新段（可能已修改）添加到总路径
						if (!segment_to_add.empty()) {
							total_route_segment.insert(total_route_segment.end(), segment_to_add.begin(), segment_to_add.end());
							PlannedRoute temp_route_for_warning(uav_id);
							temp_route_for_warning.addWaypoints(segment_to_add);
							checkSegmentWarnings(temp_route_for_warning, uav_id, result, task.getId());
							last_wp_state = NSUav::stateFromRoutePt(total_route_segment.back());
							LOG_TRACE("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 航段 #{} (重复 {}) 已合并，总点数: {}, 新状态 Pos=({}), Time={:.3f}",
								task.getId(), uav_id, i + 1, rep + 1, total_route_segment.size(),
								last_wp_state.position.toString(), last_wp_state.time_stamp);
						}
						else {
							// 如果平滑/参数化后段为空，这通常是一个问题
							LOG_WARN("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 平滑/参数化后路径段 {} (重复 {}) 为空，状态未更新。", task.getId(), uav_id, i + 1, rep + 1);
						}

						// 处理控制点的停留时间
						if (cp.hasLoiterTime() && cp.required_loiter_time > Constants::TIME_EPSILON && !total_route_segment.empty()) {
							RoutePoint loiter_end = total_route_segment.back();
							Time loiter_duration = cp.required_loiter_time;
							loiter_end.time_stamp += loiter_duration;
							loiter_end.velocity = Vector3D::Zero();
							loiter_end.payload_actions.clear();
							total_route_segment.push_back(loiter_end);
							last_wp_state = NSUav::stateFromRoutePt(loiter_end);
							LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 在控制点 #{} (重复 {}) 停留 {:.2f} 秒。新状态 Time={:.3f}", task.getId(), uav_id, i, rep + 1, loiter_duration, last_wp_state.time_stamp);
						}
					} // 结束单次路径的点循环
					LOG_DEBUG("[FollowPathTaskPlanner] 任务 [{}], 无人机 [{}]: 完成路径重复次数 {}/{}。", task.getId(), uav_id, rep + 1, repeat);
				} // 结束重复循环

				// --- 3.4 将最终航线添加到结果 ---
				planned_route.addWaypoints(total_route_segment);
				if (!planned_route.isEmpty()) {
					result.addRoute(std::move(planned_route));
					LOG_INFO("[FollowPathTaskPlanner] 任务 [{}]: 无人机 [{}] 规划完成，生成 {} 个航点。", task.getId(), uav_id, planned_route.getWaypoints().size());
				}
				else {
					LOG_WARN("[FollowPathTaskPlanner] 任务 [{}]: 无人机 [{}] 规划完成，但未生成有效航点。", task.getId(), uav_id);
					// 根据情况，可能需要设置 uav_plan_success = false;
				}

			next_uav:; // 用于 goto 跳转标签，跳过当前无人机剩余的规划步骤
			} // 结束无人机循环

			// --- 4. 设置最终状态 ---
			if (!overall_success) {
				// 如果任何一个 UAV 规划失败，则将整体结果标记为失败
				if (result.wasSuccessful()) { // 仅在尚未明确失败时设置
					result.setStatus(false, "部分无人机的 FollowPath 任务规划失败。");
				}
			}
			else {
				// 只有当所有无人机都成功且至少有一个无人机有航线时才算成功
				bool has_any_route = false;
				for (const auto& pair : result.getAllRoutes()) {
					if (!pair.second.isEmpty()) {
						has_any_route = true;
						break;
					}
				}
				if (!has_any_route && !assigned_uavs.empty()) { // 如果有无人机分配但没生成路径，也算失败
					result.setStatus(false, "所有无人机规划完成，但未生成任何有效航线。");
					LOG_WARN("[FollowPathTaskPlanner] 任务 [{}]: 所有无人机规划完成，但未生成任何有效航线", task.getId());
				}
			}

			LOG_INFO("[FollowPathTaskPlanner] 任务 [{}] 规划结束，最终状态: {}", task.getId(), result.wasSuccessful() ? "成功" : "失败");
			return result;
		}

	} // namespace NSPlanning
} // namespace NSDrones