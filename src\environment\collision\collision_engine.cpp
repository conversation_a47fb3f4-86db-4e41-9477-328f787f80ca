#include "environment/collision/collision_engine.h"
#include "environment/collision/collision_types.h"
#include "environment/geometry/geometry_manager.h"
#include "core/geometry/ishape.h"
#include "core/geometry/shapes/sphere_shape.h"
#include "utils/object_id.h"
#include "utils/logging.h"
#include "core/types.h"
#include <fcl/fcl.h>
#include <algorithm>
#include <cmath>

namespace NSDrones {
	namespace NSEnvironment {

		// === 类型别名定义 ===
		using SpatialQueryFilter = SpatialQueryFilter;
		using SpatialObjectData = SpatialObjectData;

		// === 常量定义 ===
		namespace {
			constexpr double METERS_TO_DEGREES_APPROX = 1.0 / 111000.0;  ///< 米到度的近似转换系数
			constexpr int DEFAULT_SEGMENT_SAMPLES = 10;                   ///< 线段检测的默认采样点数
			constexpr double MIN_SAFETY_RADIUS = 0.01;                    ///< 最小安全半径（米）
		}

		// === 初始化方法实现 ===

		template <typename ObjectContainer>
		bool CollisionEngine<ObjectContainer>::initialize(std::shared_ptr<NSParams::ParamValues> global_params) {
			LOG_INFO("碰撞检测引擎: 开始初始化");

			if (!global_params) {
				LOG_ERROR("碰撞检测引擎: 全局参数配置为空，初始化失败");
				return false;
			}

			try {
				// 读取碰撞检测配置参数
				bool collision_enabled = global_params->getValueOrDefault<bool>("collision_detection.enabled", true);
				double collision_margin = global_params->getValueOrDefault<double>("collision_detection.margin", 0.1);
				bool continuous_detection = global_params->getValueOrDefault<bool>("collision_detection.continuous", false);
				int max_checks = global_params->getValueOrDefault<int>("collision_detection.max_checks_per_frame", 1000);

				LOG_INFO("碰撞检测引擎: 配置参数加载完成");
				LOG_DEBUG("  - 碰撞检测启用: {}", collision_enabled ? "是" : "否");
				LOG_DEBUG("  - 安全边距: {:.3f}米", collision_margin);
				LOG_DEBUG("  - 连续碰撞检测: {}", continuous_detection ? "启用" : "禁用");
				LOG_DEBUG("  - 每帧最大检查数: {}", max_checks);

				if (!collision_enabled) {
					LOG_WARN("碰撞检测引擎: 碰撞检测功能已在配置中禁用");
					return true; // 返回成功但功能禁用
				}

				// 验证参数有效性
				if (collision_margin < 0.0) {
					LOG_WARN("碰撞检测引擎: 安全边距 {:.3f} 无效，使用默认值 0.1", collision_margin);
					collision_margin = 0.1;
				}

				if (max_checks <= 0) {
					LOG_WARN("碰撞检测引擎: 最大检查数 {} 无效，使用默认值 1000", max_checks);
					max_checks = 1000;
				}

				LOG_INFO("碰撞检测引擎: 初始化成功，使用FCL库进行高性能碰撞检测");
				return true;

			} catch (const std::exception& e) {
				LOG_ERROR("碰撞检测引擎: 初始化过程中发生异常: {}", e.what());
				return false;
			}
		}

		// === 几何体碰撞检测方法实现 ===

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkGeometryAgainstEnvironment(
			const IShape& temp_shape,
			const EcefPoint& ecef_position,
			const Orientation& orientation,
			const CollisionOptions& options) {

			LOG_DEBUG("碰撞检测引擎: 开始检测临时几何体 {} 与环境的碰撞，ECEF位置: {}",
				temp_shape.toString(), ecef_position.toString());

			std::vector<CollisionResult> results;

			try {
				// 1. 创建临时几何体的FCL对象
				auto temp_fcl_object = createFCLObject(&temp_shape, ecef_position, orientation);
				if (!temp_fcl_object) {
					LOG_ERROR("碰撞检测引擎: 无法为临时几何体 {} 创建FCL对象", temp_shape.toString());
					return results;
				}

				// 2. 转换坐标用于空间索引查询
				WGS84Point wgs84_position = NSUtils::CoordinateConverter::ecefToWGS84(ecef_position);

				// 3. 计算查询边界框
				WGS84BoundingBox query_bounds = GeometryManager::calculateQueryBounds(
					wgs84_position,
					temp_shape.getAABB(),
					options.safetyMargin
				);

				LOG_TRACE("碰撞检测引擎: 使用WGS84查询边界框 [{:.6f},{:.6f},{:.1f}] - [{:.6f},{:.6f},{:.1f}]",
					query_bounds.minLongitude, query_bounds.minLatitude, query_bounds.minAltitude,
					query_bounds.maxLongitude, query_bounds.maxLatitude, query_bounds.maxAltitude);

				// 4. 查找潜在碰撞对象
				auto potential_colliders = findPotentialColliders(query_bounds, options);
				LOG_DEBUG("碰撞检测引擎: 找到 {} 个潜在碰撞对象", potential_colliders.size());

				// 5. 执行精确碰撞检测
				results = performDetailedCollisionCheck(
					temp_fcl_object.get(),
					temp_shape.getType(),
					NSUtils::INVALID_OBJECT_ID, // 临时对象无ID
					potential_colliders,
					options
				);

			} catch (const std::exception& e) {
				LOG_ERROR("碰撞检测引擎: 临时几何体碰撞检测过程中发生异常: {}", e.what());
			}

			LOG_DEBUG("碰撞检测引擎: 临时几何体碰撞检测完成，发现 {} 个碰撞事件", results.size());
			return results;
		}

		// === 辅助方法实现 ===



		template <typename ObjectContainer>
		std::vector<ObjectID> CollisionEngine<ObjectContainer>::findPotentialColliders(
			const WGS84BoundingBox& query_bounds,
			const CollisionOptions& options) const {

			// 创建空间查询过滤器
			SpatialQueryFilter filter;
			filter.customFilter = [&options](const ObjectID& id, const SpatialObjectData& data) -> bool {
				// 过滤被忽略的对象
				return options.ignoredObjectIds.count(id) == 0;
			};

			return spatial_index_.findObjectsInRegion(query_bounds, filter);
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::performDetailedCollisionCheck(
			const fcl::CollisionObjectd* query_fcl_object,
			ShapeType query_shape_type,
			ObjectID query_object_id,
			const std::vector<ObjectID>& potential_colliders,
			const CollisionOptions& options) const {

			std::vector<CollisionResult> results;

			for (ObjectID other_id : potential_colliders) {
				// 跳过自身
				if (other_id == query_object_id) {
					continue;
				}

				// 跳过被忽略的对象
				if (options.ignoredObjectIds.count(other_id) > 0) {
					LOG_TRACE("碰撞检测引擎: 跳过被忽略的对象 ID {}", other_id);
					continue;
				}

				// 获取对象
				auto other_object = object_storage_.getObject(other_id);
				if (!other_object) {
					LOG_WARN("碰撞检测引擎: 无法获取对象 ID {}", other_id);
					continue;
				}

				// 检查动态/静态对象过滤
				if (!shouldCheckObject(other_id, options)) {
					continue;
				}

				// 获取对象形状
				const auto other_shape = other_object->getShape();
				if (!other_shape) {
					LOG_WARN("碰撞检测引擎: 对象 ID {} 缺少形状", other_id);
					continue;
				}

				// 创建FCL对象
				auto other_fcl_object = createFCLObjectFromWGS84(
					other_shape.get(),
					other_object->getWGS84Position(),
					other_object->getOrientation()
				);

				if (!other_fcl_object) {
					LOG_WARN("碰撞检测引擎: 无法为对象 ID {} 创建FCL对象", other_id);
					continue;
				}

				// 执行碰撞检测
				CollisionResult result = detector_registry_.detectCollision(
					query_fcl_object, other_fcl_object.get(),
					query_shape_type, other_shape->getType(),
					options
				);

				// 设置对象ID
				result.object1Id = query_object_id;
				result.object2Id = other_id;

				// 检查结果是否需要保存
				if (shouldSaveResult(result, options)) {
					LOG_DEBUG("碰撞检测引擎: 检测到碰撞或近距离事件，对象 {} vs {}，距离: {:.3f}米",
						query_object_id, other_id, result.distance);
					results.push_back(std::move(result));
				}
			}

			return results;
		}

		template <typename ObjectContainer>
		bool CollisionEngine<ObjectContainer>::shouldCheckObject(
			ObjectID object_id,
			const CollisionOptions& options) const {

			try {
				bool is_dynamic = object_storage_.isObjectDynamic(object_id);

				// 检查是否应该检查动态对象
				if (is_dynamic && !options.checkDynamicObjects) {
					LOG_TRACE("碰撞检测引擎: 跳过动态对象 ID {}", object_id);
					return false;
				}

				// 检查是否应该检查静态对象
				if (!is_dynamic && !options.checkStaticObjects) {
					LOG_TRACE("碰撞检测引擎: 跳过静态对象 ID {}", object_id);
					return false;
				}

				return true;
			} catch (const std::exception& e) {
				LOG_WARN("碰撞检测引擎: 检查对象 {} 类型时发生异常: {}", object_id, e.what());
				return true; // 默认允许检查
			}
		}

		template <typename ObjectContainer>
		bool CollisionEngine<ObjectContainer>::shouldSaveResult(
			const CollisionResult& result,
			const CollisionOptions& options) const {

			// 如果有碰撞，总是保存
			if (result.hasCollision) {
				return true;
			}

			// 如果启用了距离计算且距离在安全边距内，保存结果
			if (options.enableDistance &&
				result.distance != std::numeric_limits<double>::infinity() &&
				result.distance <= options.safetyMargin + Constants::GEOMETRY_EPSILON) {
				return true;
			}

			return false;
		}

		// === 对象碰撞检测方法实现 ===

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkObjectCollisions(
			ObjectID objectId, const CollisionOptions& options) {

			LOG_DEBUG("碰撞检测引擎: 开始检测对象 ID {} 与环境的碰撞", objectId);
			std::vector<CollisionResult> results;

			try {
				// 1. 获取并验证对象
				auto object = object_storage_.getObject(objectId);
				if (!object) {
					LOG_WARN("碰撞检测引擎: 未找到对象 ID {}", objectId);
					return results;
				}

				const auto shape = object->getShape();
				if (!shape) {
					LOG_DEBUG("碰撞检测引擎: 对象 ID {} 缺少形状", objectId);
					return results;
				}

				// 2. 创建FCL对象
				auto fcl_object = createFCLObjectFromWGS84(
					shape.get(),
					object->getWGS84Position(),
					object->getOrientation()
				);
				if (!fcl_object) {
					LOG_WARN("碰撞检测引擎: 无法为对象 ID {} 创建FCL对象", objectId);
					return results;
				}

				// 3. 计算查询边界框并查找潜在碰撞对象
				WGS84BoundingBox query_bounds = GeometryManager::calculateQueryBounds(
					object->getWGS84Position(),
					shape->getAABB(),
					options.safetyMargin
				);

				auto potential_colliders = findPotentialColliders(query_bounds, options);
				LOG_DEBUG("碰撞检测引擎: 对象 ID {} 找到 {} 个潜在碰撞对象",
					objectId, potential_colliders.size());

				// 4. 执行详细碰撞检测
				results = performDetailedCollisionCheck(
					fcl_object.get(),
					shape->getType(),
					objectId,
					potential_colliders,
					options
				);

			} catch (const std::exception& e) {
				LOG_ERROR("碰撞检测引擎: 对象 ID {} 碰撞检测过程中发生异常: {}", objectId, e.what());
			}

			LOG_DEBUG("碰撞检测引擎: 对象 ID {} 碰撞检测完成，发现 {} 个碰撞事件", objectId, results.size());
			return results;
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkAllCollisions(const CollisionOptions& options) {
			LOG_DEBUG("碰撞检测引擎: 开始检查场景中所有对象间的碰撞");
			std::vector<CollisionResult> results;

			try {
				// 1. 获取所有对象
				auto all_objects = object_storage_.getAllObjects();
				LOG_INFO("碰撞检测引擎: 对 {} 个对象进行全局碰撞检测", all_objects.size());

				if (all_objects.size() < 2) {
					LOG_DEBUG("碰撞检测引擎: 对象数量不足，无需进行碰撞检测");
					return results;
				}

				// 2. 对每对对象进行碰撞检测（避免重复检测）
				size_t total_pairs = (all_objects.size() * (all_objects.size() - 1)) / 2;
				size_t checked_pairs = 0;

				for (size_t i = 0; i < all_objects.size(); ++i) {
					for (size_t j = i + 1; j < all_objects.size(); ++j) {
						const auto& obj1 = all_objects[i];
						const auto& obj2 = all_objects[j];

						if (!obj1 || !obj2) {
							continue;
						}

						// 检查是否应该检测这对对象
						if (!shouldCheckObjectPair(obj1->getId(), obj2->getId(), options)) {
							continue;
						}

						// 执行对象间碰撞检测
						auto result = checkObjectCollision(*obj1, *obj2, options);

						if (shouldSaveResult(result, options)) {
							LOG_DEBUG("碰撞检测引擎: 检测到对象 {} 与 {} 间的碰撞/近距离事件",
								obj1->getId(), obj2->getId());
							results.push_back(std::move(result));
						}

						checked_pairs++;
					}
				}

				LOG_INFO("碰撞检测引擎: 全局碰撞检测完成，检查了 {}/{} 对对象，发现 {} 个碰撞事件",
					checked_pairs, total_pairs, results.size());

			} catch (const std::exception& e) {
				LOG_ERROR("碰撞检测引擎: 全局碰撞检测过程中发生异常: {}", e.what());
			}

			return results;
		}

		template <typename ObjectContainer>
		bool CollisionEngine<ObjectContainer>::shouldCheckObjectPair(
			ObjectID obj1_id,
			ObjectID obj2_id,
			const CollisionOptions& options) const {

			// 检查忽略列表
			if (options.ignoredObjectIds.count(obj1_id) > 0 ||
				options.ignoredObjectIds.count(obj2_id) > 0) {
				return false;
			}

			// 检查动态/静态对象过滤
			if (!shouldCheckObject(obj1_id, options) ||
				!shouldCheckObject(obj2_id, options)) {
				return false;
			}

			return true;
		}

		// === 访问器方法实现 ===

		template <typename ObjectContainer>
		std::shared_ptr<EntityObject> CollisionEngine<ObjectContainer>::getObject(ObjectID id) const {
			try {
				return object_storage_.getObject(id);
			} catch (const std::exception& e) {
				LOG_ERROR("碰撞检测引擎: 获取对象 ID {} 时发生异常: {}", id, e.what());
				return nullptr;
			}
		}

		// === 单对象碰撞检测实现 ===

		template <typename ObjectContainer>
		CollisionResult CollisionEngine<ObjectContainer>::checkObjectCollision(
			const EntityObject& obj1,
			const EntityObject& obj2,
			const CollisionOptions& options) {

			LOG_TRACE("碰撞检测引擎: 检测对象 {} 与 {} 间的碰撞", obj1.getId(), obj2.getId());

			try {
				// 1. 验证对象形状
				const auto shape1 = obj1.getShape();
				const auto shape2 = obj2.getShape();

				if (!shape1 || !shape2) {
					LOG_DEBUG("碰撞检测引擎: 对象缺少形状，跳过碰撞检测");
					return CollisionResult{};
				}

				// 2. 创建FCL碰撞对象
				auto fcl_obj1 = createFCLObjectFromWGS84(
					shape1.get(),
					obj1.getWGS84Position(),
					obj1.getOrientation()
				);
				auto fcl_obj2 = createFCLObjectFromWGS84(
					shape2.get(),
					obj2.getWGS84Position(),
					obj2.getOrientation()
				);

				if (!fcl_obj1 || !fcl_obj2) {
					LOG_WARN("碰撞检测引擎: 无法创建FCL对象，跳过碰撞检测");
					return CollisionResult{};
				}

				// 3. 执行碰撞检测
				CollisionResult result = detector_registry_.detectCollision(
					fcl_obj1.get(), fcl_obj2.get(),
					shape1->getType(), shape2->getType(),
					options
				);

				// 4. 设置结果信息
				result.object1Id = obj1.getId();
				result.object2Id = obj2.getId();

				if (result.hasCollision) {
					LOG_DEBUG("碰撞检测引擎: 检测到对象 {} 与 {} 碰撞，穿透深度: {:.3f}米",
						obj1.getId(), obj2.getId(), result.penetrationDepth);
				}

				return result;

			} catch (const std::exception& e) {
				LOG_ERROR("碰撞检测引擎: 对象碰撞检测过程中发生异常: {}", e.what());
				return CollisionResult{};
			}
		}

		// === Protected辅助方法实现 ===

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkObjectAgainstEnvironment(
			std::shared_ptr<EntityObject> obj,
			const CollisionOptions& options) {

			if (!obj) {
				LOG_WARN("碰撞检测引擎: 检测对象与环境碰撞时，输入对象为空");
				return std::vector<CollisionResult>{};
			}

			LOG_DEBUG("碰撞检测引擎: 检测对象 {} 与环境的碰撞", obj->getId());
			return checkObjectCollisions(obj->getId(), options);
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkObjectAgainstObject(
			std::shared_ptr<EntityObject> obj1,
			std::shared_ptr<EntityObject> obj2,
			const CollisionOptions& options) {

			if (!obj1 || !obj2) {
				LOG_WARN("碰撞检测引擎: 检测对象间碰撞时，输入对象为空");
				return std::vector<CollisionResult>{};
			}

			LOG_DEBUG("碰撞检测引擎: 检测对象 {} 与 {} 的碰撞", obj1->getId(), obj2->getId());

			auto result = checkObjectCollision(*obj1, *obj2, options);
			if (shouldSaveResult(result, options)) {
				return std::vector<CollisionResult>{ std::move(result) };
			}

			return std::vector<CollisionResult>{};
		}

		// === 基础几何碰撞检测实现 ===

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkPointAgainstEnvironment(
			const EcefPoint& ecef_point,
			double safety_radius,
			const CollisionOptions& options) {

			LOG_DEBUG("碰撞检测引擎: 检测ECEF点 {} 与环境的碰撞，安全半径: {:.2f}米",
				ecef_point.toString(), safety_radius);

			// 验证安全半径
			if (safety_radius < MIN_SAFETY_RADIUS) {
				LOG_WARN("碰撞检测引擎: 安全半径 {:.3f} 过小，使用最小值 {:.3f}",
					safety_radius, MIN_SAFETY_RADIUS);
				safety_radius = MIN_SAFETY_RADIUS;
			}

			try {
				// 创建球形几何体表示点的安全区域
				SphereShape sphere_shape(safety_radius);

				// 使用几何体检测方法
				return checkGeometryAgainstEnvironment(
					sphere_shape,
					ecef_point,
					Orientation::Identity(),
					options
				);
			} catch (const std::exception& e) {
				LOG_ERROR("碰撞检测引擎: 点碰撞检测过程中发生异常: {}", e.what());
				return std::vector<CollisionResult>{};
			}
		}

		template <typename ObjectContainer>
		std::vector<CollisionResult> CollisionEngine<ObjectContainer>::checkSegmentAgainstEnvironment(
			const EcefPoint& ecef_start,
			const EcefPoint& ecef_end,
			double safety_radius,
			const CollisionOptions& options) {

			LOG_DEBUG("碰撞检测引擎: 检测ECEF线段 {} - {} 与环境的碰撞，安全半径: {:.2f}米",
				ecef_start.toString(), ecef_end.toString(), safety_radius);

			// 验证安全半径
			if (safety_radius < MIN_SAFETY_RADIUS) {
				LOG_WARN("碰撞检测引擎: 安全半径 {:.3f} 过小，使用最小值 {:.3f}",
					safety_radius, MIN_SAFETY_RADIUS);
				safety_radius = MIN_SAFETY_RADIUS;
			}

			std::vector<CollisionResult> all_results;

			try {
				// 计算线段长度，决定采样点数
				WGS84Point wgs84_start = NSUtils::CoordinateConverter::ecefToWGS84(ecef_start);
				WGS84Point wgs84_end = NSUtils::CoordinateConverter::ecefToWGS84(ecef_end);

				double segment_length = GeometryManager::calculateDistance(wgs84_start, wgs84_end);

				// 根据线段长度和安全半径动态调整采样点数
				int num_samples = std::max(
					DEFAULT_SEGMENT_SAMPLES,
					static_cast<int>(std::ceil(segment_length / safety_radius))
				);

				// 限制最大采样点数以避免性能问题
				num_samples = std::min(num_samples, 50);

				LOG_DEBUG("碰撞检测引擎: 线段长度 {:.2f}米，使用 {} 个采样点", segment_length, num_samples + 1);

				// 沿线段进行采样检测
				for (int i = 0; i <= num_samples; ++i) {
					double t = static_cast<double>(i) / static_cast<double>(num_samples);

					// 使用GeometryManager进行线性插值（在ECEF坐标系中）
					EcefPoint sample_point = GeometryManager::interpolateECEFPoints(ecef_start, ecef_end, t);

					// 检测采样点的碰撞
					auto point_results = checkPointAgainstEnvironment(sample_point, safety_radius, options);

					// 合并结果，避免重复
					mergeUniqueResults(all_results, point_results);
				}

			} catch (const std::exception& e) {
				LOG_ERROR("碰撞检测引擎: 线段碰撞检测过程中发生异常: {}", e.what());
			}

			LOG_DEBUG("碰撞检测引擎: 线段碰撞检测完成，发现 {} 个碰撞事件", all_results.size());
			return all_results;
		}

		// === 私有辅助方法实现 ===

		template <typename ObjectContainer>
		void CollisionEngine<ObjectContainer>::mergeUniqueResults(
			std::vector<CollisionResult>& target,
			const std::vector<CollisionResult>& source) const {

			for (const auto& new_result : source) {
				// 检查是否已存在相同的碰撞对
				bool exists = std::any_of(target.begin(), target.end(),
					[&new_result](const CollisionResult& existing) {
						return (existing.object1Id == new_result.object1Id &&
								existing.object2Id == new_result.object2Id) ||
							   (existing.object1Id == new_result.object2Id &&
								existing.object2Id == new_result.object1Id);
					});

				if (!exists) {
					target.push_back(new_result);
				}
			}
		}

		// === 模板显式实例化 ===
		template class CollisionEngine<std::unordered_map<ObjectID, std::shared_ptr<EntityObject>>>;

	} // namespace NSEnvironment
} // namespace NSDrones