#pragma once

#include "environment/maps/igridmap.h"
#include "core/types.h"
#include "params/parameters.h"
#include <gdal_priv.h>
#include <GeographicLib/LocalCartesian.hpp>
#include <string>
#include <memory>
#include <vector>
#include <utility>
#include <optional>
#include <array>
#include <mutex>
#include <unordered_map>  // 添加缺失的头文件
#include "Eigen/Core"     // 添加Eigen头文件

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @class SingleGridMap
		 * @brief 单个地图文件的网格地图实现类
		 *
		 * 该类实现IGridMap接口，负责加载和管理单个地图文件（GeoTIFF、PNG等格式）。
		 * 提供基于WGS84坐标的高效地形和地物查询功能。
		 *
		 * 核心特性：
		 * - 多格式支持：GeoTIFF（地理配准）、PNG（自定义编码）
		 * - 高效存储：基于Eigen矩阵的内存优化数据结构
		 * - 坐标转换：WGS84 ↔ 局部笛卡尔坐标系的无缝转换
		 * - 多图层支持：高程、地物类型、地物高度等多种数据图层
		 * - 线程安全：所有查询操作都是线程安全的
		 *
		 * 坐标系统设计：
		 * - 外部接口：WGS84坐标系统（经度、纬度、高度）
		 * - 内部存储：局部笛卡尔坐标系统（以地图左上角为原点）
		 * - 数据组织：行列索引直接对应地理位置，支持O(1)查询
		 *
		 * 内存布局：
		 * - 数据矩阵：Eigen::MatrixXf，列优先存储
		 * - 坐标原点：地图左上角的WGS84坐标
		 * - 分辨率信息：X/Y方向的米/像素比例
		 *
		 * @note 所有几何运算通过GeometryManager进行，确保计算精度和一致性
		 * @note 线程安全通过读写锁实现，支持高并发查询
		 */
		class SingleGridMap : public IGridMap {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 默认构造函数
			 * 创建未初始化的地图对象，需要调用initialize()完成初始化
			 */
			SingleGridMap();

			/**
			 * @brief 析构函数
			 * 自动释放所有资源，包括GDAL数据集和坐标转换器
			 */
			~SingleGridMap() override;

			// === 拷贝和移动语义 ===
			SingleGridMap(const SingleGridMap&) = delete;              ///< 禁止拷贝构造，避免资源管理问题
			SingleGridMap& operator=(const SingleGridMap&) = delete;   ///< 禁止拷贝赋值，避免资源管理问题
			SingleGridMap(SingleGridMap&&) = default;                  ///< 允许移动构造，支持高效资源转移
			SingleGridMap& operator=(SingleGridMap&&) = default;       ///< 允许移动赋值，支持高效资源转移

			// === IGridMap接口实现 ===

			/**
			 * @brief 初始化地图数据源
			 * @param global_params 全局参数对象，包含地图文件路径和配置信息
			 * @return 初始化成功返回true，失败返回false
			 * @note 从global_params中读取map.file_path参数确定要加载的文件
			 * @note 根据文件扩展名自动选择加载模式（GeoTIFF或PNG）
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> global_params) override;

			/**
			 * @brief 获取指定位置的地形高程
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 海拔高度（米），查询失败时返回std::nullopt
			 */
			std::optional<double> getElevation(double latitude, double longitude) const override;

			/**
			 * @brief 获取指定位置的地物类型
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 地物类型枚举，查询失败时返回std::nullopt
			 */
			std::optional<FeatureType> getFeature(double latitude, double longitude) const override;

			/**
			 * @brief 获取指定位置的地物高度
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 地物高度（米），查询失败时返回std::nullopt
			 */
			std::optional<double> getFeatureHeight(double latitude, double longitude) const override;

			/**
			 * @brief 检查地图是否已初始化
			 * @return 已初始化返回true，否则返回false
			 */
			bool isInitialized() const override;

			/**
			 * @brief 检查指定坐标是否在数据覆盖范围内
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 在覆盖范围内且有有效数据返回true，否则返回false
			 */
			bool isCovered(double latitude, double longitude) const override;

			// === 扩展功能接口 ===

			/**
			 * @brief 直接从文件初始化地图（供TiledGridMap使用）
			 * @param file_path 地图文件路径
			 * @param load_mode 加载模式（"geotiff"或"png"）
			 * @return 初始化成功返回true，失败返回false
			 * @note 此方法不依赖全局参数，直接从指定文件加载
			 */
			bool initializeFromFile(const std::string& file_path, const std::string& load_mode = "geotiff");

			/**
			 * @brief 获取地图元数据信息
			 * @return 地图元数据结构
			 */
			MapMetadata getMetadata() const override;

			/**
			 * @brief 获取可用图层列表
			 * @return 图层名称列表
			 */
			std::vector<std::string> getAvailableLayers() const;

			// === 图层和数据访问接口 ===

			/**
			 * @brief 检查指定图层是否存在
			 * @param layer_name 图层名称
			 * @return 图层存在返回true，否则返回false
			 */
			bool hasLayer(const std::string& layer_name) const;

			/**
			 * @brief 获取指定图层的数据矩阵
			 * @param layer_name 图层名称
			 * @return 数据矩阵，图层不存在时返回std::nullopt
			 * @note 返回的矩阵是只读的，不应修改其内容
			 */
			std::optional<Eigen::MatrixXf> getLayerMatrix(const std::string& layer_name) const;

			// === 坐标转换接口 ===

			/**
			 * @brief 获取地图参考原点（局部坐标系原点）
			 * @return 地图左上角的WGS84坐标
			 */
			WGS84Point getReferenceOrigin() const;

			// === TiledGridMap调用接口 ===

			/**
			 * @brief 获取地图覆盖的边界框
			 * @return WGS84边界框
			 * @note 供TiledGridMap用于判断瓦片覆盖范围
			 */
			WGS84BoundingBox getBoundingBox() const;

			/**
			 * @brief 获取地图分辨率信息
			 * @return 分辨率对（X方向、Y方向，米/像素）
			 * @note 供TiledGridMap用于统一分辨率管理
			 */
			std::pair<double, double> getResolution() const;

			/**
			 * @brief 获取地图网格尺寸
			 * @return 尺寸对（宽度、高度，像素数）
			 * @note 供TiledGridMap用于瓦片管理
			 */
			std::pair<int, int> getGridSize() const;

			/**
			 * @brief 检查是否包含指定的图层
			 * @param layer_name 图层名称
			 * @return 包含该图层返回true，否则返回false
			 * @note 供TiledGridMap用于图层一致性检查
			 */
			bool containsLayer(const std::string& layer_name) const;

			/**
			 * @brief 获取瓦片标识符（基于文件路径）
			 * @return 瓦片唯一标识符
			 * @note 供TiledGridMap用于瓦片索引和管理
			 */
			std::string getTileId() const;

			// === 文件I/O接口 ===

			/**
			 * @brief 保存地图数据到PNG文件
			 * @param output_path 输出文件路径
			 * @return 保存成功返回true，失败返回false
			 * @note PNG格式使用自定义编码保存高程和元数据信息
			 * @note 适用于缓存和快速加载场景
			 */
			bool saveToPng(const std::string& output_path);

		private:
			// === 核心数据存储 ===
			std::unordered_map<std::string, Eigen::MatrixXf> data_layers_;  ///< 多图层数据存储（图层名 -> 数据矩阵）

			// === 地图几何信息 ===
			WGS84Point map_origin_;                                        ///< 地图左上角参考原点（WGS84坐标）
			std::pair<double, double> native_resolution_;                  ///< 原始分辨率（X方向、Y方向，米/像素）
			std::pair<int, int> grid_size_;                                ///< 网格尺寸（宽度、高度，像素数）

			// === 图层配置 ===
			std::string elevation_layer_name_;                             ///< 高程图层名称
			std::string feature_layer_name_;                               ///< 地物类型图层名称
			std::string feature_height_layer_name_;                        ///< 地物高度图层名称

			// === 内部坐标转换器（仅用于地图内部计算，对外不可见） ===
			std::shared_ptr<GeographicLib::LocalCartesian> local_cartesian_;  ///< 局部坐标转换器

			// === 状态和线程安全 ===
			bool initialized_ = false;                                     ///< 初始化完成标志
			mutable std::mutex coordinate_mutex_;                          ///< 坐标转换线程安全锁

			// === 初始化和配置方法 ===

			/**
			 * @brief 智能检测和设置图层名称
			 * @param global_params 全局参数对象
			 * @return 检测成功返回true（至少找到高程图层）
			 * @note 优先使用配置指定的图层名称，配置不存在时自动检测
			 */
			bool detectAndSetLayers(std::shared_ptr<NSParams::ParamValues> global_params);

			/**
			 * @brief 从GeoTIFF文件加载地图数据
			 * @param file_path GeoTIFF文件路径
			 * @return 加载成功返回true
			 */
			bool loadFromTif(const std::string& file_path);

			/**
			 * @brief 从PNG文件加载地图数据
			 * @param file_path PNG文件路径
			 * @return 加载成功返回true
			 */
			bool loadFromPng(const std::string& file_path);

			// === 几何计算方法（内部使用，通过GeometryManager实现） ===

			/**
			 * @brief 计算地图分辨率
			 * @param geoTransform GDAL地理变换参数
			 * @return 分辨率对（X方向、Y方向，米/像素）
			 */
			std::pair<double, double> calculateResolution(const double* geoTransform);

			/**
			 * @brief 计算GeoTIFF四个角点的WGS84坐标
			 * @param dataset GDAL数据集指针
			 * @param geoTransform 地理变换参数
			 * @return 四个角点的WGS84坐标数组
			 */
			std::array<WGS84Point, 4> calculateCorners(GDALDataset* dataset, const double* geoTransform);

			// === 坐标转换方法（内部使用，对外不可见） ===

			/**
			 * @brief 将WGS84坐标转换为矩阵索引
			 * @param wgs84_point WGS84坐标点
			 * @param row [输出] 矩阵行索引
			 * @param col [输出] 矩阵列索引
			 * @return 转换成功且索引在有效范围内返回true
			 * @note 此方法仅用于内部数据访问，不暴露给外部
			 */
			bool wgs84ToMatrixIndex(const WGS84Point& wgs84_point, int& row, int& col) const;

			// === 通用查询方法（消除重复代码） ===

			/**
			 * @brief 通用图层数据查询方法
			 * @tparam T 返回值类型
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @param layer_name 图层名称
			 * @param data_type_name 数据类型名称（用于日志）
			 * @return 查询结果，失败时返回std::nullopt
			 */
			template<typename T>
			std::optional<T> queryLayerValue(double latitude, double longitude,
				const std::string& layer_name, const std::string& data_type_name) const;

			// === 数据加载和处理方法 ===

			/**
			 * @brief 从GeoTIFF数据集加载数据到矩阵
			 * @param dataset GDAL数据集指针
			 * @param geoTransform 地理变换参数
			 * @return 加载成功返回true
			 */
			bool loadDataFromGeoTiff(GDALDataset* dataset, const double* geoTransform);

			/**
			 * @brief 填充地图元数据结构
			 * @param file_path 文件路径
			 * @param resolution 分辨率对（X方向、Y方向）
			 * @param width 栅格宽度
			 * @param height 栅格高度
			 * @param corners 四个角点坐标
			 */
			void populateMetadataFromGeoTiff(
				const std::string& file_path,
				const std::pair<double, double>& resolution,
				int width,
				int height,
				const std::array<WGS84Point, 4>& corners
			);

			// === PNG格式支持方法（用于缓存和快速加载） ===

			/**
			 * @brief 将元数据保存到PNG文件的元数据标签中
			 * @param dataset GDAL数据集指针
			 * @return 保存成功返回true
			 * @note 使用PNG的文本标签存储地图元数据信息
			 */
			bool saveMetadataToPng(GDALDataset* dataset);

			/**
			 * @brief 从PNG文件的元数据标签中加载元数据
			 * @param dataset GDAL数据集指针
			 * @return 加载成功返回true
			 * @note 从PNG的文本标签读取地图元数据信息
			 */
			bool loadMetadataFromPng(GDALDataset* dataset);

			/**
			 * @brief 将矩阵数据编码到PNG的像素数据中
			 * @param dataset GDAL数据集指针
			 * @return 编码成功返回true
			 * @note 使用自定义RGBA编码方案存储浮点数据
			 */
			bool saveMatrixDataToPng(GDALDataset* dataset);

			/**
			 * @brief 从PNG的像素数据中解码矩阵数据
			 * @param dataset GDAL数据集指针
			 * @return 解码成功返回true
			 * @note 从RGBA像素数据解码浮点数据
			 */
			bool loadMatrixDataFromPng(GDALDataset* dataset);

			// === PNG数据编码/解码辅助方法 ===

			/**
			 * @brief 将浮点高程值编码为RGBA像素
			 * @param elevation 高程值（米）
			 * @param r [输出] 红色分量
			 * @param g [输出] 绿色分量
			 * @param b [输出] 蓝色分量
			 * @param a [输出] 透明度分量
			 * @note 使用32位浮点数的字节表示进行编码
			 */
			void encodeElevationToRGBA(float elevation, uint8_t& r, uint8_t& g, uint8_t& b, uint8_t& a);

			/**
			 * @brief 从RGBA像素解码浮点高程值
			 * @param r 红色分量
			 * @param g 绿色分量
			 * @param b 蓝色分量
			 * @param a 透明度分量
			 * @return 解码的高程值（米）
			 * @note 从32位字节表示重构浮点数
			 */
			float decodeElevationFromRGBA(uint8_t r, uint8_t g, uint8_t b, uint8_t a);
		};

	} // namespace NSEnvironment
} // namespace NSDrones
