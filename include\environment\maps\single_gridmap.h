#pragma once

#include "environment/maps/igridmap.h"
#include "core/types.h"
#include "params/parameters.h"
#include <gdal_priv.h>
#include <GeographicLib/LocalCartesian.hpp>
#include <string>
#include <memory>
#include <vector>
#include <utility>
#include <optional>
#include <array>
#include <mutex>
#include <unordered_map>  // 添加缺失的头文件
#include "Eigen/Core"     // 添加Eigen头文件

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @class SingleGridMap
		 * @brief 单个地图文件的网格地图实现类
		 *
		 * 该类实现IGridMap接口，负责加载和管理单个地图文件（GeoTIFF、PNG等格式）。
		 * 提供基于WGS84坐标的高效地形和地物查询功能。
		 *
		 * 核心特性：
		 * - 多格式支持：GeoTIFF（地理配准）、PNG（自定义编码）
		 * - 高效存储：基于Eigen矩阵的内存优化数据结构
		 * - 坐标转换：WGS84 ↔ 局部笛卡尔坐标系的无缝转换
		 * - 多图层支持：高程、地物类型、地物高度等多种数据图层
		 * - 线程安全：所有查询操作都是线程安全的
		 *
		 * 坐标系统设计：
		 * - 外部接口：WGS84坐标系统（经度、纬度、高度）
		 * - 内部存储：局部笛卡尔坐标系统（以地图左上角为原点）
		 * - 数据组织：行列索引直接对应地理位置，支持O(1)查询
		 *
		 * 内存布局：
		 * - 数据矩阵：Eigen::MatrixXf，列优先存储
		 * - 坐标原点：地图左上角的WGS84坐标
		 * - 分辨率信息：X/Y方向的米/像素比例
		 *
		 * @note 所有几何运算通过GeometryManager进行，确保计算精度和一致性
		 * @note 线程安全通过读写锁实现，支持高并发查询
		 */
		class SingleGridMap : public IGridMap {
		public:
			// === 生命周期管理 ===

			/**
			 * @brief 默认构造函数
			 * 创建未初始化的地图对象，需要调用initialize()完成初始化
			 */
			SingleGridMap();

			/**
			 * @brief 析构函数
			 * 自动释放所有资源，包括GDAL数据集和坐标转换器
			 */
			~SingleGridMap() override;

			// === 拷贝和移动语义 ===
			SingleGridMap(const SingleGridMap&) = delete;              ///< 禁止拷贝构造，避免资源管理问题
			SingleGridMap& operator=(const SingleGridMap&) = delete;   ///< 禁止拷贝赋值，避免资源管理问题
			SingleGridMap(SingleGridMap&&) = default;                  ///< 允许移动构造，支持高效资源转移
			SingleGridMap& operator=(SingleGridMap&&) = default;       ///< 允许移动赋值，支持高效资源转移

			// === IGridMap接口实现 ===

			/**
			 * @brief 初始化地图数据源
			 * @param global_params 全局参数对象，包含地图文件路径和配置信息
			 * @return 初始化成功返回true，失败返回false
			 * @note 从global_params中读取map.file_path参数确定要加载的文件
			 * @note 根据文件扩展名自动选择加载模式（GeoTIFF或PNG）
			 */
			bool initialize(std::shared_ptr<NSParams::ParamValues> global_params) override;

			/**
			 * @brief 获取指定位置的地形高程
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 海拔高度（米），查询失败时返回std::nullopt
			 */
			std::optional<double> getElevation(double latitude, double longitude) const override;

			/**
			 * @brief 获取指定位置的地物类型
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 地物类型枚举，查询失败时返回std::nullopt
			 */
			std::optional<FeatureType> getFeature(double latitude, double longitude) const override;

			/**
			 * @brief 获取指定位置的地物高度
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 地物高度（米），查询失败时返回std::nullopt
			 */
			std::optional<double> getFeatureHeight(double latitude, double longitude) const override;

			/**
			 * @brief 检查地图是否已初始化
			 * @return 已初始化返回true，否则返回false
			 */
			bool isInitialized() const override;

			/**
			 * @brief 检查指定坐标是否在数据覆盖范围内
			 * @param latitude WGS84纬度（度）
			 * @param longitude WGS84经度（度）
			 * @return 在覆盖范围内且有有效数据返回true，否则返回false
			 */
			bool isCovered(double latitude, double longitude) const override;

			// === 扩展功能接口 ===

			/**
			 * @brief 直接从文件初始化地图（供TiledGridMap使用）
			 * @param file_path 地图文件路径
			 * @param load_mode 加载模式（"geotiff"或"png"）
			 * @return 初始化成功返回true，失败返回false
			 * @note 此方法不依赖全局参数，直接从指定文件加载
			 */
			bool initializeFromFile(const std::string& file_path, const std::string& load_mode = "geotiff");

			/**
			 * @brief 获取地图元数据信息
			 * @return 地图元数据结构
			 */
			MapMetadata getMetadata() const override;

			/**
			 * @brief 获取可用图层列表
			 * @return 图层名称列表
			 */
			std::vector<std::string> getAvailableLayers() const;

			// === 图层和数据访问接口 ===

			/**
			 * @brief 检查指定图层是否存在
			 * @param layer_name 图层名称
			 * @return 图层存在返回true，否则返回false
			 */
			bool hasLayer(const std::string& layer_name) const;

			/**
			 * @brief 获取指定图层的数据矩阵
			 * @param layer_name 图层名称
			 * @return 数据矩阵，图层不存在时返回std::nullopt
			 * @note 返回的矩阵是只读的，不应修改其内容
			 */
			std::optional<Eigen::MatrixXf> getLayerMatrix(const std::string& layer_name) const;

			// === 坐标转换接口 ===

			/**
			 * @brief 获取地图参考原点（局部坐标系原点）
			 * @return 地图左上角的WGS84坐标
			 */
			WGS84Point getReferenceOrigin() const;

			// === 文件I/O接口 ===

			/**
			 * @brief 保存地图数据到PNG文件
			 * @param output_path 输出文件路径
			 * @return 保存成功返回true，失败返回false
			 * @note PNG格式使用自定义编码保存高程和元数据信息
			 * @note 适用于缓存和快速加载场景
			 */
			bool saveToPng(const std::string& output_path);

		private:
			// --- 私有成员变量 ---
			// 基于Eigen矩阵的地图数据存储
			std::unordered_map<std::string, Eigen::MatrixXf> data_layers_; ///< 地图数据图层（图层名 -> 数据矩阵）
			MapMetadata metadata_; ///< 地图元数据

			// 局部坐标系相关
			WGS84Point map_origin_; ///< 地图原点（左上角）WGS84坐标
			std::shared_ptr<GeographicLib::LocalCartesian> local_cartesian_; ///< 局部笛卡尔坐标系转换器
			std::pair<double, double> native_resolution_; ///< 原始分辨率（X方向，Y方向）米/像素
			std::pair<int, int> grid_size_; ///< 网格尺寸（宽度，高度）像素数

			mutable std::mutex coordinate_mutex_; ///< 坐标转换的线程安全锁
			bool initialized_ = false; ///< 标志适配器是否成功初始化

			// --- 已实现的私有辅助函数 ---

			/**
			 * @brief 智能检测和设置图层名称（配置优先 + 自动回退）
			 * @param global_params 全局参数对象
			 * @return 检测成功返回 true（至少找到高程图层）
			 */
			bool detectAndSetLayers(std::shared_ptr<NSParams::ParamValues> global_params);

			/**
			 * @brief 计算地图分辨率（米/像素）
			 * @param geoTransform 地理变换参数
			 * @return 分辨率对 (X方向, Y方向)（米/像素）
			 */
			std::pair<double, double> calculateResolution(const double* geoTransform);

			/**
			 * @brief 计算GeoTIFF四个角点的WGS84坐标
			 * @param dataset GDAL数据集指针
			 * @param geoTransform 地理变换参数
			 * @return 四个角点的WGS84坐标数组
			 */
			std::array<WGS84Point, 4> calculateCorners(GDALDataset* dataset, const double* geoTransform);

			/**
			 * @brief 将WGS84坐标转换为矩阵索引
			 * @param wgs84_point WGS84坐标点
			 * @param row 输出：矩阵行索引
			 * @param col 输出：矩阵列索引
			 * @return 转换成功且索引在有效范围内返回true
			 */
			bool wgs84ToMatrixIndex(const WGS84Point& wgs84_point, int& row, int& col) const;

			/**
			 * @brief 从 GeoTIFF 数据集加载数据到矩阵
			 * @param dataset GDAL 数据集指针
			 * @param geoTransform 地理变换参数
			 * @return 加载成功返回 true
			 */
			bool loadDataFromGeoTiff(GDALDataset* dataset, const double* geoTransform);

			/**
			 * @brief 填充元数据结构
			 * @param file_path 文件路径
			 * @param resolution 分辨率对 (X方向, Y方向)
			 * @param width 栅格宽度
			 * @param height 栅格高度
			 * @param corners 四个角点坐标
			 */
			void populateMetadataFromGeoTiff(const std::string& file_path, const std::pair<double, double>& resolution,
											 int width, int height, const std::array<WGS84Point, 4>& corners);

			// --- PNG格式的元数据和矩阵数据编码/解码方法 ---
			/**
			 * @brief 将元数据保存到PNG文件的元数据标签中
			 * @param dataset GDAL数据集指针
			 * @return 保存成功返回true
			 */
			bool saveMetadataToPng(GDALDataset* dataset);

			/**
			 * @brief 从PNG文件的元数据标签中加载元数据
			 * @param dataset GDAL数据集指针
			 * @return 加载成功返回true
			 */
			bool loadMetadataFromPng(GDALDataset* dataset);

			/**
			 * @brief 将矩阵数据编码到PNG的像素数据中
			 * @param dataset GDAL数据集指针
			 * @return 编码成功返回true
			 */
			bool saveMatrixDataToPng(GDALDataset* dataset);

			/**
			 * @brief 从PNG的像素数据中解码矩阵数据
			 * @param dataset GDAL数据集指针
			 * @return 解码成功返回true
			 */
			bool loadMatrixDataFromPng(GDALDataset* dataset);

			/**
			 * @brief 将浮点高程值编码为RGBA像素
			 * @param elevation 高程值（米）
			 * @param r 输出：红色分量
			 * @param g 输出：绿色分量
			 * @param b 输出：蓝色分量
			 * @param a 输出：透明度分量
			 */
			void encodeElevationToRGBA(float elevation, uint8_t& r, uint8_t& g, uint8_t& b, uint8_t& a);

			/**
			 * @brief 从RGBA像素解码浮点高程值
			 * @param r 红色分量
			 * @param g 绿色分量
			 * @param b 蓝色分量
			 * @param a 透明度分量
			 * @return 解码的高程值（米）
			 */
			float decodeElevationFromRGBA(uint8_t r, uint8_t g, uint8_t b, uint8_t a);
		};

	} // namespace NSEnvironment
} // namespace NSDrones
