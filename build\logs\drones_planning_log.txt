[info]:[文件：logging.cpp，第89行，函数：NSDrones::Logger::init]: 
[0001] 日志系统初始化完成。日志文件: 'logs/drones_planning_log.txt', 覆盖模式: true, 控制台级别: trace, 文件级别: trace
[info]:[文件：main.cpp，第554行，函数：main]: 
[0002] 当前工作目录: E:\source\dronesplanning\build
[info]:[文件：main.cpp，第555行，函数：main]: 
[0003] 系统日志通过 NSDrones::Logger::init() 初始化成功。日志级别 Console: TRACE, File: TRACE
[trace]:[文件：main.cpp，第558行，函数：main]: 
[0004] Main: spdlog::flush_on(spdlog::level::err) 已设置。
[trace]:[文件：file_utils.cpp，第178行，函数：NSDrones::NSUtils::getExeDir]: 
[0005] 尝试获取可执行文件所在目录...
[trace]:[文件：file_utils.cpp，第125行，函数：NSDrones::NSUtils::getExePath]: 
[0006] 尝试获取可执行文件路径...
[debug]:[文件：file_utils.cpp，第138行，函数：NSDrones::NSUtils::getExePath]: 
[0007] 成功获取 Windows 可执行文件路径: E:/source/dronesplanning/build/Release/drones_planning.exe
[debug]:[文件：file_utils.cpp，第182行，函数：NSDrones::NSUtils::getExeDir]: 
[0008] 可执行文件所在目录: E:/source/dronesplanning/build/Release
[info]:[文件：main.cpp，第573行，函数：main]: 
[0009] 加载配置文件路径: E:\source\dronesplanning\build\Release\data\configfile.json
[info]:[文件：paramregistry.cpp，第26行，函数：NSDrones::NSParams::ParamRegistry::ParamRegistry]: 
[0010] ParamRegistry instance created at 0x7ff68be72e88.
[debug]:[文件：paramregistry.cpp，第28行，函数：NSDrones::NSParams::ParamRegistry::ParamRegistry]: 
[0011] ParamRegistry: ThreadSafeCache配置 - 最大存活时间: 10分钟, 最大条目数: 5000
[info]:[文件：main.cpp，第577行，函数：main]: 
[0012] ParamRegistry 单例已获取 (main)。
[info]:[文件：config.cpp，第44行，函数：NSDrones::Config::Config]: 
[0013] 正在使用配置文件 'E:\source\dronesplanning\build\Release\data\configfile.json' 初始化...
[info]:[文件：config.cpp，第159行，函数：NSDrones::Config::parseMainConfigAndResolvePaths]: 
[0014] 步骤 1/4 - 正在解析主配置文件并设定相关路径: 'E:/source/dronesplanning/build/Release/data/configfile.json'
[debug]:[文件：file_utils.cpp，第42行，函数：NSDrones::NSUtils::isFileExists]: 
[0015] 文件 E:/source/dronesplanning/build/Release/data/configfile.json 是否存在且为常规文件: true
[debug]:[文件：file_utils.cpp，第64行，函数：NSDrones::NSUtils::readFileToString]: 
[0016] 尝试读取文件: E:/source/dronesplanning/build/Release/data/configfile.json
[info]:[文件：file_utils.cpp，第94行，函数：NSDrones::NSUtils::readFileToString]: 
[0017] 成功读取文件: E:/source/dronesplanning/build/Release/data/configfile.json, 大小: 16538 字节
[debug]:[文件：config.cpp，第170行，函数：NSDrones::Config::parseMainConfigAndResolvePaths]: 
[0018] 成功读取主配置文件内容 (大小: 16538 字节)。
[debug]:[文件：config.cpp，第179行，函数：NSDrones::Config::parseMainConfigAndResolvePaths]: 
[0019] 主配置文件 JSON 内容成功解析。
[info]:[文件：config.cpp，第187行，函数：NSDrones::Config::parseMainConfigAndResolvePaths]: 
[0020] 数据根目录 (data_path_): 'E:/source/dronesplanning/build/Release/data'
[info]:[文件：config.cpp，第188行，函数：NSDrones::Config::parseMainConfigAndResolvePaths]: 
[0021] 参数定义目录 (defines_path_): 'E:/source/dronesplanning/build/Release/data/defines'
[info]:[文件：config.cpp，第189行，函数：NSDrones::Config::parseMainConfigAndResolvePaths]: 
[0022] 对象实例目录 (objects_path_): 'E:/source/dronesplanning/build/Release/data/objects'
[info]:[文件：config.cpp，第190行，函数：NSDrones::Config::parseMainConfigAndResolvePaths]: 
[0023] 地形数据目录 (terrain_path_): 'E:/source/dronesplanning/build/Release/data/terrain'
[info]:[文件：config.cpp，第192行，函数：NSDrones::Config::parseMainConfigAndResolvePaths]: 
[0024] 主配置文件解析和路径设定成功完成。
[debug]:[文件：config.cpp，第50行，函数：NSDrones::Config::Config]: 
[0025] 主配置文件解析完成。数据路径: 'E:\source\dronesplanning\build\Release\data', 定义路径: 'E:\source\dronesplanning\build\Release\data\defines', 对象路径: 'E:\source\dronesplanning\build\Release\data\objects'
[info]:[文件：config.cpp，第51行，函数：NSDrones::Config::Config]: 
[0026] Config instance created at 0x276802c5810. (Not yet initialized)
[info]:[文件：main.cpp，第582行，函数：main]: 
[0027] Main: Config 实例已创建。
[info]:[文件：config.cpp，第69行，函数：NSDrones::Config::initialize]: 
[0028] 开始核心初始化流程 (initialize)...⏳
[info]:[文件：config.cpp，第76行，函数：NSDrones::Config::initialize]: 
[0029] 步骤 0.1/5 - 正在初始化并注册所有枚举类型...
[info]:[文件：enum_utils.cpp，第65行，函数：NSDrones::NSUtils::initializeAndRegisterAllEnums]: 
[0030] 开始注册所有枚举类型...
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0031] 已为类型索引 enum NSDrones::NSCore::Status (来自类型 'Status') 映射到枚举名称 'NSDrones::NSCore::Status'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0032] 已为类型索引 enum NSDrones::NSCore::ErrorCode (来自类型 'ErrorCode') 映射到枚举名称 'NSDrones::NSCore::ErrorCode'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0033] 已为类型索引 enum NSDrones::NSCore::ShapeType (来自类型 'ShapeType') 映射到枚举名称 'NSDrones::NSCore::ShapeType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0034] 已为类型索引 enum NSDrones::NSCore::ZoneType (来自类型 'ZoneType') 映射到枚举名称 'NSDrones::NSCore::ZoneType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0035] 已为类型索引 enum NSDrones::NSCore::BuildingMaterialType (来自类型 'BuildingMaterialType') 映射到枚举名称 'NSDrones::NSCore::BuildingMaterialType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0036] 已为类型索引 enum NSDrones::NSCore::EnergyModelType (来自类型 'EnergyModelType') 映射到枚举名称 'NSDrones::NSCore::EnergyModelType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0037] 已为类型索引 enum NSDrones::NSCore::FlightControllerType (来自类型 'FlightControllerType') 映射到枚举名称 'NSDrones::NSCore::FlightControllerType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0038] 已为类型索引 enum NSDrones::NSCore::PropulsionType (来自类型 'PropulsionType') 映射到枚举名称 'NSDrones::NSCore::PropulsionType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0039] 已为类型索引 enum NSDrones::NSCore::FeatureType (来自类型 'FeatureType') 映射到枚举名称 'NSDrones::NSCore::FeatureType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0040] 已为类型索引 enum NSDrones::NSCore::AltitudeType (来自类型 'AltitudeType') 映射到枚举名称 'NSDrones::NSCore::AltitudeType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0041] 已为类型索引 enum NSDrones::NSCore::ControlPointType (来自类型 'ControlPointType') 映射到枚举名称 'NSDrones::NSCore::ControlPointType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0042] 已为类型索引 enum NSDrones::NSCore::TaskType (来自类型 'TaskType') 映射到枚举名称 'NSDrones::NSCore::TaskType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0043] 已为类型索引 enum NSDrones::NSCore::UavType (来自类型 'UavType') 映射到枚举名称 'NSDrones::NSCore::UavType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0044] 已为类型索引 enum NSDrones::NSCore::LogLevel (来自类型 'LogLevel') 映射到枚举名称 'NSDrones::NSCore::LogLevel'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0045] 已为类型索引 enum NSDrones::NSCore::SeverityType (来自类型 'SeverityType') 映射到枚举名称 'NSDrones::NSCore::SeverityType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0046] 已为类型索引 enum NSDrones::NSCore::MovementStrategyType (来自类型 'MovementStrategyType') 映射到枚举名称 'NSDrones::NSCore::MovementStrategyType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0047] 已为类型索引 enum NSDrones::NSPlanning::WarningType (来自类型 'WarningType') 映射到枚举名称 'NSDrones::NSPlanning::WarningType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0048] 已为类型索引 enum NSDrones::NSPlanning::PlanningStrategyType (来自类型 'PlanningStrategyType') 映射到枚举名称 'NSDrones::NSPlanning::PlanningStrategyType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0049] 已为类型索引 enum NSDrones::NSPlanning::PlannerType (来自类型 'PlannerType') 映射到枚举名称 'NSDrones::NSPlanning::PlannerType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0050] 已为类型索引 enum NSDrones::NSPlanning::CalculationFidelityType (来自类型 'CalculationFidelityType') 映射到枚举名称 'NSDrones::NSPlanning::CalculationFidelityType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0051] 已为类型索引 enum NSDrones::NSPlanning::TaskAllocationStrategyType (来自类型 'TaskAllocationStrategyType') 映射到枚举名称 'NSDrones::NSPlanning::TaskAllocationStrategyType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0052] 已为类型索引 enum NSDrones::NSPlanning::ScanPatternType (来自类型 'ScanPatternType') 映射到枚举名称 'NSDrones::NSPlanning::ScanPatternType'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0053] 已为类型索引 enum NSDrones::NSUav::FlightMode (来自类型 'FlightMode') 映射到枚举名称 'NSDrones::NSUav::FlightMode'
[trace]:[文件：enum_utils.h，第140行，函数：NSDrones::NSUtils::registerEnum]: 
[0054] 已为类型索引 enum NSDrones::NSUav::MissionStatus (来自类型 'MissionStatus') 映射到枚举名称 'NSDrones::NSUav::MissionStatus'
[info]:[文件：enum_utils.cpp，第98行，函数：NSDrones::NSUtils::initializeAndRegisterAllEnums]: 
[0055] 所有枚举类型注册完毕。
[info]:[文件：config.cpp，第78行，函数：NSDrones::Config::initialize]: 
[0056] 步骤 0.1/5 - 所有枚举类型已初始化并注册。
[info]:[文件：config.cpp，第80行，函数：NSDrones::Config::initialize]: 
[0057] 步骤 0.2/5 - 正在创建 Environment 实例...
[info]:[文件：environment.cpp，第85行，函数：NSDrones::NSEnvironment::Environment::Environment]: 
[0058] 环境: 创建环境实例，ThreadSafeCache统一缓存系统已初始化
[debug]:[文件：environment.cpp，第87行，函数：NSDrones::NSEnvironment::Environment::Environment]: 
[0059] 环境: ThreadSafeCache配置 - 最大存活时间: 5分钟, 最大条目数: 1000
[info]:[文件：environment.cpp，第52行，函数：NSDrones::NSEnvironment::Environment::createInstance]: 
[0060] Environment: 全局实例已创建
[info]:[文件：config.cpp，第92行，函数：NSDrones::Config::initialize]: 
[0061] 步骤 0.2/5 - Environment 实例创建成功。
[info]:[文件：config.cpp，第94行，函数：NSDrones::Config::initialize]: 
[0062] 步骤 1/5 - 正在加载全局参数定义...
[debug]:[文件：config.cpp，第727行，函数：NSDrones::Config::loadGlobalParamDefines]: 
[0063] Config::loadGlobalParamDefines - 开始加载嵌入的全局参数定义...
[info]:[文件：config.cpp，第741行，函数：NSDrones::Config::loadGlobalParamDefines]: 
[0064] 在主配置文件中找到 'global_parameter_definitions' 节点，尝试解析和注册全局参数定义...
[debug]:[文件：config.cpp，第745行，函数：NSDrones::Config::loadGlobalParamDefines]: 
[0065] Config::loadGlobalParamDefines - 调用 ParamRegistry::parseJsonToParamDefines...
[debug]:[文件：paramregistry.cpp，第76行，函数：NSDrones::NSParams::ParamRegistry::parseJsonToParamDefines]: 
[0066] ParamRegistry::parseJsonToParamDefines - 公共接口调用，type_tag_override: ''
[trace]:[文件：parameters.cpp，第208行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0067] ParamDefines::fromJson - 从 JSON 对象内部获取到 type_tag: 'global_environment_params'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0068] ParamDefines::fromJson - 开始为类型标签 'global_environment_params' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0069] ParamDefines::fromJson - 类型标签 'global_environment_params' 包含 50 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0070] ParamDefines instance created for type_tag 'global_environment_params' at 0xafb54fa1c8
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0071] ParamDefines::fromJson - 处理参数定义 key='default_mission_alt_msl' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0072] ParamDefines::fromJson - 处理参数定义 key='default_mission_speed_mps' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0073] ParamDefines::fromJson - 处理参数定义 key='default_mission_loiter_radius_m' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0074] ParamDefines::fromJson - 处理参数定义 key='default_mission_loiter_time_s' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0075] ParamDefines::fromJson - 处理参数定义 key='landing_approach_alt_msl' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0076] ParamDefines::fromJson - 处理参数定义 key='wgs84_origin' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0077] ParamDefines::fromJson - 处理参数定义 key='spatial_index.default_max_objects_per_node' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0078] ParamDefines::fromJson - 处理参数定义 key='spatial_index.default_max_depth' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0079] ParamDefines::fromJson - 处理参数定义 key='spatial_index.tile_size' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0080] ParamDefines::fromJson - 处理参数定义 key='logging_level' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0081] ParamDefines::fromJson - 处理参数定义 key='logging.console_level' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0082] ParamDefines::fromJson - 处理参数定义 key='logging.file_level' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0083] ParamDefines::fromJson - 处理参数定义 key='environment_cache_max_age_minutes' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0084] ParamDefines::fromJson - 处理参数定义 key='environment_cache_max_size' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0085] ParamDefines::fromJson - 处理参数定义 key='param_registry_cache_max_age_minutes' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0086] ParamDefines::fromJson - 处理参数定义 key='param_registry_cache_max_size' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0087] ParamDefines::fromJson - 处理参数定义 key='map.enabled' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0088] ParamDefines::fromJson - 处理参数定义 key='map.data_directory' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0089] ParamDefines::fromJson - 处理参数定义 key='map.file_path' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0090] ParamDefines::fromJson - 处理参数定义 key='spatial_index.map_bounds_min_x' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0091] ParamDefines::fromJson - 处理参数定义 key='spatial_index.map_bounds_min_y' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0092] ParamDefines::fromJson - 处理参数定义 key='spatial_index.map_bounds_min_z' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0093] ParamDefines::fromJson - 处理参数定义 key='spatial_index.map_bounds_max_x' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0094] ParamDefines::fromJson - 处理参数定义 key='spatial_index.map_bounds_max_y' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0095] ParamDefines::fromJson - 处理参数定义 key='spatial_index.map_bounds_max_z' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0096] ParamDefines::fromJson - 处理参数定义 key='map.load_mode' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0097] ParamDefines::fromJson - 处理参数定义 key='map.auto_convert' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0098] ParamDefines::fromJson - 处理参数定义 key='map.layer_config.elevation_layer' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0099] ParamDefines::fromJson - 处理参数定义 key='map.layer_config.feature_layer' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0100] ParamDefines::fromJson - 处理参数定义 key='map.layer_config.feature_height_layer' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0101] ParamDefines::fromJson - 处理参数定义 key='map.layer_mapping' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0102] ParamDefines::fromJson - 处理参数定义 key='task_spaces.takeoff_area.origin_latitude' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0103] ParamDefines::fromJson - 处理参数定义 key='task_spaces.takeoff_area.origin_longitude' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0104] ParamDefines::fromJson - 处理参数定义 key='task_spaces.takeoff_area.origin_altitude' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0105] ParamDefines::fromJson - 处理参数定义 key='task_spaces.takeoff_area.coverage_min_bound' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0106] ParamDefines::fromJson - 处理参数定义 key='task_spaces.takeoff_area.coverage_max_bound' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0107] ParamDefines::fromJson - 处理参数定义 key='task_spaces.takeoff_area.bvh_max_objects_per_node' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0108] ParamDefines::fromJson - 处理参数定义 key='task_spaces.takeoff_area.bvh_max_depth' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0109] ParamDefines::fromJson - 处理参数定义 key='task_spaces.formation_follow.leader_entity_id' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0110] ParamDefines::fromJson - 处理参数定义 key='task_spaces.formation_follow.coverage_min_bound' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0111] ParamDefines::fromJson - 处理参数定义 key='task_spaces.formation_follow.coverage_max_bound' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0112] ParamDefines::fromJson - 处理参数定义 key='task_spaces.formation_follow.bvh_max_objects_per_node' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0113] ParamDefines::fromJson - 处理参数定义 key='task_spaces.formation_follow.bvh_max_depth' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0114] ParamDefines::fromJson - 处理参数定义 key='task_spaces.target_area.origin_latitude' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0115] ParamDefines::fromJson - 处理参数定义 key='task_spaces.target_area.origin_longitude' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0116] ParamDefines::fromJson - 处理参数定义 key='task_spaces.target_area.origin_altitude' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0117] ParamDefines::fromJson - 处理参数定义 key='task_spaces.target_area.coverage_min_bound' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0118] ParamDefines::fromJson - 处理参数定义 key='task_spaces.target_area.coverage_max_bound' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0119] ParamDefines::fromJson - 处理参数定义 key='task_spaces.target_area.bvh_max_objects_per_node' (类型标签 'global_environment_params')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0120] ParamDefines::fromJson - 处理参数定义 key='task_spaces.target_area.bvh_max_depth' (类型标签 'global_environment_params')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0121] ParamDefines::fromJson - 类型标签 'global_environment_params' 的参数定义从 JSON 对象解析完成，共添加 50 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0122] ParamDefines instance move-constructed for type_tag 'global_environment_params' from 0xafb54fa1c8 to 0xafb54faec0 (50 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0123] ParamDefines instance destroyed for type_tag '' at 0xafb54fa1c8 (had 0 definitions)
[info]:[文件：paramregistry.cpp，第83行，函数：NSDrones::NSParams::ParamRegistry::parseJsonToParamDefines]: 
[0124] ParamRegistry::parseJsonToParamDefines - 成功从JSON对象为类型标签 'global_environment_params' 解析了ParamDefines。
[debug]:[文件：config.cpp，第747行，函数：NSDrones::Config::loadGlobalParamDefines]: 
[0125] Config::loadGlobalParamDefines - ParamRegistry::parseJsonToParamDefines 返回，has_value(): true
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0126] ParamDefines instance move-constructed for type_tag 'global_environment_params' from 0xafb54faec0 to 0xafb54faf40 (50 definitions)
[info]:[文件：config.cpp，第754行，函数：NSDrones::Config::loadGlobalParamDefines]: 
[0127] 成功从 'global_parameter_definitions' 节点解析了类型标签为 'global_environment_params' 的参数定义 (共 50 个定义)。正在注册...
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0128] ParamDefines instance move-constructed for type_tag 'global_environment_params' from 0xafb54faf40 to 0xafb54faf78 (50 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0129] 正在为类型标签 'global_environment_params' 注册 ParamDefines 对象 (包含 50 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0130] ParamDefines instance move-constructed for type_tag 'global_environment_params' from 0xafb54faf78 to 0xafb54fab70 (50 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0131] ParamDefines instance move-constructed for type_tag 'global_environment_params' from 0xafb54fab70 to 0x276802db438 (50 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0132] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0133] ParamDefines instance move-constructed for type_tag 'global_environment_params' from 0x276802db438 to 0x276802c8f60 (50 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0134] ParamRegistry: 成功注册类型标签 'global_environment_params' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0135] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0136] ParamDefines instance destroyed for type_tag '' at 0x276802db438 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0137] ParamDefines instance destroyed for type_tag '' at 0xafb54fab70 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0138] ParamDefines instance destroyed for type_tag '' at 0xafb54faf78 (had 0 definitions)
[info]:[文件：config.cpp，第763行，函数：NSDrones::Config::loadGlobalParamDefines]: 
[0139] 成功注册了从 'global_parameter_definitions' 解析的全局参数定义 (类型标签 'global_environment_params')。
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0140] ParamDefines instance destroyed for type_tag '' at 0xafb54faf40 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0141] ParamDefines instance destroyed for type_tag '' at 0xafb54faec0 (had 0 definitions)
[info]:[文件：config.cpp，第99行，函数：NSDrones::Config::initialize]: 
[0142] 步骤 1/5 - 全局参数定义加载完成。
[info]:[文件：config.cpp，第101行，函数：NSDrones::Config::initialize]: 
[0143] 步骤 2/5 - 正在加载 'defines' 目录参数定义...
[info]:[文件：config.cpp，第664行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0144] 开始加载 'E:/source/dronesplanning/build/Release/data/defines' 目录下的所有参数定义文件...
[debug]:[文件：config.cpp，第675行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0145] 参数定义目录 'E:/source/dronesplanning/build/Release/data/defines' 有效，开始遍历JSON文件...
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0146] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.json', 将使用类型标签 'AlgorithmObject' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0147] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0148] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0149] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0150] ParamDefines::fromJson - 类型标签 'AlgorithmObject' 包含 2 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0151] ParamDefines instance created for type_tag 'AlgorithmObject' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0152] ParamDefines::fromJson - 处理参数定义 key='name' (类型标签 'AlgorithmObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0153] ParamDefines::fromJson - 处理参数定义 key='version' (类型标签 'AlgorithmObject')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0154] ParamDefines::fromJson - 类型标签 'AlgorithmObject' 的参数定义从 JSON 对象解析完成，共添加 2 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0155] ParamDefines instance move-constructed for type_tag 'AlgorithmObject' from 0xafb54f9c58 to 0xafb54fa3a0 (2 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0156] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0157] ParamDefines instance move-constructed for type_tag 'AlgorithmObject' from 0xafb54fa3a0 to 0xafb54fa430 (2 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0158] 正在为类型标签 'AlgorithmObject' 注册 ParamDefines 对象 (包含 2 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0159] ParamDefines instance move-constructed for type_tag 'AlgorithmObject' from 0xafb54fa430 to 0xafb54fa060 (2 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0160] ParamDefines instance move-constructed for type_tag 'AlgorithmObject' from 0xafb54fa060 to 0x276802db6d8 (2 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0161] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0162] ParamDefines instance move-constructed for type_tag 'AlgorithmObject' from 0x276802db6d8 to 0x276802c9140 (2 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0163] ParamRegistry: 成功注册类型标签 'AlgorithmObject' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0164] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0165] ParamDefines instance destroyed for type_tag '' at 0x276802db6d8 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0166] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0167] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0168] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0169] 成功注册了类型标签 'AlgorithmObject' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0170] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.PathPlanner.json', 将使用类型标签 'AlgorithmObject.PathPlanner' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0171] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.PathPlanner.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0172] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject.PathPlanner'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0173] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject.PathPlanner' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0174] ParamDefines::fromJson - 类型标签 'AlgorithmObject.PathPlanner' 包含 3 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0175] ParamDefines instance created for type_tag 'AlgorithmObject.PathPlanner' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0176] ParamDefines::fromJson - 处理参数定义 key='planning_timeout_ms' (类型标签 'AlgorithmObject.PathPlanner')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0177] ParamDefines::fromJson - 处理参数定义 key='collision_check_resolution' (类型标签 'AlgorithmObject.PathPlanner')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0178] ParamDefines::fromJson - 处理参数定义 key='enable_smoothing' (类型标签 'AlgorithmObject.PathPlanner')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0179] ParamDefines::fromJson - 类型标签 'AlgorithmObject.PathPlanner' 的参数定义从 JSON 对象解析完成，共添加 3 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0180] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner' from 0xafb54f9c58 to 0xafb54fa3a0 (3 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0181] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0182] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner' from 0xafb54fa3a0 to 0xafb54fa430 (3 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0183] 正在为类型标签 'AlgorithmObject.PathPlanner' 注册 ParamDefines 对象 (包含 3 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0184] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner' from 0xafb54fa430 to 0xafb54fa060 (3 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0185] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner' from 0xafb54fa060 to 0x276802eeaa8 (3 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0186] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0187] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner' from 0x276802eeaa8 to 0x276802c9460 (3 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0188] ParamRegistry: 成功注册类型标签 'AlgorithmObject.PathPlanner' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0189] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0190] ParamDefines instance destroyed for type_tag '' at 0x276802eeaa8 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0191] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0192] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0193] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0194] 成功注册了类型标签 'AlgorithmObject.PathPlanner' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.PathPlanner.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0195] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.PathPlanner.RRTStar.json', 将使用类型标签 'AlgorithmObject.PathPlanner.RRTStar' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0196] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.PathPlanner.RRTStar.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0197] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject.PathPlanner.RRTStar'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0198] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject.PathPlanner.RRTStar' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0199] ParamDefines::fromJson - 类型标签 'AlgorithmObject.PathPlanner.RRTStar' 包含 6 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0200] ParamDefines instance created for type_tag 'AlgorithmObject.PathPlanner.RRTStar' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0201] ParamDefines::fromJson - 处理参数定义 key='max_iterations' (类型标签 'AlgorithmObject.PathPlanner.RRTStar')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0202] ParamDefines::fromJson - 处理参数定义 key='step_size_m' (类型标签 'AlgorithmObject.PathPlanner.RRTStar')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0203] ParamDefines::fromJson - 处理参数定义 key='goal_bias' (类型标签 'AlgorithmObject.PathPlanner.RRTStar')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0204] ParamDefines::fromJson - 处理参数定义 key='rewire_radius_factor' (类型标签 'AlgorithmObject.PathPlanner.RRTStar')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0205] ParamDefines::fromJson - 处理参数定义 key='collision_check_resolution_m' (类型标签 'AlgorithmObject.PathPlanner.RRTStar')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0206] ParamDefines::fromJson - 处理参数定义 key='smoothing_iterations' (类型标签 'AlgorithmObject.PathPlanner.RRTStar')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0207] ParamDefines::fromJson - 类型标签 'AlgorithmObject.PathPlanner.RRTStar' 的参数定义从 JSON 对象解析完成，共添加 6 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0208] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner.RRTStar' from 0xafb54f9c58 to 0xafb54fa3a0 (6 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0209] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0210] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner.RRTStar' from 0xafb54fa3a0 to 0xafb54fa430 (6 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0211] 正在为类型标签 'AlgorithmObject.PathPlanner.RRTStar' 注册 ParamDefines 对象 (包含 6 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0212] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner.RRTStar' from 0xafb54fa430 to 0xafb54fa060 (6 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0213] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner.RRTStar' from 0xafb54fa060 to 0x276802efbe8 (6 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0214] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0215] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.PathPlanner.RRTStar' from 0x276802efbe8 to 0x276802c92d0 (6 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0216] ParamRegistry: 成功注册类型标签 'AlgorithmObject.PathPlanner.RRTStar' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0217] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0218] ParamDefines instance destroyed for type_tag '' at 0x276802efbe8 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0219] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0220] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0221] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0222] 成功注册了类型标签 'AlgorithmObject.PathPlanner.RRTStar' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.PathPlanner.RRTStar.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0223] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TaskAllocator.json', 将使用类型标签 'AlgorithmObject.TaskAllocator' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0224] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.TaskAllocator.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0225] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject.TaskAllocator'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0226] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject.TaskAllocator' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0227] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TaskAllocator' 包含 3 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0228] ParamDefines instance created for type_tag 'AlgorithmObject.TaskAllocator' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0229] ParamDefines::fromJson - 处理参数定义 key='allocation_strategy' (类型标签 'AlgorithmObject.TaskAllocator')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0230] ParamDefines::fromJson - 处理参数定义 key='max_allocation_time_ms' (类型标签 'AlgorithmObject.TaskAllocator')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0231] ParamDefines::fromJson - 处理参数定义 key='enable_reallocation' (类型标签 'AlgorithmObject.TaskAllocator')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0232] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TaskAllocator' 的参数定义从 JSON 对象解析完成，共添加 3 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0233] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator' from 0xafb54f9c58 to 0xafb54fa3a0 (3 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0234] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0235] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator' from 0xafb54fa3a0 to 0xafb54fa430 (3 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0236] 正在为类型标签 'AlgorithmObject.TaskAllocator' 注册 ParamDefines 对象 (包含 3 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0237] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator' from 0xafb54fa430 to 0xafb54fa060 (3 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0238] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator' from 0xafb54fa060 to 0x276802edf68 (3 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0239] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0240] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator' from 0x276802edf68 to 0x276802c94b0 (3 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0241] ParamRegistry: 成功注册类型标签 'AlgorithmObject.TaskAllocator' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0242] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0243] ParamDefines instance destroyed for type_tag '' at 0x276802edf68 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0244] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0245] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0246] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0247] 成功注册了类型标签 'AlgorithmObject.TaskAllocator' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TaskAllocator.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0248] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TaskAllocator.Simple.json', 将使用类型标签 'AlgorithmObject.TaskAllocator.Simple' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0249] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.TaskAllocator.Simple.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0250] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject.TaskAllocator.Simple'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0251] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject.TaskAllocator.Simple' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0252] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TaskAllocator.Simple' 包含 0 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0253] ParamDefines instance created for type_tag 'AlgorithmObject.TaskAllocator.Simple' at 0xafb54f9c58
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0254] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TaskAllocator.Simple' 的参数定义从 JSON 对象解析完成，共添加 0 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0255] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator.Simple' from 0xafb54f9c58 to 0xafb54fa3a0 (0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0256] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0257] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator.Simple' from 0xafb54fa3a0 to 0xafb54fa430 (0 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0258] 正在为类型标签 'AlgorithmObject.TaskAllocator.Simple' 注册 ParamDefines 对象 (包含 0 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0259] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator.Simple' from 0xafb54fa430 to 0xafb54fa060 (0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0260] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator.Simple' from 0xafb54fa060 to 0x276802eee68 (0 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0261] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0262] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TaskAllocator.Simple' from 0x276802eee68 to 0x276802c91e0 (0 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0263] ParamRegistry: 成功注册类型标签 'AlgorithmObject.TaskAllocator.Simple' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0264] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0265] ParamDefines instance destroyed for type_tag '' at 0x276802eee68 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0266] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0267] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0268] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0269] 成功注册了类型标签 'AlgorithmObject.TaskAllocator.Simple' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TaskAllocator.Simple.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0270] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TrajectoryEvaluator.Energy.json', 将使用类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0271] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.TrajectoryEvaluator.Energy.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0272] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject.TrajectoryEvaluator.Energy'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0273] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0274] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy' 包含 3 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0275] ParamDefines instance created for type_tag 'AlgorithmObject.TrajectoryEvaluator.Energy' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0276] ParamDefines::fromJson - 处理参数定义 key='calculation_fidelity' (类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0277] ParamDefines::fromJson - 处理参数定义 key='simple_model_hover_w' (类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0278] ParamDefines::fromJson - 处理参数定义 key='simple_model_forward_coeff' (类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0279] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy' 的参数定义从 JSON 对象解析完成，共添加 3 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0280] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator.Energy' from 0xafb54f9c58 to 0xafb54fa3a0 (3 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0281] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0282] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator.Energy' from 0xafb54fa3a0 to 0xafb54fa430 (3 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0283] 正在为类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy' 注册 ParamDefines 对象 (包含 3 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0284] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator.Energy' from 0xafb54fa430 to 0xafb54fa060 (3 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0285] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator.Energy' from 0xafb54fa060 to 0x276802ee568 (3 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0286] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0287] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator.Energy' from 0x276802ee568 to 0x276802c9230 (3 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0288] ParamRegistry: 成功注册类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0289] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0290] ParamDefines instance destroyed for type_tag '' at 0x276802ee568 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0291] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0292] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0293] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0294] 成功注册了类型标签 'AlgorithmObject.TrajectoryEvaluator.Energy' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TrajectoryEvaluator.Energy.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0295] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TrajectoryEvaluator.json', 将使用类型标签 'AlgorithmObject.TrajectoryEvaluator' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0296] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.TrajectoryEvaluator.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0297] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject.TrajectoryEvaluator'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0298] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject.TrajectoryEvaluator' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0299] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TrajectoryEvaluator' 包含 4 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0300] ParamDefines instance created for type_tag 'AlgorithmObject.TrajectoryEvaluator' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0301] ParamDefines::fromJson - 处理参数定义 key='evaluation_resolution' (类型标签 'AlgorithmObject.TrajectoryEvaluator')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0302] ParamDefines::fromJson - 处理参数定义 key='weight_energy' (类型标签 'AlgorithmObject.TrajectoryEvaluator')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0303] ParamDefines::fromJson - 处理参数定义 key='weight_time' (类型标签 'AlgorithmObject.TrajectoryEvaluator')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0304] ParamDefines::fromJson - 处理参数定义 key='weight_safety' (类型标签 'AlgorithmObject.TrajectoryEvaluator')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0305] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TrajectoryEvaluator' 的参数定义从 JSON 对象解析完成，共添加 4 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0306] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator' from 0xafb54f9c58 to 0xafb54fa3a0 (4 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0307] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0308] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator' from 0xafb54fa3a0 to 0xafb54fa430 (4 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0309] 正在为类型标签 'AlgorithmObject.TrajectoryEvaluator' 注册 ParamDefines 对象 (包含 4 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0310] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator' from 0xafb54fa430 to 0xafb54fa060 (4 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0311] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator' from 0xafb54fa060 to 0x276802eeb08 (4 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0312] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0313] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryEvaluator' from 0x276802eeb08 to 0x276802c9370 (4 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0314] ParamRegistry: 成功注册类型标签 'AlgorithmObject.TrajectoryEvaluator' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0315] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0316] ParamDefines instance destroyed for type_tag '' at 0x276802eeb08 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0317] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0318] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0319] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0320] 成功注册了类型标签 'AlgorithmObject.TrajectoryEvaluator' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TrajectoryEvaluator.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0321] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TrajectoryOptimizer.BSpline.json', 将使用类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0322] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.TrajectoryOptimizer.BSpline.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0323] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject.TrajectoryOptimizer.BSpline'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0324] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0325] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline' 包含 9 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0326] ParamDefines instance created for type_tag 'AlgorithmObject.TrajectoryOptimizer.BSpline' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0327] ParamDefines::fromJson - 处理参数定义 key='order' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0328] ParamDefines::fromJson - 处理参数定义 key='output_points' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0329] ParamDefines::fromJson - 处理参数定义 key='max_iterations' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0330] ParamDefines::fromJson - 处理参数定义 key='learning_rate' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0331] ParamDefines::fromJson - 处理参数定义 key='tolerance' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0332] ParamDefines::fromJson - 处理参数定义 key='collision_weight' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0333] ParamDefines::fromJson - 处理参数定义 key='safe_distance' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0334] ParamDefines::fromJson - 处理参数定义 key='sample_points' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0335] ParamDefines::fromJson - 处理参数定义 key='cost_weights.energy' (类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0336] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline' 的参数定义从 JSON 对象解析完成，共添加 9 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0337] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer.BSpline' from 0xafb54f9c58 to 0xafb54fa3a0 (9 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0338] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0339] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer.BSpline' from 0xafb54fa3a0 to 0xafb54fa430 (9 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0340] 正在为类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline' 注册 ParamDefines 对象 (包含 9 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0341] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer.BSpline' from 0xafb54fa430 to 0xafb54fa060 (9 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0342] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer.BSpline' from 0xafb54fa060 to 0x276802db438 (9 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0343] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0344] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer.BSpline' from 0x276802db438 to 0x276802f5670 (9 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0345] ParamRegistry: 成功注册类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0346] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0347] ParamDefines instance destroyed for type_tag '' at 0x276802db438 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0348] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0349] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0350] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0351] 成功注册了类型标签 'AlgorithmObject.TrajectoryOptimizer.BSpline' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TrajectoryOptimizer.BSpline.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0352] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TrajectoryOptimizer.json', 将使用类型标签 'AlgorithmObject.TrajectoryOptimizer' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0353] 文件 'E:\source\dronesplanning\build\Release\data\defines\AlgorithmObject.TrajectoryOptimizer.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0354] 从Json对象注册参数定义，类型标签为: 'AlgorithmObject.TrajectoryOptimizer'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0355] ParamDefines::fromJson - 开始为类型标签 'AlgorithmObject.TrajectoryOptimizer' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0356] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TrajectoryOptimizer' 包含 3 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0357] ParamDefines instance created for type_tag 'AlgorithmObject.TrajectoryOptimizer' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0358] ParamDefines::fromJson - 处理参数定义 key='optimization_timeout_ms' (类型标签 'AlgorithmObject.TrajectoryOptimizer')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0359] ParamDefines::fromJson - 处理参数定义 key='convergence_tolerance' (类型标签 'AlgorithmObject.TrajectoryOptimizer')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0360] ParamDefines::fromJson - 处理参数定义 key='enable_collision_avoidance' (类型标签 'AlgorithmObject.TrajectoryOptimizer')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0361] ParamDefines::fromJson - 类型标签 'AlgorithmObject.TrajectoryOptimizer' 的参数定义从 JSON 对象解析完成，共添加 3 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0362] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer' from 0xafb54f9c58 to 0xafb54fa3a0 (3 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0363] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0364] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer' from 0xafb54fa3a0 to 0xafb54fa430 (3 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0365] 正在为类型标签 'AlgorithmObject.TrajectoryOptimizer' 注册 ParamDefines 对象 (包含 3 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0366] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer' from 0xafb54fa430 to 0xafb54fa060 (3 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0367] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer' from 0xafb54fa060 to 0x276802ee088 (3 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0368] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0369] ParamDefines instance move-constructed for type_tag 'AlgorithmObject.TrajectoryOptimizer' from 0x276802ee088 to 0x276802f4ea0 (3 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0370] ParamRegistry: 成功注册类型标签 'AlgorithmObject.TrajectoryOptimizer' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0371] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0372] ParamDefines instance destroyed for type_tag '' at 0x276802ee088 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0373] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0374] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0375] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0376] 成功注册了类型标签 'AlgorithmObject.TrajectoryOptimizer' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/AlgorithmObject.TrajectoryOptimizer.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0377] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.json', 将使用类型标签 'EntityObject' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0378] 文件 'E:\source\dronesplanning\build\Release\data\defines\EntityObject.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0379] 从Json对象注册参数定义，类型标签为: 'EntityObject'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0380] ParamDefines::fromJson - 开始为类型标签 'EntityObject' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0381] ParamDefines::fromJson - 类型标签 'EntityObject' 包含 15 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0382] ParamDefines instance created for type_tag 'EntityObject' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0383] ParamDefines::fromJson - 处理参数定义 key='instance_name_override' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0384] ParamDefines::fromJson - 处理参数定义 key='initial_position_ned' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0385] ParamDefines::fromJson - 处理参数定义 key='initial_position_wgs84' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0386] ParamDefines::fromJson - 处理参数定义 key='initial_orientation_ypr_deg' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0387] ParamDefines::fromJson - 处理参数定义 key='shape_type' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0388] ParamDefines::fromJson - 处理参数定义 key='shape_offset_ned' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0389] ParamDefines::fromJson - 处理参数定义 key='shape_orientation_ypr_deg' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0390] ParamDefines::fromJson - 处理参数定义 key='shape_box_size_xyz' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0391] ParamDefines::fromJson - 处理参数定义 key='shape_cylinder_radius_m' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0392] ParamDefines::fromJson - 处理参数定义 key='shape_cylinder_height_m' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0393] ParamDefines::fromJson - 处理参数定义 key='shape_sphere_radius_m' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0394] ParamDefines::fromJson - 处理参数定义 key='shape_polygon_vertices_ned' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0395] ParamDefines::fromJson - 处理参数定义 key='shape_polygon_min_alt_msl' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0396] ParamDefines::fromJson - 处理参数定义 key='shape_polygon_max_alt_msl' (类型标签 'EntityObject')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0397] ParamDefines::fromJson - 处理参数定义 key='movement_strategy_type' (类型标签 'EntityObject')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0398] ParamDefines::fromJson - 类型标签 'EntityObject' 的参数定义从 JSON 对象解析完成，共添加 15 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0399] ParamDefines instance move-constructed for type_tag 'EntityObject' from 0xafb54f9c58 to 0xafb54fa3a0 (15 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0400] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0401] ParamDefines instance move-constructed for type_tag 'EntityObject' from 0xafb54fa3a0 to 0xafb54fa430 (15 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0402] 正在为类型标签 'EntityObject' 注册 ParamDefines 对象 (包含 15 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0403] ParamDefines instance move-constructed for type_tag 'EntityObject' from 0xafb54fa430 to 0xafb54fa060 (15 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0404] ParamDefines instance move-constructed for type_tag 'EntityObject' from 0xafb54fa060 to 0x276802fdcf8 (15 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0405] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0406] ParamDefines instance move-constructed for type_tag 'EntityObject' from 0x276802fdcf8 to 0x276802f59e0 (15 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0407] ParamRegistry: 成功注册类型标签 'EntityObject' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0408] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0409] ParamDefines instance destroyed for type_tag '' at 0x276802fdcf8 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0410] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0411] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0412] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0413] 成功注册了类型标签 'EntityObject' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0414] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Obstacle.Building.json', 将使用类型标签 'EntityObject.Obstacle.Building' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0415] 文件 'E:\source\dronesplanning\build\Release\data\defines\EntityObject.Obstacle.Building.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0416] 从Json对象注册参数定义，类型标签为: 'EntityObject.Obstacle.Building'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0417] ParamDefines::fromJson - 开始为类型标签 'EntityObject.Obstacle.Building' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0418] ParamDefines::fromJson - 类型标签 'EntityObject.Obstacle.Building' 包含 2 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0419] ParamDefines instance created for type_tag 'EntityObject.Obstacle.Building' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0420] ParamDefines::fromJson - 处理参数定义 key='building_material' (类型标签 'EntityObject.Obstacle.Building')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0421] ParamDefines::fromJson - 处理参数定义 key='is_destructible' (类型标签 'EntityObject.Obstacle.Building')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0422] ParamDefines::fromJson - 类型标签 'EntityObject.Obstacle.Building' 的参数定义从 JSON 对象解析完成，共添加 2 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0423] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle.Building' from 0xafb54f9c58 to 0xafb54fa3a0 (2 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0424] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0425] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle.Building' from 0xafb54fa3a0 to 0xafb54fa430 (2 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0426] 正在为类型标签 'EntityObject.Obstacle.Building' 注册 ParamDefines 对象 (包含 2 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0427] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle.Building' from 0xafb54fa430 to 0xafb54fa060 (2 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0428] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle.Building' from 0xafb54fa060 to 0x276802fcdf8 (2 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0429] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0430] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle.Building' from 0x276802fcdf8 to 0x276802f5080 (2 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0431] ParamRegistry: 成功注册类型标签 'EntityObject.Obstacle.Building' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0432] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0433] ParamDefines instance destroyed for type_tag '' at 0x276802fcdf8 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0434] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0435] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0436] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0437] 成功注册了类型标签 'EntityObject.Obstacle.Building' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Obstacle.Building.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0438] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Obstacle.json', 将使用类型标签 'EntityObject.Obstacle' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0439] 文件 'E:\source\dronesplanning\build\Release\data\defines\EntityObject.Obstacle.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0440] 从Json对象注册参数定义，类型标签为: 'EntityObject.Obstacle'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0441] ParamDefines::fromJson - 开始为类型标签 'EntityObject.Obstacle' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0442] ParamDefines::fromJson - 类型标签 'EntityObject.Obstacle' 包含 0 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0443] ParamDefines instance created for type_tag 'EntityObject.Obstacle' at 0xafb54f9c58
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0444] ParamDefines::fromJson - 类型标签 'EntityObject.Obstacle' 的参数定义从 JSON 对象解析完成，共添加 0 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0445] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle' from 0xafb54f9c58 to 0xafb54fa3a0 (0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0446] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0447] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle' from 0xafb54fa3a0 to 0xafb54fa430 (0 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0448] 正在为类型标签 'EntityObject.Obstacle' 注册 ParamDefines 对象 (包含 0 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0449] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle' from 0xafb54fa430 to 0xafb54fa060 (0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0450] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle' from 0xafb54fa060 to 0x276802fdbd8 (0 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0451] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0452] ParamDefines instance move-constructed for type_tag 'EntityObject.Obstacle' from 0x276802fdbd8 to 0x276802f5440 (0 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0453] ParamRegistry: 成功注册类型标签 'EntityObject.Obstacle' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0454] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0455] ParamDefines instance destroyed for type_tag '' at 0x276802fdbd8 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0456] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0457] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0458] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0459] 成功注册了类型标签 'EntityObject.Obstacle' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Obstacle.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0460] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Uav.FixedWing.json', 将使用类型标签 'EntityObject.Uav.FixedWing' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0461] 文件 'E:\source\dronesplanning\build\Release\data\defines\EntityObject.Uav.FixedWing.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0462] 从Json对象注册参数定义，类型标签为: 'EntityObject.Uav.FixedWing'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0463] ParamDefines::fromJson - 开始为类型标签 'EntityObject.Uav.FixedWing' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0464] ParamDefines::fromJson - 类型标签 'EntityObject.Uav.FixedWing' 包含 11 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0465] ParamDefines instance created for type_tag 'EntityObject.Uav.FixedWing' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0466] ParamDefines::fromJson - 处理参数定义 key='aero.wing_span_m' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0467] ParamDefines::fromJson - 处理参数定义 key='aero.wing_area_m2' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0468] ParamDefines::fromJson - 处理参数定义 key='aero.min_airspeed_mps' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0469] ParamDefines::fromJson - 处理参数定义 key='aero.Cl_alpha' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0470] ParamDefines::fromJson - 处理参数定义 key='aero.induced_drag_coeff' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0471] ParamDefines::fromJson - 处理参数定义 key='propulsion.type' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0472] ParamDefines::fromJson - 处理参数定义 key='propulsion.max_thrust_N' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0473] ParamDefines::fromJson - 处理参数定义 key='energy.consumption_cruise_power_coeff' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0474] ParamDefines::fromJson - 处理参数定义 key='aero.aspect_ratio' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0475] ParamDefines::fromJson - 处理参数定义 key='aero.drag_coeff_zero_lift' (类型标签 'EntityObject.Uav.FixedWing')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0476] ParamDefines::fromJson - 处理参数定义 key='aero.stall_angle_deg' (类型标签 'EntityObject.Uav.FixedWing')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0477] ParamDefines::fromJson - 类型标签 'EntityObject.Uav.FixedWing' 的参数定义从 JSON 对象解析完成，共添加 11 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0478] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.FixedWing' from 0xafb54f9c58 to 0xafb54fa3a0 (11 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0479] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0480] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.FixedWing' from 0xafb54fa3a0 to 0xafb54fa430 (11 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0481] 正在为类型标签 'EntityObject.Uav.FixedWing' 注册 ParamDefines 对象 (包含 11 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0482] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.FixedWing' from 0xafb54fa430 to 0xafb54fa060 (11 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0483] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.FixedWing' from 0xafb54fa060 to 0x276802ef108 (11 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0484] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0485] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.FixedWing' from 0x276802ef108 to 0x276802f52b0 (11 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0486] ParamRegistry: 成功注册类型标签 'EntityObject.Uav.FixedWing' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0487] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0488] ParamDefines instance destroyed for type_tag '' at 0x276802ef108 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0489] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0490] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0491] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0492] 成功注册了类型标签 'EntityObject.Uav.FixedWing' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Uav.FixedWing.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0493] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Uav.json', 将使用类型标签 'EntityObject.Uav' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0494] 文件 'E:\source\dronesplanning\build\Release\data\defines\EntityObject.Uav.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0495] 从Json对象注册参数定义，类型标签为: 'EntityObject.Uav'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0496] ParamDefines::fromJson - 开始为类型标签 'EntityObject.Uav' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0497] ParamDefines::fromJson - 类型标签 'EntityObject.Uav' 包含 25 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0498] ParamDefines instance created for type_tag 'EntityObject.Uav' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0499] ParamDefines::fromJson - 处理参数定义 key='physical.empty_weight' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0500] ParamDefines::fromJson - 处理参数定义 key='physical.max_payload_weight' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0501] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.maxHVel' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0502] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.maxHAcc' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0503] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.maxVVelUp' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0504] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.maxVVelDown' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0505] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.maxVAcc' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0506] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.maxAlt' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0507] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.maxHDecel' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0508] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.maxVDecel' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0509] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.minOpSpeed' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0510] ParamDefines::fromJson - 处理参数定义 key='dynamics.base.max_yaw_rate_dps' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0511] ParamDefines::fromJson - 处理参数定义 key='energy.model_type' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0512] ParamDefines::fromJson - 处理参数定义 key='energy.initial_battery_level_wh' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0513] ParamDefines::fromJson - 处理参数定义 key='energy.base.max_capacity' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0514] ParamDefines::fromJson - 处理参数定义 key='energy.base.min_safe_fraction' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0515] ParamDefines::fromJson - 处理参数定义 key='energy.base.charging_efficiency' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0516] ParamDefines::fromJson - 处理参数定义 key='energy.base.discharging_efficiency' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0517] ParamDefines::fromJson - 处理参数定义 key='energy.base.baseline_power' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0518] ParamDefines::fromJson - 处理参数定义 key='payload.supported_types' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0519] ParamDefines::fromJson - 处理参数定义 key='payloads' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0520] ParamDefines::fromJson - 处理参数定义 key='flight_controller.type' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0521] ParamDefines::fromJson - 处理参数定义 key='aero.wing_span_m' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0522] ParamDefines::fromJson - 处理参数定义 key='physics.mass_kg' (类型标签 'EntityObject.Uav')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0523] ParamDefines::fromJson - 处理参数定义 key='limits.max_speed_mps' (类型标签 'EntityObject.Uav')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0524] ParamDefines::fromJson - 类型标签 'EntityObject.Uav' 的参数定义从 JSON 对象解析完成，共添加 25 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0525] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav' from 0xafb54f9c58 to 0xafb54fa3a0 (25 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0526] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0527] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav' from 0xafb54fa3a0 to 0xafb54fa430 (25 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0528] 正在为类型标签 'EntityObject.Uav' 注册 ParamDefines 对象 (包含 25 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0529] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav' from 0xafb54fa430 to 0xafb54fa060 (25 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0530] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav' from 0xafb54fa060 to 0x2768030aa98 (25 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0531] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0532] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav' from 0x2768030aa98 to 0x276802f5030 (25 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0533] ParamRegistry: 成功注册类型标签 'EntityObject.Uav' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0534] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0535] ParamDefines instance destroyed for type_tag '' at 0x2768030aa98 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0536] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0537] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0538] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0539] 成功注册了类型标签 'EntityObject.Uav' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Uav.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0540] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Uav.Multirotor.json', 将使用类型标签 'EntityObject.Uav.Multirotor' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0541] 文件 'E:\source\dronesplanning\build\Release\data\defines\EntityObject.Uav.Multirotor.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0542] 从Json对象注册参数定义，类型标签为: 'EntityObject.Uav.Multirotor'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0543] ParamDefines::fromJson - 开始为类型标签 'EntityObject.Uav.Multirotor' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0544] ParamDefines::fromJson - 类型标签 'EntityObject.Uav.Multirotor' 包含 9 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0545] ParamDefines instance created for type_tag 'EntityObject.Uav.Multirotor' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0546] ParamDefines::fromJson - 处理参数定义 key='physics.rotor_count' (类型标签 'EntityObject.Uav.Multirotor')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0547] ParamDefines::fromJson - 处理参数定义 key='physics.motor_time_constant_s' (类型标签 'EntityObject.Uav.Multirotor')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0548] ParamDefines::fromJson - 处理参数定义 key='physics.rotor_thrust_coeff' (类型标签 'EntityObject.Uav.Multirotor')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0549] ParamDefines::fromJson - 处理参数定义 key='physics.rotor_torque_coeff' (类型标签 'EntityObject.Uav.Multirotor')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0550] ParamDefines::fromJson - 处理参数定义 key='energy.mr.hover_power' (类型标签 'EntityObject.Uav.Multirotor')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0551] ParamDefines::fromJson - 处理参数定义 key='energy.mr.horizontal_power_factor' (类型标签 'EntityObject.Uav.Multirotor')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0552] ParamDefines::fromJson - 处理参数定义 key='energy.mr.vertical_power_factor' (类型标签 'EntityObject.Uav.Multirotor')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0553] ParamDefines::fromJson - 处理参数定义 key='energy.mr.baseline_power' (类型标签 'EntityObject.Uav.Multirotor')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0554] ParamDefines::fromJson - 处理参数定义 key='energy.mr.endurance_est_max_v_speed' (类型标签 'EntityObject.Uav.Multirotor')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0555] ParamDefines::fromJson - 类型标签 'EntityObject.Uav.Multirotor' 的参数定义从 JSON 对象解析完成，共添加 9 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0556] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.Multirotor' from 0xafb54f9c58 to 0xafb54fa3a0 (9 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0557] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0558] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.Multirotor' from 0xafb54fa3a0 to 0xafb54fa430 (9 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0559] 正在为类型标签 'EntityObject.Uav.Multirotor' 注册 ParamDefines 对象 (包含 9 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0560] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.Multirotor' from 0xafb54fa430 to 0xafb54fa060 (9 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0561] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.Multirotor' from 0xafb54fa060 to 0x27680309bf8 (9 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0562] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0563] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.Multirotor' from 0x27680309bf8 to 0x276802f4f90 (9 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0564] ParamRegistry: 成功注册类型标签 'EntityObject.Uav.Multirotor' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0565] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0566] ParamDefines instance destroyed for type_tag '' at 0x27680309bf8 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0567] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0568] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0569] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0570] 成功注册了类型标签 'EntityObject.Uav.Multirotor' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Uav.Multirotor.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0571] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Uav.VTOL.json', 将使用类型标签 'EntityObject.Uav.VTOL' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0572] 文件 'E:\source\dronesplanning\build\Release\data\defines\EntityObject.Uav.VTOL.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0573] 从Json对象注册参数定义，类型标签为: 'EntityObject.Uav.VTOL'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0574] ParamDefines::fromJson - 开始为类型标签 'EntityObject.Uav.VTOL' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0575] ParamDefines::fromJson - 类型标签 'EntityObject.Uav.VTOL' 包含 15 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0576] ParamDefines instance created for type_tag 'EntityObject.Uav.VTOL' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0577] ParamDefines::fromJson - 处理参数定义 key='vtol.mode' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0578] ParamDefines::fromJson - 处理参数定义 key='dynamics.vtol.transitionSpeed' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0579] ParamDefines::fromJson - 处理参数定义 key='dynamics.vtol.maxHVel' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0580] ParamDefines::fromJson - 处理参数定义 key='dynamics.vtol.maxVVelUp' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0581] ParamDefines::fromJson - 处理参数定义 key='dynamics.vtol.maxVVelDown' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0582] ParamDefines::fromJson - 处理参数定义 key='dynamics.vtol.maxHAcc' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0583] ParamDefines::fromJson - 处理参数定义 key='dynamics.vtol.maxVAcc' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0584] ParamDefines::fromJson - 处理参数定义 key='dynamics.vtol.hover.maxHVel' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0585] ParamDefines::fromJson - 处理参数定义 key='dynamics.vtol.fw.maxHVel' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0586] ParamDefines::fromJson - 处理参数定义 key='vtol.hover_rotor_count' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0587] ParamDefines::fromJson - 处理参数定义 key='energy.vtol.transition_avg_power' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0588] ParamDefines::fromJson - 处理参数定义 key='energy.vtol.transition_duration_estimation' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0589] ParamDefines::fromJson - 处理参数定义 key='energy.vtol.hover_power' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0590] ParamDefines::fromJson - 处理参数定义 key='vtol.transition_speed_mps' (类型标签 'EntityObject.Uav.VTOL')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0591] ParamDefines::fromJson - 处理参数定义 key='energy.consumption_forward_flight_coeff' (类型标签 'EntityObject.Uav.VTOL')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0592] ParamDefines::fromJson - 类型标签 'EntityObject.Uav.VTOL' 的参数定义从 JSON 对象解析完成，共添加 15 个有效参数定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0593] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.VTOL' from 0xafb54f9c58 to 0xafb54fa3a0 (15 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0594] ParamDefines instance destroyed for type_tag '' at 0xafb54f9c58 (had 0 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0595] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.VTOL' from 0xafb54fa3a0 to 0xafb54fa430 (15 definitions)
[debug]:[文件：paramregistry.cpp，第175行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines]: 
[0596] 正在为类型标签 'EntityObject.Uav.VTOL' 注册 ParamDefines 对象 (包含 15 个定义)。可能覆盖现有定义。
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0597] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.VTOL' from 0xafb54fa430 to 0xafb54fa060 (15 definitions)
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0598] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.VTOL' from 0xafb54fa060 to 0x2768030b518 (15 definitions)
[trace]:[文件：thread_safe_cache.h，第453行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0599] 执行数据更新操作，将完全失效缓存
[trace]:[文件：parameters.cpp，第47行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0600] ParamDefines instance move-constructed for type_tag 'EntityObject.Uav.VTOL' from 0x2768030b518 to 0x276802f5b70 (15 definitions)
[debug]:[文件：paramregistry.cpp，第188行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefines::<lambda_2f3eb69589a3b457b64c16381946fb95>::operator ()]: 
[0601] ParamRegistry: 成功注册类型标签 'EntityObject.Uav.VTOL' 的参数定义
[debug]:[文件：thread_safe_cache.h，第464行，函数：NSDrones::NSUtils::ThreadSafeCache<class std::shared_ptr<class NSDrones::NSParams::ParamDefines const > >::executeUpdateWithFullInvalidation]: 
[0602] 数据更新成功，缓存已完全失效
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0603] ParamDefines instance destroyed for type_tag '' at 0x2768030b518 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0604] ParamDefines instance destroyed for type_tag '' at 0xafb54fa060 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0605] ParamDefines instance destroyed for type_tag '' at 0xafb54fa430 (had 0 definitions)
[trace]:[文件：parameters.cpp，第62行，函数：NSDrones::NSParams::ParamDefines::~ParamDefines]: 
[0606] ParamDefines instance destroyed for type_tag '' at 0xafb54fa3a0 (had 0 definitions)
[info]:[文件：config.cpp，第694行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0607] 成功注册了类型标签 'EntityObject.Uav.VTOL' 的参数定义 (来自文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Uav.VTOL.json')。
[debug]:[文件：config.cpp，第690行，函数：NSDrones::Config::loadDirectoryParamDefines]: 
[0608] 发现参数定义文件 'E:/source/dronesplanning/build/Release/data/defines/EntityObject.Zone.json', 将使用类型标签 'EntityObject.Zone' 进行注册。
[trace]:[文件：paramregistry.cpp，第376行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromFile]: 
[0609] 文件 'E:\source\dronesplanning\build\Release\data\defines\EntityObject.Zone.json' 内容已成功读取并解析为 JSON 对象。
[trace]:[文件：paramregistry.cpp，第344行，函数：NSDrones::NSParams::ParamRegistry::registerParamDefinesFromJson]: 
[0610] 从Json对象注册参数定义，类型标签为: 'EntityObject.Zone'
[debug]:[文件：parameters.cpp，第224行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0611] ParamDefines::fromJson - 开始为类型标签 'EntityObject.Zone' 从 JSON 对象解析参数定义...
[trace]:[文件：parameters.cpp，第232行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0612] ParamDefines::fromJson - 类型标签 'EntityObject.Zone' 包含 2 个参数定义条目。
[trace]:[文件：parameters.cpp，第21行，函数：NSDrones::NSParams::ParamDefines::ParamDefines]: 
[0613] ParamDefines instance created for type_tag 'EntityObject.Zone' at 0xafb54f9c58
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0614] ParamDefines::fromJson - 处理参数定义 key='zone_type' (类型标签 'EntityObject.Zone')
[trace]:[文件：parameters.cpp，第261行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0615] ParamDefines::fromJson - 处理参数定义 key='is_3d' (类型标签 'EntityObject.Zone')
[debug]:[文件：parameters.cpp，第317行，函数：NSDrones::NSParams::ParamDefines::fromJson]: 
[0616] ParamDefines::fromJson - 类型标签 'EntityObject.Zone' 的参数定义从 JSON 对象解析完成，共添加 2 个有效参数定义。
