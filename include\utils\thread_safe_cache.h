// include/utils/thread_safe_cache.h
#pragma once

#include "utils/geometry_manager.h"
#include <unordered_map>
#include <unordered_set>
#include <shared_mutex>
#include <condition_variable>
#include <chrono>
#include <optional>
#include <atomic>
#include <string>
#include <vector>
#include <functional>
#include <numeric>
#include <algorithm>
#include <thread>
#include <list>
#include "core/types.h"
#include "utils/logging.h"

namespace NSDrones {
    namespace NSUtils {

        /**
         * @brief 缓存条目结构
         * @tparam T 缓存值的类型
         */
        template<typename T>
        struct CacheEntry {
            T value;                                                    ///< 缓存的值
            std::chrono::steady_clock::time_point timestamp;            ///< 缓存时间戳
            uint64_t version;                                           ///< 缓存版本号
            std::string query_type;                                     ///< 查询类型
            typename std::list<std::string>::iterator lru_it;             ///< 指向lru_order_中键的迭代器

            CacheEntry() = default;
            CacheEntry(T val, const std::string& type)
                : value(std::move(val))
                , timestamp(std::chrono::steady_clock::now())
                , version(0)
                , query_type(type) {}

            CacheEntry(T val, const std::string& type, typename std::list<std::string>::iterator it)
                : value(std::move(val))
                , timestamp(std::chrono::steady_clock::now())
                , version(0)
                , query_type(type)
                , lru_it(it) {}
        };

        /**
         * @class ThreadSafeCache
         * @brief 线程安全的通用缓存类，支持函数注入模式
         * @tparam T 缓存值的类型
         */
        template<typename T>
        class ThreadSafeCache {
        public:
            // 函数注入类型定义
            using DataProvider = std::function<T()>;                    ///< 数据提供函数类型
            using CacheInvalidator = std::function<void()>;             ///< 缓存失效函数类型
            using DataUpdater = std::function<bool()>;                  ///< 数据更新函数类型

        private:
            mutable std::shared_mutex mutex_;                           ///< 读写锁
            std::unordered_map<std::string, CacheEntry<T>> cache_;      ///< 缓存存储
            mutable std::atomic<uint64_t> version_{0};                  ///< 全局版本号
            std::chrono::minutes max_age_;                              ///< 缓存最大存活时间
            size_t max_size_;                                           ///< 缓存最大条目数
            mutable std::unordered_set<std::string> computing_keys_;    ///< 正在计算的键集合
            mutable std::condition_variable_any computation_cv_;        ///< 用于通知计算完成的条件变量
            mutable std::list<std::string> lru_order_;                    ///< 按LRU顺序存储键（前端是最少使用，后端是最近使用）

        public:
            /**
             * @brief 构造函数
             * @param max_age 缓存最大存活时间
             * @param max_size 缓存最大条目数
             */
            explicit ThreadSafeCache(std::chrono::minutes max_age = std::chrono::minutes(5),
                                    size_t max_size = 1000)
                : max_age_(max_age), max_size_(max_size) {}

            /**
             * @brief 获取缓存值
             * @param key 缓存键
             * @return 缓存值的可选引用，如果不存在或已过期则为空
             */
            std::optional<T> get(const std::string& key) {
                std::shared_lock lock(mutex_);
                auto it = cache_.find(key);
                if (it != cache_.end()) {
                    if (!isExpired(it->second)) {
                        // 升级为独占锁以更新LRU顺序
                        lock.unlock();
                        std::unique_lock unique_lock(mutex_);
                        // 重新查找元素，因为它可能已经改变
                        it = cache_.find(key);
                        if (it != cache_.end() && !isExpired(it->second)) {
                            // 将访问的项移动到LRU列表的末尾（MRU）
                            lru_order_.erase(it->second.lru_it);
                            lru_order_.push_back(key);
                            it->second.lru_it = --lru_order_.end(); // 更新迭代器到新位置
                            LOG_DEBUG("缓存命中键 '{}' 并更新LRU顺序", key);
                            return it->second.value;
                        }
                        // 如果重新查找失败或过期，则返回std::nullopt
                    } else {
                        // 条目存在但已过期
                        LOG_DEBUG("缓存命中键 '{}' 但条目已过期", key);
                    }
                }
                return std::nullopt;
            }

            /**
             * @brief 设置缓存值
             * @param key 缓存键
             * @param value 缓存值
             * @param query_type 查询类型
             */
            void set(const std::string& key, T value, const std::string& query_type = "") {
                std::unique_lock lock(mutex_);
                removeExpiredInternal(); // 清理过期条目（内部方法，已持有锁）

                auto it = cache_.find(key);
                if (it != cache_.end()) { // Key exists, update it
                    LOG_DEBUG("更新缓存中已存在的键: {}。查询类型: {}", key, query_type);
                    it->second.value = std::move(value);
                    it->second.timestamp = std::chrono::steady_clock::now();
                    it->second.version++;
                    it->second.query_type = query_type; // Update query_type as well

                    // Move to the end of LRU list (MRU)
                    lru_order_.erase(it->second.lru_it);
                    lru_order_.push_back(key);
                    it->second.lru_it = --lru_order_.end();
                } else { // Key does not exist, insert new
                    // Check if cache is full and needs eviction
                    if (cache_.size() >= max_size_) {
                        if (!lru_order_.empty()) {
                            const std::string& lru_key = lru_order_.front();
                            auto lru_it_map = cache_.find(lru_key);
                            if (lru_it_map != cache_.end()) {
                                LOG_DEBUG("缓存已满，正在移除最近最少使用的键: {}", lru_key);
                                cache_.erase(lru_it_map); // Erase from cache map
                            } else {
                                LOG_WARN("在缓存淘汰过程中未找到LRU键 '{}'。", lru_key);
                            }
                            lru_order_.pop_front(); // Erase from LRU list
                        } else {
                            LOG_WARN("缓存已满但LRU顺序列表为空。这不应该发生。");
                        }
                    }

                    // Add new item
                    if (cache_.size() < max_size_) { // Re-check size after potential eviction
                        LOG_DEBUG("添加新键到缓存: {}。查询类型: {}", key, query_type);
                        lru_order_.push_back(key); // Add to LRU list first
                        typename std::list<std::string>::iterator new_entry_lru_it = --lru_order_.end(); // Get iterator

                        auto [new_it_map, success] = cache_.emplace(
                            std::piecewise_construct,
                            std::forward_as_tuple(key),
                            std::forward_as_tuple(std::move(value), query_type, new_entry_lru_it)
                        );

                        if (!success) {
                             // Should not happen if logic is correct
                            lru_order_.erase(new_entry_lru_it); // Rollback LRU list change
                            LOG_ERROR("无法为键 '{}' 添加新条目，虽然它应该是可用的。", key);
                        }
                    } else {
                        LOG_WARN("尝试淘汰后缓存仍然已满。无法添加键 '{}'。最大大小: {}。", key, max_size_);
                    }
                }
            }

            /**
             * @brief 清空所有缓存
             */
            void clear() {
                std::unique_lock lock(mutex_);
                cache_.clear();
                lru_order_.clear();
                version_.fetch_add(1);
                LOG_INFO("缓存已清空。");
            }

            /**
             * @brief 按查询类型清空缓存
             * @param query_type 查询类型
             */
            void clearByType(const std::string& query_type) {
                std::unique_lock lock(mutex_);
                for (auto it = cache_.begin(); it != cache_.end(); ) {
                    if (it->second.query_type == query_type) {
                        LOG_DEBUG("按类型清理缓存条目 '{}', 键: {}", query_type, it->first);
                        lru_order_.erase(it->second.lru_it); // Erase from LRU list
                        it = cache_.erase(it); // Erase from cache map and get next iterator
                    } else {
                        ++it;
                    }
                }
                LOG_INFO("已清理类型为 '{}' 的缓存条目", query_type);
            }

            /**
             * @brief 移除过期的缓存条目（公共接口，线程安全）
             */
            void removeExpired() {
                std::unique_lock lock(mutex_);
                removeExpiredInternal();
            }

        private:
            /**
             * @brief 移除过期的缓存条目（内部方法，调用者必须持有unique_lock）
             */
            void removeExpiredInternal() {
                auto now = std::chrono::steady_clock::now();
                for (auto it = cache_.begin(); it != cache_.end(); ) {
                    if (now - it->second.timestamp > max_age_) {
                        LOG_DEBUG("移除过期缓存条目，键: {}", it->first);
                        lru_order_.erase(it->second.lru_it); // 从LRU链表中删除
                        it = cache_.erase(it); // 从缓存映射中删除
                    } else {
                        ++it;
                    }
                }
            }

        public:

            /**
             * @brief 获取缓存统计信息
             * @return 包含缓存大小、版本号等信息的字符串
             */
            std::string getStatistics() const {
                std::shared_lock lock(mutex_);
                return "缓存大小: " + std::to_string(cache_.size()) +
                       ", 最大大小: " + std::to_string(max_size_) +
                       ", 版本号: " + std::to_string(version_.load());
            }

            /**
             * @brief 获取当前缓存大小
             * @return 缓存条目数量
             */
            size_t size() const {
                std::shared_lock lock(mutex_);
                return cache_.size();
            }

            /**
             * @brief 检查缓存是否为空
             * @return 如果缓存为空则返回true
             */
            bool empty() const {
                std::shared_lock lock(mutex_);
                return cache_.empty();
            }

            /**
             * @brief 获取或计算缓存值（函数注入模式）
             * @param key 缓存键
             * @param data_provider 数据提供函数，当缓存未命中时调用
             * @param query_type 查询类型
             * @return 缓存值
             */
            T getOrCompute(const std::string& key, DataProvider data_provider, const std::string& query_type = "") {
                // Try to get from cache with shared lock first
                {
                    std::shared_lock lock(mutex_);
                    auto it = cache_.find(key);
                    if (it != cache_.end() && !isExpired(it->second)) {
                        // Upgrade to unique lock to modify lru_order_
                        // 这部分代码用于升级共享锁为独占锁，以便修改lru_order_
                        // 这种模式（释放共享锁，获取独占锁）如果不小心会导致竞争，但这里我们在获取独占锁后重新检查条件
                        // 一个更健壮的方法可能涉及始终获取独占锁或更复杂的读写锁策略
                        // 目前，让我们尝试一个更简单的方法来更新LRU
                        // 我们需要修改lru_order_和it->second.lru_it，这需要独占锁

                        // 为了避免复杂的锁升级，我们将释放共享锁，获取独占锁，然后重新查找和更新
                        // 这对于命中来说性能较差，但实现起来更简单
                        // 一个真正高性能的LRU缓存，支持并发读写更新，是非凡的

                        // 简单的方法：当get被调用时，我们不在这里修改LRU顺序
                        // LRU顺序只在`set`时更新
                        // 这意味着它不是严格的LRU，而是“最近最少设置/添加”
                        // 如果需要严格的LRU读取，复杂度会增加
                        // 目前，我们继续使用原始的理解，并尝试实现严格的LRU读取

                        // 释放共享锁，获取独占锁
                        lock.unlock();
                        std::unique_lock unique_lock(mutex_);
                        // 重新查找元素，因为它可能已经改变
                        it = cache_.find(key);
                        if (it != cache_.end() && !isExpired(it->second)) {
                            // 将访问的项移动到LRU列表的末尾（MRU）
                            lru_order_.erase(it->second.lru_it);
                            lru_order_.push_back(key);
                            it->second.lru_it = --lru_order_.end(); // 更新迭代器到新位置
                            return it->second.value;
                        }
                        // 如果重新查找失败或已过期，继续计算
                        // 此时仍然持有unique_lock
                    }
                } // Shared lock released here or unique_lock scope ends if taken above.

                // 如果未找到或已过期，则计算值。需要独占锁
                std::unique_lock lock(mutex_); // 确保获取独占锁

                // 获取独占锁后重新检查缓存，其他线程可能已经填充了它
                auto it = cache_.find(key);
                if (it != cache_.end() && !isExpired(it->second)) {
                    // 其他线程已经填充了该项。更新LRU。
                    lru_order_.erase(it->second.lru_it);
                    lru_order_.push_back(key);
                    it->second.lru_it = --lru_order_.end();
                    return it->second.value;
                }

                // 如果仍未找到或已过期，则计算并设置
                LOG_DEBUG("缓存未命中或键 '{}' 已过期。正在计算值。查询类型: {}", key, query_type);
                T value = data_provider(); // 计算值（可能抛出异常）

                // `set`方法处理LRU更新、淘汰等
                // 但`set`也需要独占锁。我们已经有一个独占锁
                // 所以，我们在这里复制`set`方法中相关的逻辑

                removeExpiredInternal(); // 清理过期条目（内部方法，已持有锁）

                auto update_it = cache_.find(key); // 重新查找键
                if (update_it != cache_.end()) { // 键现在存在（例如更新或重新添加）
                    LOG_DEBUG("计算后更新已存在的键: {}。", key);
                    update_it->second.value = std::move(value);
                    update_it->second.timestamp = std::chrono::steady_clock::now();
                    update_it->second.version++;
                    update_it->second.query_type = query_type;
                    lru_order_.erase(update_it->second.lru_it);
                    lru_order_.push_back(key);
                    update_it->second.lru_it = --lru_order_.end();
                } else {
                    if (cache_.size() >= max_size_) {
                        if (!lru_order_.empty()) {
                            const std::string& lru_key_to_evict = lru_order_.front();
                            auto lru_map_it = cache_.find(lru_key_to_evict);
                            if (lru_map_it != cache_.end()) {
                                cache_.erase(lru_map_it);
                            }
                            lru_order_.pop_front();
                        }
                    }
                    if (cache_.size() < max_size_) {
                        lru_order_.push_back(key);
                        typename std::list<std::string>::iterator new_lru_it = --lru_order_.end();

                        auto [emplaced_it, success] = cache_.emplace(
                            std::piecewise_construct,
                            std::forward_as_tuple(key),
                            std::forward_as_tuple(std::move(value), query_type, new_lru_it) // 传递lru_it给构造函数
                        );
                        if (!success) {
                            lru_order_.erase(new_lru_it); // 回滚
                            LOG_ERROR("在getOrCompute中无法为键 '{}' 添加新条目。", key);
                            // 如果emplace失败，我们应该直接返回计算值或抛出异常
                            // 目前，如果emplace失败，我们将返回计算值
                        }
                        // 返回新添加条目的值
                        return emplaced_it->second.value;
                    } else {
                        LOG_WARN("在尝试淘汰后，getOrCompute中的缓存仍然已满，键 '{}'。返回计算值但不缓存。", key);
                        // 回退：返回计算值但不缓存
                        return value;
                    }
                }
                return update_it->second.value; // 返回更新条目的值
            }

            /**
             * @brief 执行数据更新操作并管理缓存（函数注入模式）
             * @param updater 数据更新函数
             * @param invalidator 缓存失效函数，更新成功后调用
             * @return 更新是否成功
             */
            bool executeUpdate(DataUpdater updater, CacheInvalidator invalidator) {
                LOG_TRACE("正在执行数据更新操作");

                // 不持有缓存锁调用更新函数，让调用者负责数据源的线程安全
                bool success = updater();

                if (success) {
                    // 更新成功，获取缓存锁执行缓存失效
                    std::unique_lock lock(mutex_);
                    LOG_TRACE("数据更新成功，执行缓存失效");
                    invalidator();
                    version_.fetch_add(1);
                } else {
                    LOG_TRACE("数据更新失败，保持缓存不变");
                }

                return success;
            }

            /**
             * @brief 执行数据更新操作并失效指定类型的缓存
             * @param updater 数据更新函数
             * @param affected_types 受影响的缓存类型列表
             * @return 更新是否成功
             */
            bool executeUpdateWithTypeInvalidation(DataUpdater updater, const std::vector<std::string>& affected_types) {
                LOG_TRACE("执行数据更新操作，影响缓存类型: {}",
                         std::accumulate(affected_types.begin(), affected_types.end(), std::string(),
                             [](const std::string& a, const std::string& b) {
                                 return a.empty() ? b : a + ", " + b;
                             }));

                // 不持有缓存锁调用更新函数，让调用者负责数据源的线程安全
                bool success = updater();

                if (success) {
                    // 更新成功，获取缓存锁失效指定类型的缓存
                    std::unique_lock lock(mutex_);
                    for (const auto& type : affected_types) {
                        auto it = cache_.begin();
                        while (it != cache_.end()) {
                            if (it->second.query_type == type) {
                                LOG_DEBUG("按类型失效缓存条目，键: {}, 类型: {}", it->first, type);
                                lru_order_.erase(it->second.lru_it); // 从LRU链表中删除
                                it = cache_.erase(it); // 从缓存映射中删除
                            } else {
                                ++it;
                            }
                        }
                    }
                    version_.fetch_add(1);
                    LOG_DEBUG("数据更新成功，已失效 {} 种类型的缓存", affected_types.size());
                } else {
                    LOG_TRACE("数据更新失败，保持缓存不变");
                }

                return success;
            }

            /**
             * @brief 执行数据更新操作并完全失效缓存
             * @param updater 数据更新函数
             * @return 更新是否成功
             */
            bool executeUpdateWithFullInvalidation(DataUpdater updater) {
                LOG_TRACE("执行数据更新操作，将完全失效缓存");

                // 不持有缓存锁调用更新函数，让调用者负责数据源的线程安全
                bool success = updater();

                if (success) {
                    // 更新成功，获取缓存锁完全失效缓存
                    std::unique_lock lock(mutex_);
                    cache_.clear();
                    lru_order_.clear(); // 同时清理LRU链表
                    version_.fetch_add(1);
                    LOG_DEBUG("数据更新成功，缓存已完全失效");
                } else {
                    LOG_TRACE("数据更新失败，保持缓存不变");
                }

                return success;
            }

            size_t getMaxSize() const {
                std::shared_lock lock(mutex_);
                return max_size_;
            }

            std::chrono::minutes getMaxAge() const {
                std::shared_lock lock(mutex_);
                return max_age_;
            }

            /**
             * @brief 更新缓存配置参数
             * @param new_max_age 新的最大存活时间
             * @param new_max_size 新的最大条目数
             * @note 此方法会清空现有缓存以应用新配置
             */
            void updateConfiguration(std::chrono::minutes new_max_age, size_t new_max_size) {
                std::unique_lock lock(mutex_);
                max_age_ = new_max_age;
                max_size_ = new_max_size;
                cache_.clear();
                lru_order_.clear();
                version_.fetch_add(1);
                LOG_INFO("ThreadSafeCache: 配置已更新 - 最大存活时间: {}分钟, 最大条目数: {}",
                         new_max_age.count(), new_max_size);
            }

            /**
             * @brief 获取当前缓存配置
             * @return std::pair<std::chrono::minutes, size_t> 返回 {最大存活时间, 最大条目数}
             */
            std::pair<std::chrono::minutes, size_t> getConfiguration() const {
                std::shared_lock lock(mutex_);
                return {max_age_, max_size_};
            }

        private:
            bool isExpired(const CacheEntry<T>& entry) const {
                auto now = std::chrono::steady_clock::now();
                return now - entry.timestamp > max_age_;
            }
        };

        // 常用的缓存类型别名
        using ObjectIdCache = ThreadSafeCache<std::vector<ObjectID>>;
        using StringCache = ThreadSafeCache<std::string>;
        using BoolCache = ThreadSafeCache<bool>;

        /**
         * @brief 空间查询缓存条目（使用WGS84坐标）
         * @note 优化了对频繁位置更新的缓存策略
         */
        struct SpatialQueryCacheEntry {
            std::vector<ObjectID> results;
            WGS84BoundingBox region;  // 查询区域（WGS84坐标）
            std::chrono::steady_clock::time_point timestamp;

            // 缓存优化字段
            mutable size_t accessCount = 0;           // 访问次数
            mutable bool isStale = false;             // 是否过期（由于对象位置变化）
            std::optional<double> toleranceRadius;   // 容差半径（米），用于近似匹配

            SpatialQueryCacheEntry() = default;

            SpatialQueryCacheEntry(const std::vector<ObjectID>& res,
                                  const WGS84BoundingBox& wgs84_region,
                                  std::optional<double> tolerance = std::nullopt)
                : results(res)
                , region(wgs84_region)
                , timestamp(std::chrono::steady_clock::now())
                , toleranceRadius(tolerance) {
            }

            /**
             * @brief 检查查询区域是否与缓存区域匹配（考虑容差）
             * @param queryRegion 查询区域
             * @return 是否匹配
             */
            bool matchesRegion(const WGS84BoundingBox& queryRegion) const {
                if (!toleranceRadius.has_value()) {
                    // 精确匹配
                    return region.minLongitude == queryRegion.minLongitude &&
                           region.maxLongitude == queryRegion.maxLongitude &&
                           region.minLatitude == queryRegion.minLatitude &&
                           region.maxLatitude == queryRegion.maxLatitude &&
                           region.minAltitude == queryRegion.minAltitude &&
                           region.maxAltitude == queryRegion.maxAltitude;
                } else {
                    // 容差匹配（使用GeometryManager进行精确计算）
                    auto center1 = region.getCenter();
                    auto center2 = queryRegion.getCenter();

                    // 使用GeometryManager计算精确距离
                    double distance = GeometryManager::calculateDistance(
                        WGS84Point(center1.longitude, center1.latitude, center1.altitude),
                        WGS84Point(center2.longitude, center2.latitude, center2.altitude)
                    );
                    return distance <= toleranceRadius.value();
                }
            }

            /**
             * @brief 标记缓存条目为过期
             */
            void markStale() const {
                isStale = true;
            }

            /**
             * @brief 检查缓存条目是否仍然有效
             * @param maxAge 最大生存时间
             * @return 是否有效
             */
            bool isValid(std::chrono::milliseconds maxAge) const {
                if (isStale) return false;
                auto now = std::chrono::steady_clock::now();
                return (now - timestamp) <= maxAge;
            }
        };

        using SpatialQueryCache = ThreadSafeCache<SpatialQueryCacheEntry>;

    } // namespace NSUtils
} // namespace NSDrones
