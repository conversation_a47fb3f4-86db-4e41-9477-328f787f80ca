#pragma once

#include "core/types.h"
#include "utils/object_id.h"
#include "utils/coordinate_converter.h"
#include <variant>
#include <vector>
#include <string>
#include <unordered_set>
#include <memory>

namespace NSDrones {
	namespace NSEnvironment {

		/**
		 * @struct CollisionOptions
		 * @brief 碰撞检测选项配置结构
		 *
		 * 统一配置所有类型碰撞检测的行为参数，包括检测精度、性能优化、
		 * 时间相关设置等。支持静态/动态对象过滤、安全边距设置、
		 * 连续碰撞检测等高级功能。
		 */
		struct CollisionOptions {
			// === 基础检测选项 ===
			double safetyMargin = 0.0;                              ///< 安全边距(米)，用于扩展碰撞检测范围
			bool checkDynamicObjects = true;                        ///< 是否检查动态对象（可移动对象）
			bool checkStaticObjects = true;                         ///< 是否检查静态对象（固定障碍物）
			std::unordered_set<ObjectID> ignoredObjectIds;          ///< 忽略检测的对象ID集合

			// === 时间相关选项 ===
			Time timePoint = 0.0;                                   ///< 检测的时间点（用于动态对象状态）
			int timeSteps = 5;                                      ///< 连续碰撞检测的离散时间步数
			double timeHorizon = 1.0;                               ///< 未来时间窗口大小（秒）

			// === 检测精度选项 ===
			bool enableContact = true;                              ///< 是否计算接触点详细信息
			bool enableDistance = true;                             ///< 是否计算最短距离
			bool enableNearestPoints = true;                        ///< 是否计算最近点对
			bool enableContinuousDetection = false;                 ///< 是否启用连续碰撞检测（扫掠测试）
			uint32_t maxContacts = 10;                              ///< 最大接触点数量限制

			// === 性能优化选项 ===
			double collisionEpsilon = 1e-6;                         ///< 碰撞检测精度阈值（米）
			uint32_t maxIterations = 20;                            ///< 迭代算法最大迭代次数
			bool useSpacePartitioning = true;                       ///< 是否使用空间分区加速

			// === 缓存选项 ===
			bool enableResultCache = true;                          ///< 是否启用结果缓存
			double cacheValidTime = 0.05;                           ///< 缓存有效时间（秒）

			/**
			 * @brief 默认构造函数
			 * 使用合理的默认值初始化所有选项
			 */
			CollisionOptions() = default;

			// === 静态工厂方法 ===

			/**
			 * @brief 创建快速碰撞检测选项（仅检测是否碰撞，不计算详细信息）
			 * @return 性能优化的碰撞选项，适用于大量对象的快速筛选
			 */
			static CollisionOptions createFastOptions() {
				CollisionOptions options;
				options.enableContact = false;
				options.enableDistance = false;
				options.enableNearestPoints = false;
				options.maxContacts = 1;
				return options;
			}

			/**
			 * @brief 创建详细碰撞检测选项（计算所有可用信息）
			 * @return 详细的碰撞选项，适用于需要完整碰撞信息的场景
			 */
			static CollisionOptions createDetailedOptions() {
				CollisionOptions options;
				options.enableContact = true;
				options.enableDistance = true;
				options.enableNearestPoints = true;
				options.maxContacts = 30;
				return options;
			}

			/**
			 * @brief 创建连续碰撞检测选项（用于运动轨迹碰撞检测）
			 * @param horizon 时间窗口大小（秒）
			 * @param steps 离散时间步数
			 * @return 连续碰撞检测选项，适用于轨迹规划和动态避障
			 */
			static CollisionOptions createContinuousOptions(double horizon = 1.0, int steps = 10) {
				CollisionOptions options;
				options.enableContinuousDetection = true;
				options.timeHorizon = horizon;
				options.timeSteps = steps;
				options.enableDistance = true;
				return options;
			}

			/**
			 * @brief 创建安全边距检测选项
			 * @param margin 安全边距（米）
			 * @return 带安全边距的碰撞选项，适用于路径规划安全检查
			 */
			static CollisionOptions createSafetyOptions(double margin) {
				CollisionOptions options;
				options.safetyMargin = margin;
				options.enableDistance = true;
				options.enableNearestPoints = true;
				return options;
			}
		};

		/**
		 * @struct CollisionQuery
		 * @brief 碰撞检测查询结构
		 *
		 * 定义不同类型的碰撞检测查询，支持点、线段、对象间等多种检测模式。
		 * 使用variant模式存储不同查询类型的特定数据，确保类型安全和内存效率。
		 */
		struct CollisionQuery {
			/**
			 * @enum QueryType
			 * @brief 碰撞查询类型枚举
			 *
			 * 定义支持的碰撞检测查询类型，从简单的点检测到复杂的连续碰撞检测
			 */
			enum class QueryType {
				UNKNOWN = 0,                         ///< 未知或未初始化的查询类型
				POINT_ENVIRONMENT,                   ///< 点与环境碰撞查询（用于位置有效性检查）
				SEGMENT_ENVIRONMENT,                 ///< 线段与环境碰撞查询（用于路径碰撞检测）
				TWO_OBJECTS,                         ///< 两个特定对象间碰撞查询
				OBJECT_VS_ENVIRONMENT,               ///< 单个对象与环境中所有其他对象的碰撞查询
				ALL_RELEVANT_OBJECT_PAIRS,           ///< 场景中所有相关对象对的碰撞查询（全局碰撞检测）
				CONTINUOUS_OBJECT_VS_ENVIRONMENT     ///< 连续碰撞检测：对象沿轨迹运动的扫掠测试
			};

			// --- 查询特定数据结构 ---

			/**
			 * @struct PointEnvQueryData
			 * @brief 用于 POINT_ENVIRONMENT 查询类型的数据。
			 */
			struct PointEnvQueryData {
				EcefPoint point;       ///< 要检查的点 (ECEF坐标)
				Time time_stamp = 0.0; ///< 查询的时间戳 (用于动态对象)

				PointEnvQueryData(EcefPoint p, Time t = 0.0) : point(std::move(p)), time_stamp(t) {}
			};

			/**
			 * @struct SegmentEnvQueryData
			 * @brief 用于 SEGMENT_ENVIRONMENT 查询类型的数据。
			 */
			struct SegmentEnvQueryData {
				WGS84Point start_point; ///< 线段起点 (WGS84坐标)
				WGS84Point end_point;   ///< 线段终点 (WGS84坐标)
				Time start_time = 0.0; ///< 线段起点的时间戳
				Time end_time = 0.0;   ///< 线段终点的时间戳

				SegmentEnvQueryData(WGS84Point s, WGS84Point e, Time t1 = 0.0, Time t2 = 0.0)
					: start_point(std::move(s)), end_point(std::move(e)), start_time(t1), end_time(t2) {}
			};

			/**
			 * @struct TwoObjectsQueryData
			 * @brief 用于 TWO_OBJECTS 查询类型的数据。
			 */
			struct TwoObjectsQueryData {
				ObjectID object1_id = NSUtils::INVALID_OBJECT_ID; ///< 第一个对象的ID
				ObjectID object2_id = NSUtils::INVALID_OBJECT_ID; ///< 第二个对象的ID

				TwoObjectsQueryData(ObjectID id1, ObjectID id2) : object1_id(id1), object2_id(id2) {}
			};

			/**
			 * @struct ObjectEnvQueryData
			 * @brief 用于 OBJECT_VS_ENVIRONMENT 查询类型的数据。
			 */
			struct ObjectEnvQueryData {
				ObjectID object_id = NSUtils::INVALID_OBJECT_ID; ///< 要检查的对象的ID

				explicit ObjectEnvQueryData(ObjectID id) : object_id(id) {}
			};

			/**
			 * @struct SweepQueryData
			 * @brief 用于 CONTINUOUS_OBJECT_VS_ENVIRONMENT (扫掠测试) 查询类型的数据。
			 */
			struct SweepQueryData {
				ObjectID moving_object_id = NSUtils::INVALID_OBJECT_ID; ///< 进行扫掠测试的移动对象的ID
				Vector3D direction;       ///< 移动方向 (建议归一化)
				double max_distance = 0.0;        ///< 最大移动距离

				SweepQueryData(ObjectID id, Vector3D dir, double dist)
					: moving_object_id(id), direction(std::move(dir)), max_distance(dist) {}
			};


			// --- 成员变量 ---
			QueryType type = QueryType::UNKNOWN; ///< 查询类型

			// 使用 std::variant 存储特定于查询类型的数据
			// std::monostate 用于表示某些查询类型可能不需要额外数据 (例如 ALL_RELEVANT_OBJECT_PAIRS)
			std::variant<
				std::monostate,              // 对应 QueryType::UNKNOWN 或 QueryType::ALL_RELEVANT_OBJECT_PAIRS
				PointEnvQueryData,
				SegmentEnvQueryData,
				TwoObjectsQueryData,
				ObjectEnvQueryData,
				SweepQueryData
			> data;

			// --- 构造函数 ---
			CollisionQuery() : type(QueryType::UNKNOWN), data(std::monostate{}) {}

			// 便利构造函数
			explicit CollisionQuery(QueryType query_type, decltype(data) query_data = std::monostate{})
				: type(query_type), data(std::move(query_data)) {
				// 确保 ALL_RELEVANT_OBJECT_PAIRS 使用 monostate
				if (type == QueryType::ALL_RELEVANT_OBJECT_PAIRS && !std::holds_alternative<std::monostate>(data)) {
					data = std::monostate{};
				}
			}
		};

		/**
		 * @struct CollisionResult
		 * @brief 碰撞检测结果结构
		 *
		 * 统一的碰撞检测结果，包含碰撞状态、几何信息、对象信息等。
		 * 支持ECEF和WGS84双坐标系输出，满足不同使用场景需求。
		 */
		struct CollisionResult {
			// === 基本碰撞状态 ===
			bool hasCollision = false;                                     ///< 是否检测到碰撞
			double distance = std::numeric_limits<double>::infinity();     ///< 最近距离（米，未碰撞时有效）
			double penetrationDepth = 0.0;                                 ///< 穿透深度（米，碰撞时有效）

			// === 接触点信息（内部计算使用ECEF坐标系） ===
			std::vector<EcefPoint> contactPoints;                          ///< 接触点列表（ECEF坐标）
			std::vector<Vector3D> contactNormals;                          ///< 接触点法线列表

			// === 接触点信息（外部输出使用WGS84坐标系） ===
			std::vector<WGS84Point> contactPointsWGS84;                    ///< 接触点列表（WGS84坐标）

			// === 最近点信息（用于距离计算和可视化） ===
			EcefPoint nearestPoint1;                                       ///< 第一个形状上的最近点（ECEF坐标）
			EcefPoint nearestPoint2;                                       ///< 第二个形状上的最近点（ECEF坐标）
			WGS84Point nearestPoint1WGS84;                                 ///< 第一个形状上的最近点（WGS84坐标）
			WGS84Point nearestPoint2WGS84;                                 ///< 第二个形状上的最近点（WGS84坐标）

			// === 碰撞对象标识 ===
			ObjectID object1Id = NSUtils::INVALID_OBJECT_ID;               ///< 第一个碰撞对象ID
			ObjectID object2Id = NSUtils::INVALID_OBJECT_ID;               ///< 第二个碰撞对象ID

			// === 时间信息（连续碰撞检测） ===
			Time collisionTime = 0.0;                                      ///< 碰撞发生的时间点（连续检测）

			// === 调试和追踪信息 ===
			std::string collisionSource;                                   ///< 检测器名称（用于调试和性能分析）

			/**
			 * @brief 默认构造函数
			 * 初始化为无碰撞状态
			 */
			CollisionResult() = default;

			/**
			 * @brief 构造碰撞结果
			 * @param id1 第一个碰撞对象ID
			 * @param id2 第二个碰撞对象ID
			 * @param depth 穿透深度（米）
			 * @param source 检测器名称
			 */
			CollisionResult(
				const ObjectID& id1,
				const ObjectID& id2,
				double depth,
				const std::string& source = "通用检测器"
			) : hasCollision(true),
				penetrationDepth(depth),
				object1Id(id1),
				object2Id(id2),
				collisionSource(source) {}

			/**
			 * @brief 添加接触点信息（ECEF坐标）
			 * @param ecef_point 接触点（ECEF坐标）
			 * @param normal 接触点法线
			 */
			void addContact(const EcefPoint& ecef_point, const Vector3D& normal) {
				contactPoints.push_back(ecef_point);
				contactNormals.push_back(normal);
			}

			/**
			 * @brief 添加接触点信息（WGS84坐标）
			 * @param wgs84_point 接触点（WGS84坐标）
			 * @param normal 接触点法线
			 */
			void addContactWGS84(const WGS84Point& wgs84_point, const Vector3D& normal) {
				contactPointsWGS84.push_back(wgs84_point);
				contactNormals.push_back(normal);
			}

			/**
			 * @brief 添加接触点信息（同时包含ECEF和WGS84坐标）
			 * @param ecef_point 接触点（ECEF坐标）
			 * @param wgs84_point 接触点（WGS84坐标）
			 * @param normal 接触点法线
			 */
			void addContactBoth(const EcefPoint& ecef_point, const WGS84Point& wgs84_point, const Vector3D& normal) {
				contactPoints.push_back(ecef_point);
				contactPointsWGS84.push_back(wgs84_point);
				contactNormals.push_back(normal);
			}

			/**
			 * @brief 设置最近点信息（ECEF坐标）
			 * @param ecef_p1 第一个形状上的最近点（ECEF坐标）
			 * @param ecef_p2 第二个形状上的最近点（ECEF坐标）
			 * @param dist 两点之间的距离（米）
			 */
			void setNearestPoints(const EcefPoint& ecef_p1, const EcefPoint& ecef_p2, double dist) {
				nearestPoint1 = ecef_p1;
				nearestPoint2 = ecef_p2;
				distance = dist;
			}

			/**
			 * @brief 设置最近点信息（WGS84坐标）
			 * @param wgs84_p1 第一个形状上的最近点（WGS84坐标）
			 * @param wgs84_p2 第二个形状上的最近点（WGS84坐标）
			 */
			void setNearestPointsWGS84(const WGS84Point& wgs84_p1, const WGS84Point& wgs84_p2) {
				nearestPoint1WGS84 = wgs84_p1;
				nearestPoint2WGS84 = wgs84_p2;
			}

			/**
			 * @brief 合并碰撞检测结果
			 * @param other 另一个碰撞结果
			 * @return 合并后的结果引用
			 *
			 * 合并策略：
			 * 1. 碰撞优先于非碰撞
			 * 2. 碰撞间比较穿透深度（保留更严重的）
			 * 3. 非碰撞间比较距离（保留更近的）
			 */
			CollisionResult& merge(const CollisionResult& other) {
				// 策略1：碰撞优先 - 如果另一个有碰撞而当前没有，采用另一个
				if (!hasCollision && other.hasCollision) {
					*this = other;
					return *this;
				}

				// 策略2：都有碰撞时，保留穿透深度更大的（更严重的碰撞）
				if (hasCollision && other.hasCollision) {
					if (other.penetrationDepth > penetrationDepth) {
						*this = other;
					}
					return *this;
				}

				// 策略3：都无碰撞时，保留距离更小的（更接近的情况）
				if (!hasCollision && !other.hasCollision) {
					if (other.distance < distance) {
						*this = other;
					}
					return *this;
				}

				// 默认保留当前结果（当前有碰撞而另一个没有）
				return *this;
			}

			/**
			 * @brief 检查结果是否有效
			 * @return 如果结果包含有效信息返回true
			 */
			bool isValid() const {
				return object1Id != NSUtils::INVALID_OBJECT_ID ||
					object2Id != NSUtils::INVALID_OBJECT_ID ||
					hasCollision ||
					hasValidDistance();
			}

		private:
			/**
			 * @brief 检查距离值是否有效
			 * @return 如果距离值有效返回true
			 */
			bool hasValidDistance() const {
				return distance != std::numeric_limits<double>::infinity() && distance >= 0.0;
			}
		};
	} // namespace NSEnvironment
} // namespace NSDrones