// include/environment/coordinate/coordinate_manager.h
#pragma once

// 前向声明
namespace NSDrones {
	namespace NSEnvironment {
		class TaskSpace;
	}
}

#include "environment/coordinate/task_space.h"

namespace NSDrones {
	namespace NSEnvironment {
		using namespace NSDrones::NSCore;
		using namespace NSDrones::NSUtils;

		//=== 坐标系统管理器 ===//

		/**
		 * @class CoordinateManager
		 * @brief 坐标系统管理器 - 统一管理所有任务空间和坐标转换
		 *
		 * 职责：
		 * 1. 管理多个任务空间（TaskSpace）的生命周期
		 * 2. 提供全局坐标空间的便捷访问
		 * 3. 处理实体位置更新对动态任务空间的影响
		 * 4. 作为系统中唯一的坐标转换入口点
		 */
		class CoordinateManager {
		private:
			// 任务空间映射 space_id -> TaskSpace
			std::unordered_map<std::string, std::shared_ptr<TaskSpace>> task_spaces_;
			mutable std::mutex spaces_mutex_;
			mutable std::atomic<size_t> total_spaces_created_{ 0 };

		public:
			CoordinateManager();
			~CoordinateManager();

			bool initialize(std::shared_ptr<NSParams::ParamValues> global_params);

			bool createStaticTaskSpace(const std::string& space_id,
				const WGS84Point& reference_point,
				const std::string& task_type = "global_space");

			bool createDynamicTaskSpace(const std::string& space_id,
				ObjectID reference_entity_id,
				const std::string& task_type = "global_space");

			/**
			 * @brief 添加已创建的TaskSpace到管理器
			 * @param task_space 已创建和初始化的TaskSpace实例
			 * @return 是否添加成功
			 */
			bool addTaskSpace(std::shared_ptr<TaskSpace> task_space);
			
			/**
			 * @brief 从对象配置创建TaskSpace
			 * @param space_id TaskSpace ID
			 * @return 是否删除成功
			*/
			bool removeTaskSpace(const std::string& space_id);
			// 任务空间管理
			void clearAllTaskSpaces();

			/**
			 * @brief 获取任务空间
			 * @param space_id 空间ID，默认为全局任务空间
			 * @return 任务空间共享指针，如果不存在则返回nullptr
			 */
			std::shared_ptr<TaskSpace> getTaskSpace(const std::string& space_id = Constants::GLOBAL_TASK_SPACE_ID);
			/**
			 * @brief 查找使用指定实体的任务空间
			 * @param entity_id 附着实体ID的任务空间
			 * @return 使用该实体的任务空间ID列表
			 */
			std::vector<std::string> findTaskSpacesByEntity(ObjectID entity_id) const;
			bool hasTaskSpace(const std::string& space_id) const;
			std::vector<std::string> getAllTaskSpaceIds() const;
			size_t getActiveTaskSpaceCount() const;

			/**
			 * @brief 更新实体位置（通知所有相关的动态任务空间）
			 * @param entity_id 实体ID
			 * @param new_position 新位置
			 * @return 更新的任务空间数量
			 */
			size_t entitySetOrigin(ObjectID entity_id, const WGS84Point& new_position);

			/**
			 * @brief 查找或创建TaskSpace
			 * @param origin 期望的基准点坐标
			 * @param space_id 空间ID引用，输入期望的ID，输出实际匹配的ID
			 * @return 找到匹配的TaskSpace返回智能指针，否则返回nullptr
			 * @note 此方法只查找已存在的TaskSpace，不创建新的。新TaskSpace应通过Config创建
			 */
			std::shared_ptr<TaskSpace> getOrCreateTaskSpace(const WGS84Point& origin, std::string& space_id);


			bool createTaskSpacesFromObjects(const std::vector<nlohmann::json>& taskspace_objects);

			// === 坐标转换方法 ===

			/**
			 * @brief WGS84坐标转ECEF坐标
			 * @param wgs84_point WGS84坐标点
			 * @return ECEF坐标点
			 */
			EcefPoint wgs84ToECEF(const WGS84Point& wgs84_point) const;

			/**
			 * @brief ECEF坐标转WGS84坐标
			 * @param ecef_point ECEF坐标点
			 * @return WGS84坐标点
			 */
			WGS84Point ecefToWGS84(const EcefPoint& ecef_point) const;

		private:
			std::string generateUniqueSpaceId(const std::string& prefix = "space") const;
			bool isValidSpaceId(const std::string& space_id) const;
			bool createTaskSpaceFromObjectConfig(const std::string& space_id, 
				const std::string& type_key, const nlohmann::json& object_json);
		};

	} // namespace NSEnvironment
} // namespace NSDrones