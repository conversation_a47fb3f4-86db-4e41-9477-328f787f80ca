// include/planning/planning_types.h
#pragma once

#include "core/types.h"
#include "mission/control_point.h"
#include "uav/uav_fwd.h"
#include <vector>
#include <string>
#include <memory>
#include <utility>
#include <stdexcept>
#include <variant>
#include <map>
#include <set>
#include <limits>
#include <optional>

// 前向声明
namespace NSDrones {
	namespace NSUav { class IDynamicModel; }
	namespace NSMission { class PayloadActionCommand; }
}

namespace NSDrones {
	namespace NSPlanning {
		using RouteID = std::string;    
		using namespace NSDrones::NScore;

		/**
		 * @struct RoutePoint
		 * @brief 航线上的单个路径点，包含完整的时空状态信息
		 *
		 * 该结构体定义了无人机在特定时刻的完整状态，包括：
		 * - 空间位置（WGS84坐标系）
		 * - 时间信息
		 * - 运动状态（速度、姿态）
		 * - 载荷动作指令
		 */
		struct RoutePoint {
			// === 基本状态信息 ===
			NSCore::WGS84Point position;                                    ///< WGS84位置坐标
			NSCore::Time time_stamp = 0.0;                                  ///< 时间戳（秒，绝对时间或相对任务开始时间）
			NSCore::Vector3D velocity = NSCore::Vector3D::Zero();           ///< 速度向量（米/秒，NED坐标系）
			NSCore::Orientation orientation = NSCore::Orientation::Identity(); ///< 姿态四元数

			// === 载荷动作信息 ===
			std::vector<NSMission::PayloadActionCommand> payload_actions;   ///< 在此航路点需要执行的载荷动作命令列表

			// === 构造函数 ===
			/** @brief 默认构造函数 */
			RoutePoint() = default;

			/**
			 * @brief 完整参数构造函数
			 * @param pos WGS84位置坐标
			 * @param time 时间戳
			 * @param vel 速度向量
			 * @param att 姿态四元数
			 * @param actions 载荷动作命令列表
			 */
			RoutePoint(const NSCore::WGS84Point& pos,
					   NSCore::Time time = 0.0,
					   const NSCore::Vector3D& vel = NSCore::Vector3D::Zero(),
					   const NSCore::Orientation& att = NSCore::Orientation::Identity(),
					   const std::vector<NSMission::PayloadActionCommand>& actions = {})
				: position(pos), time_stamp(time), velocity(vel), orientation(att), payload_actions(actions) {}
		};

		// === 类型别名 ===
		using RouteSegment = std::vector<RoutePoint>;                      ///< 航段类型（一系列航路点）

		/**
		 * @class PlannedRoute
		 * @brief 特定无人机的完整规划航线
		 *
		 * 该类封装了单个无人机的完整航线信息，包括：
		 * - 无人机标识
		 * - 有序的航路点序列
		 * - 航线统计信息（长度、时间等）
		 * - 航线验证和操作方法
		 */
		class PlannedRoute {
		public:
			// === 构造函数 ===
			/** @brief 默认构造函数（创建无效ID的路径） */
			PlannedRoute() = default;

			/**
			 * @brief 指定无人机ID的构造函数
			 * @param uav_id 关联的无人机ID
			 */
			explicit PlannedRoute(NSCore::ObjectID uav_id);

			// === 基本访问接口 ===
			/** @brief 获取关联的无人机ID */
			const NSCore::ObjectID& getUavId() const;

			/** @brief 获取航路点列表（只读） */
			const RouteSegment& getWaypoints() const;

			/** @brief 获取航路点列表（可修改） */
			RouteSegment& getWaypoints();

			// === 航路点操作接口 ===
			/**
			 * @brief 添加单个航路点
			 * @param point 要添加的航路点
			 * @note 会进行时间戳单调性和位置冗余检查
			 */
			void addWaypoint(const RoutePoint& point);

			/**
			 * @brief 添加航段（多个航路点）
			 * @param segment 要添加的航段
			 * @note 会进行时间戳连续性检查，并可能合并连接点
			 */
			void addWaypoints(const RouteSegment& segment);

			/** @brief 清空所有航路点 */
			void clearWaypoints();

			// === 状态查询接口 ===
			/** @brief 检查航线是否为空 */
			bool isEmpty() const;

			/** @brief 计算航线总长度（米） */
			double getTotalLength() const;

			/** @brief 计算航线总时间（秒） */
			NSCore::Time getTotalTime() const;

			/**
			 * @brief 获取起始点
			 * @return 起始航路点的const引用
			 * @throws DroneException 如果航线为空
			 */
			const RoutePoint& getStartPoint() const;

			/**
			 * @brief 获取结束点
			 * @return 结束航路点的const引用
			 * @throws DroneException 如果航线为空
			 */
			const RoutePoint& getEndPoint() const;

		private:
			// === 内部数据成员 ===
			NSCore::ObjectID uav_id_ = NSUtils::INVALID_OBJECT_ID;          ///< 关联的无人机ID
			RouteSegment waypoints_;                                        ///< 航路点序列
		};

		// === 全局辅助函数 ===

		/**
		 * @brief 对几何路径进行匀速时间参数化
		 *
		 * 将纯几何路径转换为包含时间信息的航段，采用恒定速度模型。
		 * 如果提供了动力学模型，会根据模型限制调整速度。
		 *
		 * @param path_geometry 输入的3D几何路径点（至少包含2个点）
		 * @param dynamics 无人机动力学模型（可选，用于速度限制检查）
		 * @param start_time 起始时间戳
		 * @param start_velocity 起始速度向量
		 * @param desired_speed 期望的恒定速度（m/s）
		 * @param result 输出的时间参数化后的航段
		 * @return 参数化成功且生成有效航段时返回true
		 */
		bool timeParameterizeConstantSpeed(
			const std::vector<NSCore::WGS84Point>& path_geometry,
			const NSUav::IDynamicModel* dynamics,
			NSCore::Time start_time,
			const NSCore::Vector3D& start_velocity,
			double desired_speed,
			RouteSegment& result);

		// === 枚举类型定义 ===

		/**
		 * @enum PlanningStrategyType
		 * @brief 路径规划或任务分配的宏观策略类型
		 *
		 * 定义了规划器可以采用的不同优化目标和策略方向
		 */
		enum class PlanningStrategyType {
			DEFAULT,                ///< 默认策略（通常是最快或最直接）
			MINIMIZE_TIME,          ///< 最小化总时间
			MINIMIZE_ENERGY,        ///< 最小化总能耗
			MINIMIZE_RISK,          ///< 最小化风险（避开高威胁区域）
			MAXIMIZE_COVERAGE,      ///< 最大化覆盖率（勘测任务中）
			CUSTOM                  ///< 用户自定义或其他特定策略
		};

		/**
		 * @enum PlannerType
		 * @brief 规划器组件类型标识
		 *
		 * 用于标识和分类不同类型的规划器组件
		 */
		enum class PlannerType {
			UNKNOWN,                ///< 未知类型
			TASK_ALLOCATOR,         ///< 任务分配器
			PATH_PLANNER,           ///< 路径规划器
			TRAJECTORY_OPTIMIZER,   ///< 轨迹优化器
			MISSION_PLANNER,        ///< 任务规划器
			COVERAGE_PLANNER        ///< 覆盖规划器
		};

		/**
		 * @enum WarningType
		 * @brief 规划过程中产生的警告类型
		 *
		 * 定义了规划模块内部或与其他模块交互时可能产生的各种警告类型，
		 * 用于问题诊断和系统监控
		 */
		enum class WarningType {
			// === 通用警告 ===
			UNKNOWN,                        ///< 未知警告类型
			CRITICAL,                       ///< 严重错误
			INTERNAL_ERROR,                 ///< 内部错误

			// === 规划相关警告 ===
			PLANNING_ERROR,                 ///< 规划错误
			PLANNING_FAILURE,               ///< 规划失败
			OPTIMIZATION_FAILED,            ///< 优化失败
			OPTIMIZATION_FAILURE,           ///< 优化过程失败
			TRAJECTORY_INFEASIBLE,          ///< 轨迹不可行

			// === 分配相关警告 ===
			ALLOCATION_ERROR,               ///< 分配错误
			RESOURCE_UNAVAILABLE,           ///< 资源不可用
			PLANNER_NOT_FOUND,              ///< 找不到规划器

			// === 区域相关警告 ===
			ENTERED_WARNING_ZONE,           ///< 进入警告区域
			LEFT_WARNING_ZONE,              ///< 离开警告区域
			ENTERED_KEEPOUT_ZONE,           ///< 进入禁飞区域
			CROSS_KEEPOUT_BOUNDARY,         ///< 穿越禁飞区边界
			ENTERED_THREAT_ZONE,            ///< 进入威胁区域
			CROSS_THREAT_BOUNDARY,          ///< 穿越威胁区边界

			// === 状态相关警告 ===
			DYNAMICS_VIOLATION,             ///< 动力学约束违反
			PROXIMITY_ALERT,                ///< 接近警报
			INVALID_STATE,                  ///< 无效状态
			ENVIRONMENT_QUERY_FAILURE,      ///< 环境查询失败
			PAYLOAD_ERROR                   ///< 载荷错误
		};

		/**
		 * @struct WarningEvent
		 * @brief 规划过程中产生的警告事件
		 *
		 * 封装了警告事件的完整信息，包括类型、描述、时空信息和关联对象
		 */
		struct WarningEvent {
			// === 基本警告信息 ===
			WarningType wtype = WarningType::UNKNOWN;                       ///< 警告类型
			std::string description = "";                                   ///< 警告的文字描述
			NSCore::Time time_stamp = 0.0;                                 ///< 事件发生时间（近似）
			NSCore::WGS84Point location;                                   ///< 事件发生位置（WGS84坐标）

			// === 关联对象信息 ===
			NSCore::ObjectID related_uav_id = NSUtils::INVALID_OBJECT_ID;  ///< 关联的无人机ID（如果适用）
			NSCore::ObjectID related_zone_id = NSUtils::INVALID_OBJECT_ID; ///< 关联的区域ID（如果适用）
			NSCore::ObjectID related_task_id = NSUtils::INVALID_OBJECT_ID; ///< 关联的任务ID（如果适用）

			// === 构造函数 ===
			/** @brief 默认构造函数 */
			WarningEvent() = default;

			/**
			 * @brief 完整参数构造函数
			 * @param t 警告类型
			 * @param desc 描述信息
			 * @param time 时间戳
			 * @param loc 发生位置
			 * @param uav_id 关联无人机ID
			 * @param zone_id 关联区域ID
			 * @param task_id 关联任务ID
			 */
			WarningEvent(WarningType t,
						 std::string desc,
						 NSCore::Time time = 0.0,
						 const NSCore::WGS84Point& loc = NSCore::WGS84Point(),
						 NSCore::ObjectID uav_id = NSUtils::INVALID_OBJECT_ID,
						 NSCore::ObjectID zone_id = NSUtils::INVALID_OBJECT_ID,
						 NSCore::ObjectID task_id = NSUtils::INVALID_OBJECT_ID)
				: wtype(t), description(std::move(desc)), time_stamp(time), location(loc),
				  related_uav_id(std::move(uav_id)), related_zone_id(std::move(zone_id)), related_task_id(std::move(task_id)) {}
		};

		/**
		 * @enum CalculationFidelityType
		 * @brief 计算精度级别
		 *
		 * 定义了规划计算的精度级别，用于在性能和精度之间进行权衡
		 */
		enum class CalculationFidelityType {
			SIMPLE,                 ///< 简单计算（快速但精度较低）
			DETAILED,               ///< 详细计算（精度高但耗时较长）
			UNKNOWN                 ///< 未知精度级别
		};

		/**
		 * @enum TaskAllocationStrategyType
		 * @brief 任务分配策略类型
		 *
		 * 定义了任务分配器可以使用的不同分配策略
		 */
		enum class TaskAllocationStrategyType {
			CLOSEST_AVAILABLE,      ///< 选择最近的可用无人机
			LEAST_BUSY,             ///< 选择最空闲的无人机
			UNKNOWN                 ///< 未知分配策略
		};

		/**
		 * @enum ScanPatternType
		 * @brief 区域扫描模式类型
		 *
		 * 定义了区域扫描任务可以使用的不同扫描模式
		 */
		enum class ScanPatternType {
			ZIGZAG,                 ///< 之字形扫描
			PARALLEL,               ///< 平行线扫描
			SPIRAL,                 ///< 螺旋扫描
			UNKNOWN                 ///< 未知或未指定扫描模式
		};

	} // namespace NSPlanning
} // namespace NSDrones
